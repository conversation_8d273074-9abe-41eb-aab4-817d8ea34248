package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDispatchDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportDispatchService;
import com.teyuntong.goods.service.service.common.enums.ClientSignEnum;
import com.teyuntong.goods.service.service.common.enums.PublishPlatformEnum;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.user.CsMaintainedCustomRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.user.service.client.custom.dto.CsMaintainedCustomDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 添加个人货主
 *
 * <AUTHOR>
 * @since 2025/02/23 17:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddDispatchOwnerPostHandler {

    private final TransportDispatchService transportDispatchService;
    private final CsMaintainedCustomRemoteService csMaintainedCustomRemoteService;


    public void handler(BasePublishProcessBO processBO) {
        TransportMainDO transportMain = processBO.getTransportMain();

        // 新代调货源不进tyt_transport_dispatch
        if (SourceTypeEnum.isNewDispatch(transportMain.getSourceType())) {
            return;
        }

        // 添加到个人货主
        CsMaintainedCustomDTO customerInfo = csMaintainedCustomRemoteService.getCustomerInfo(transportMain.getUserId());
        Integer clientSign = processBO.getBaseParam().getClientSign();

        if ((customerInfo != null && customerInfo.getGoodsSync() == 1)
                || Objects.equals(SourceTypeEnum.YMM.getCode(), transportMain.getSourceType())) {
            this.saveTransportDispatch(transportMain, customerInfo, clientSign);
        }
    }

    public void saveTransportDispatch(TransportMainDO transport, CsMaintainedCustomDTO csMaintainedCustom, Integer clientSign) {

        BigDecimal decPrice = TransportUtil.hasPrice(transport.getPrice()) ? new BigDecimal(transport.getPrice()) : null;
        Integer publishPlatform = getPublishPlatform(clientSign); // TransportDispatch新增发布终端字段

        TransportDispatchDO transportDispatchDO = transportDispatchService.getBySrcMsgId(transport.getSrcMsgId());
        if (transportDispatchDO == null) {
            transportDispatchDO = new TransportDispatchDO();
            transportDispatchDO.setSrcMsgId(transport.getSrcMsgId());
            transportDispatchDO.setUserId(transport.getUserId());
            transportDispatchDO.setPublishUserId(csMaintainedCustom.getDispatcherId());
            transportDispatchDO.setPublishUserName(csMaintainedCustom.getDispatcherName());
            transportDispatchDO.setDispatcherId(csMaintainedCustom.getDispatcherId());
            transportDispatchDO.setDispatcherName(csMaintainedCustom.getDispatcherName());
            transportDispatchDO.setOwnerFreight(decPrice);
            transportDispatchDO.setCreateTime(new Date());
            transportDispatchDO.setModifyTime(new Date());
            transportDispatchDO.setPublishPlatform(publishPlatform);
            transportDispatchService.save(transportDispatchDO);
        } else {
            transportDispatchDO.setPublishUserId(csMaintainedCustom.getDispatcherId());
            transportDispatchDO.setPublishUserName(csMaintainedCustom.getDispatcherName());
            transportDispatchDO.setDispatcherId(csMaintainedCustom.getDispatcherId());
            transportDispatchDO.setDispatcherName(csMaintainedCustom.getDispatcherName());
            transportDispatchDO.setOwnerFreight(decPrice);
            transportDispatchDO.setModifyTime(new Date());
            transportDispatchDO.setPublishPlatform(publishPlatform);
            transportDispatchService.updateById(transportDispatchDO);
        }
    }

    /**
     * 返回发布终端
     */
    private Integer getPublishPlatform(Integer clientSign) {
        Integer publishPlatform;
        if (ClientSignEnum.ANDROID_GOODS.getCode().equals(clientSign)
                || ClientSignEnum.IOS_GOODS.getCode().equals(clientSign)) {
            publishPlatform = PublishPlatformEnum.APP.getCode();
        } else if (ClientSignEnum.PC.getCode().equals(clientSign)) {
            publishPlatform = PublishPlatformEnum.PC.getCode();
        } else {
            publishPlatform = null;
        }
        return publishPlatform;
    }
}
