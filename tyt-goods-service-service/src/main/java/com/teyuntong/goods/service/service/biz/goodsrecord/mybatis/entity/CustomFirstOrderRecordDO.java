package com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Getter
@Setter
@TableName("tyt_custom_first_order_record")
public class CustomFirstOrderRecordDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户手机号
     */
    private String customPhone;

    /**
     * 首次发货时间
     */
    private Date firstPublishTime;

    /**
     * 首次履约完单时间
     */
    private Date firstFinishOrderTime;

    /**
     * 状态字段
     */
    private Integer status;
}
