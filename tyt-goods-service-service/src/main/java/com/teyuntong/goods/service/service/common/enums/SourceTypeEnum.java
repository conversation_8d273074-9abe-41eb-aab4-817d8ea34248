package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Describe 货源来源枚举类
 * <AUTHOR>
 * @Date 2023/8/15
 */
@Getter
@AllArgsConstructor
public enum SourceTypeEnum {

    NORMAL(1, "普通发货"),
    DISPATCH(2, "调度发货"),
    OWNER(3, "个人货主"),
    YMM(4, "运满满"),
    HONGXIN(5, "宏信"),
    NEW_DISPATCH(6, "新代调");

    private final Integer code;
    private final String name;

    /**
     * 是否代调账户发货
     */
    public static boolean isDispatch(Integer code) {
        return DISPATCH.code.equals(code) || HONGXIN.code.equals(code);
    }

    /**
     * 是否ymm货源
     */
    public static boolean isYmm(Integer code) {
        return YMM.code.equals(code);
    }

    /**
     * 是否新代调货源
     */
    public static boolean isNewDispatch(Integer code) {
        return NEW_DISPATCH.code.equals(code);
    }

}
