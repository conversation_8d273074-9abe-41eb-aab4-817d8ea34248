package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 货源状态枚举
 * 状态 1有效（发布中），0无效（已过期），2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
 *
 * <AUTHOR>
 * @since 2025-02-27 10:03
 */
@Getter
@AllArgsConstructor
public enum GoodsStatusEnum {
    INVALID(0, "无效"),
    PUBLISHING(1, "发布中"),
    DONE(4, "已成交"),
    CANCEL(5, "已取消"),
    CONFIRMING(6, "待确认"),
    ;
    private Integer code;
    private String name;
}
