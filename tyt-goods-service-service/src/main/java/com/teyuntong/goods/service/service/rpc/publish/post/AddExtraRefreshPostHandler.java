package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.refresh.mybatis.entity.GoodsRefreshManualDO;
import com.teyuntong.goods.service.service.biz.refresh.service.GoodsRefreshManualService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.ExtraRefreshItemEnum;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 增加货源额外刷新次数
 *
 * <AUTHOR>
 * @since 2025/02/23 17:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddExtraRefreshPostHandler {

    private final TytConfigRemoteService tytConfigRemoteService;
    private final GoodsRefreshManualService goodsRefreshManualService;

    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO processBO) {

        TransportMainDO newMainDO = processBO.getTransportMain();
        GoodsRefreshManualDO existRecord = goodsRefreshManualService.getBySrcMsgId(newMainDO.getSrcMsgId());

        // 需要刷新的次数
        int refreshTimes = 0;

        // 优先从入参里取刷新次数，如果为0，则手动判断
        if (processBO instanceof DirectPublishProcessBO directPublishBO) {
            refreshTimes = directPublishBO.getDirectPublishBO().getExtraRefreshTimes();
        }

        if (refreshTimes == 0) {
            // 6710：不再增加额外曝光次数
            if (TransportUtil.getClientVersion(processBO.getBaseParam().getClientVersion()) >= 6710) {
                return;
            }
            refreshTimes = getItemValue(newMainDO, existRecord);
        }

        // 保存
        saveExtraRefreshTimes(newMainDO, existRecord, refreshTimes);
    }

    /**
     * 返回需要增加的额外刷新次数
     */
    private int getItemValue(TransportMainDO newMainDO, GoodsRefreshManualDO existRecord) {
        int itemValue = existRecord == null ? 0 : existRecord.getItemValue();
        // 如果运费不为空，且之前没填写运费，则增加
        if (TransportUtil.hasPrice(newMainDO.getPrice()) && !ExtraRefreshItemEnum.check(itemValue, ExtraRefreshItemEnum.FILL_PRICE)) {
            itemValue += ExtraRefreshItemEnum.FILL_PRICE.getBinaryValue();
        }
        // 一口价
        if (Objects.equals(newMainDO.getPublishType(), PublishTypeEnum.FIXED.getCode())
                && !ExtraRefreshItemEnum.check(itemValue, ExtraRefreshItemEnum.TO_FIXED_PRICE)) {
            itemValue += ExtraRefreshItemEnum.TO_FIXED_PRICE.getBinaryValue();
        }
        // 目的地
        if (StringUtils.isNotBlank(newMainDO.getDestDetailAdd())
                && !Objects.equals(newMainDO.getDestDetailAdd(), newMainDO.getDestArea())
                && !ExtraRefreshItemEnum.check(itemValue, ExtraRefreshItemEnum.FILL_DEST_DETAIL)) {
            itemValue += ExtraRefreshItemEnum.FILL_DEST_DETAIL.getBinaryValue();
        }
        if (existRecord != null && existRecord.getItemValue() == itemValue) {
            itemValue = 0;
        }
        return itemValue;
    }

    /**
     * 新增货源额外刷新次数
     */
    private void saveExtraRefreshTimes(TransportMainDO newMainDO, GoodsRefreshManualDO existRecord, int itemValue) {
        if (itemValue == 0) {
            log.info("没有符合刷新的操作，不增加刷新次数");
            return;
        }
        Integer refreshInterval = tytConfigRemoteService.getIntValue("goods_manual_refresh_interval", 10);
        List<ExtraRefreshItemEnum> itemEnums = ExtraRefreshItemEnum.getList(itemValue);
        Integer refreshTimes = itemEnums.stream().map(ExtraRefreshItemEnum::getRefreshTimes).mapToInt(Integer::intValue).sum();
        String remark = newMainDO.getUserShowName() + "，" + itemEnums.stream().map(ExtraRefreshItemEnum::getDesc)
                .collect(Collectors.joining("、")) + "，增加了" + (refreshTimes * refreshInterval) + "分钟曝光";

        if (existRecord == null) {
            GoodsRefreshManualDO refreshManualDO = new GoodsRefreshManualDO();
            refreshManualDO.setSrcMsgId(newMainDO.getSrcMsgId());
            refreshManualDO.setUserId(newMainDO.getUserId());
            refreshManualDO.setRefreshInterval(refreshInterval);
            refreshManualDO.setTotalTimes(refreshTimes);
            refreshManualDO.setRefreshTimes(0);
            refreshManualDO.setLeftTimes(refreshTimes);
            refreshManualDO.setItemValue(itemValue);
            refreshManualDO.setRemark(remark);
            refreshManualDO.setCreateTime(new Date());
            goodsRefreshManualService.save(refreshManualDO);
        } else {
            GoodsRefreshManualDO refreshManualDO = new GoodsRefreshManualDO();
            refreshManualDO.setId(existRecord.getId());
            refreshManualDO.setTotalTimes(refreshTimes);
            refreshManualDO.setLeftTimes(refreshTimes - existRecord.getRefreshTimes());
            refreshManualDO.setItemValue(itemValue);
            refreshManualDO.setRemark(remark);
            refreshManualDO.setModifyTime(new Date());
            goodsRefreshManualService.updateById(refreshManualDO);
        }
    }

}
