package com.teyuntong.goods.service.service.rpc.publish.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发布操作类型
 *
 * <AUTHOR>
 * @since 2025/02/11 10:13
 */
@Getter
@AllArgsConstructor
public enum BackoutReasonEnum {
    DIRECT_PUBLISH("直接发布撤销", 0),
    DEAL_OFFLINE("线下找到车", 1),
    CARGO_CANCEL("货不走了", 2),
    REPUBLISH("重新发布", 3),
    DEAL_ONLINE("已线上成交", 4),
    NO_CAR("没找到车", 5),
    OTHER_REASON("其他原因", 6),
    SHIPPER_CANCEL("货主已取消货源", 7),
    ADD_PRICE("运费加价撤销", 7),
    TRANSFER_FIXED("转一口价撤销", 8),
    TRANSFER_TELE("转电议撤销", 9),
    REFRESH("刷新撤销", 11),
    CHANGE_PUBLISH_METHOD("变更发货方式撤销", 12),
    RESEND("撤销并重发", 13),
    CANCEL_DISPATCH_AUTH("新代调取消找车", 14),
    MATCH_CAR("匹配到车", 100),
    OFF("已下架", 100),
    NOT_TRANS("未满足货源同步要求", 100),
    USER_NO_AUTH("用户未授权", 100),
    TIMEOUT("超时下架", 100),

    ;
    private final String key;
    private final Integer value;
}
