package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CallPhoneRecordDO {

    private Long id;

    private Long srcMsgId;

    private Long carUserId;

    private String carUserName;

    private Integer carIsVip;

    private String path;

    private String module;

    private Date createTime;

    private String platId;

    /**
     * 货源来源（1货主；2调度客服；3个人货主;4:运满满货源；5：宏信货源;6:新代调货源）
     */
    private Integer sourceType;

}