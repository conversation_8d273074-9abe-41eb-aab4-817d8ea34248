package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.AppCallLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 * 记录APP拨打电话的信息（电话标注）  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Mapper
public interface AppCallLogMapper extends BaseMapper<AppCallLogDO> {

    /**
     * 查询一段时间内用户的拨打次数
     */
    int getCallCountOfPeriod(@Param("userId") Long userId,
                             @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询某货源的拨打次数，userId可为空
     */
    int getCallCountOfGoods(@Param("srcMsgId") Long srcMsgId, @Param("userId") Long userId);

    /**
     * 获取N次拨打之后的最早拨打时间
     */
    Date getLastCallTimeOffset(@Param("srcMsgId") Long srcMsgId, @Param("offset") Integer offset);

    int getViewLogCountBySrcMsgId(@Param("srcMsgId") Long srcMsgId);
}
