package com.teyuntong.goods.service.service.biz.specialcar.service;


import com.teyuntong.goods.service.client.specialcar.dto.SpecialCarAutoLabelDTO;
import com.teyuntong.goods.service.client.specialcar.dto.SpecialCarBILabelDTO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarDO;

import java.util.List;

/**
 * <p>
 * 专车准入准出 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-06-27
 */
public interface SpecialCarService {

    void saveSpecialCar(SpecialCarDO specialCarDO);

    void updateSpecialCar(SpecialCarDO specialCarDO);

    List<SpecialCarDO> getSpecialCarByUserId(Long userId ,Long driverId,Long driverUserId);

    SpecialCarDO getSpecialCarById(Long id);

    void autoLabel(SpecialCarAutoLabelDTO specialCarAutoLabelDTO);

    void biLabel(SpecialCarBILabelDTO specialCarAutoLabelDTO);

    void autoLabelDelete(SpecialCarAutoLabelDTO specialCarAutoLabelDTO);

    void allCityLabelDelete();

    void gpsCheatDelete();

}
