package com.teyuntong.goods.service.service.biz.specialcar.service.impl;

import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarDispatchDetailDO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SpecialCarDispatchDetailMapper;
import com.teyuntong.goods.service.service.biz.specialcar.service.SpecialCarDispatchDetailService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 专车派单详情表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SpecialCarDispatchDetailServiceImpl implements SpecialCarDispatchDetailService {
    private final SpecialCarDispatchDetailMapper specialCarDispatchDetailMapper;

    @Override
    public void saveSpecialCarDispatchDetail(SpecialCarDispatchDetailDO specialCarDispatchDetail) {
        specialCarDispatchDetailMapper.insert(specialCarDispatchDetail);
    }

    @Override
    public Integer selectCountByUserAndGoodsId(Long goodsId, Long userId) {
        return specialCarDispatchDetailMapper.selectCountByUserAndGoodsId(goodsId, userId);
    }
}
