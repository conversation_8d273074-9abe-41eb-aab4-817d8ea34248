package com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货源曝光记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-23
 */
@Getter
@Setter
@TableName("exposure_permission_used_record")
public class ExposurePermissionUsedRecordDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 出发地(省市区以减号-分割开)
     */
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    private String destPoint;

    /**
     * 货物内容
     */
    private String taskContent;

    /**
     * 使用时间
     */
    private Date ctime;
}
