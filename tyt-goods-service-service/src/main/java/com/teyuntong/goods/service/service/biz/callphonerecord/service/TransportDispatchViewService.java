package com.teyuntong.goods.service.service.biz.callphonerecord.service;

import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;

import java.util.List;

/**
 * 货源查看、联系统计表 service 接口
 *
 * <AUTHOR>
 * @since 2024/12/02 13:55
 */
public interface TransportDispatchViewService {

    /**
     * 货源某个货源的所有拨打查看记录
     */
    List<TransportDispatchViewDO> getBySrcMsgId(Long srcMsgId);

    /**
     * 根据srcMsgId获取联系次数和查看次数
     */
    List<TransportDispatchViewDO> getContactAndViewCount(List<Long> srcMsgIds);

    /**
     * 根据srcMsgId获取联系次数和查看次数
     */
    TransportDispatchViewDO getContactAndViewCount(Long srcMsgId);

    Integer hasContactInSrcMsgIds(List<Long> srcMsgIds);

    /**
     * 保存货源查看或联系记录
     *
     * @param user      车主信息
     * @param srcMsgId  货源ID
     * @param type      查看类型 1：查看  2：联系
     */
    void addTransportView(UserRpcVO user, Long srcMsgId, Integer type);
}
