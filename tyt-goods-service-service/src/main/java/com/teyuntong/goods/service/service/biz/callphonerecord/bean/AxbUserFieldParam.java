package com.teyuntong.goods.service.service.biz.callphonerecord.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AxbUserFieldParam implements Serializable {

    /**
     * 货源ID
     */
    private Long SrcMsgId;

    /**
     * 货方ID
     */
    private Long transportUserId;

    /**
     * 车方ID
     */
    private Long carUserId;

    /**
     * 运费
     */
    private String price;

    /**
     * 信息费
     */
    private String infoFee;

    /**
     *  技术服务费
     */
    private String tecServiceFee;

    /**
     * 订金是否退还（0不退还；1退还）
     */
    private String refundFlag;

    /**
     * 货源/业务类型
     */
    private List<String> transportType;

    /**
     * 运距
     */
    private String distance;

    /**
     * 出发地(省市区以减号-分割开)
     */
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    private String destPoint;

}
