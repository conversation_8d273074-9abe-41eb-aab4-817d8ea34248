package com.teyuntong.goods.service.service.common.utils;

import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * tyt格式化装卸货时间方法
 *
 * <AUTHOR>
 * @since 2025/09/18 15:12
 */
public class TytFormateLoadTimeUtil {

    /**
     * 格式化装卸货时间
     */
    public static String dealLoadAndUnloadTime(Date beginLoadingTime, Date loadTime, Date beginUnloadTime, Date unloadTime) {
        if (beginLoadingTime == null && loadTime == null && beginUnloadTime == null && unloadTime == null) {
            return "今明两日 随到随装";
        }

        StringBuilder sb = new StringBuilder();

        // 装货时间
        String starTime = "";
        if (beginLoadingTime != null || loadTime != null) {
            starTime = getPeriodTime(beginLoadingTime, loadTime);
        }
        if (StringUtils.isNotBlank(starTime)) {
            sb.append(starTime);
            sb.append(" 装货");
        } else {
            sb.append("今明两日 随到随装");
        }

        // 卸货时间
        String endTime = "";
        if (beginUnloadTime != null || unloadTime != null) {
            endTime = getPeriodTime(beginUnloadTime, unloadTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            sb.append(" - ");
            sb.append(endTime);
            sb.append(" 卸货");
        }
        return sb.toString();
    }

    /**
     * 格式化 时间
     * 因为24整点关系， -1秒
     */
    private static String getPeriodTime(Date beginDate, Date endDate) {
        if (endDate == null) {
            return null;
        }

        long beginTime = beginDate != null ? beginDate.getTime() : endDate.getTime();
        long endTime = endDate.getTime();
        long startOfDayTime = DateUtil.beginOfDay(new Date(beginTime)).getTime();
        long hour = 1000 * 60 * 60;
        long tt = (endTime - beginTime) / (hour);
        if (beginTime == endTime) {
            //如果一样，且为零点，那就当24点处理
            if (beginTime == startOfDayTime) {
                return getCargoPubData(beginTime - 1) + " 晚上24点";
            }
            long l = (beginTime + 1 - startOfDayTime) / hour;
            if (l == 24L) {
                return getCargoPubData(beginTime) + " 晚上24点";
            }
        }
        if (tt >= 23) {
            String cargoPubData = getCargoPubData(beginTime);
            return cargoPubData + " 全天00-24点";
        } else {
            StringBuilder stringBuffer = new StringBuilder();
            //day 因为零点整的关系，-1秒进行计算
            String showDayText = getCargoPubData(beginTime);
            stringBuffer.append(showDayText);

            //时间
            String cargoStartHour = getCargoPubHour(beginTime);
            String cargoEndHour = getCargoPubHour(endTime);
            String title;
            long lcLong = startOfDayTime + hour * 6;
            long swLong = startOfDayTime + hour * 12;
            long xwLong = startOfDayTime + hour * 18;
            if (endTime <= lcLong) {
                title = " 凌晨";
            } else if (endTime <= swLong) {
                title = " 上午";
            } else if (endTime <= xwLong) {
                title = " 下午";
            } else {
                title = " 晚上";
                cargoEndHour = "24";
            }

            //time
            if (endTime > 0 && beginTime != endTime) {
                stringBuffer.append(title);
                if (cargoStartHour.contains(":00")) {
                    // stringBuffer.append("");
                    stringBuffer.append(StringUtils.substringBefore(cargoStartHour, ":"));
                    stringBuffer.append("-");
                    stringBuffer.append(StringUtils.substringBefore(cargoEndHour, ":"));
                    stringBuffer.append("点");
                } else {
                    stringBuffer.append(" ");
                    stringBuffer.append(cargoStartHour);
                    stringBuffer.append("-");
                    stringBuffer.append(cargoEndHour);
                }
            } else {
                stringBuffer.append(title);
                if (cargoStartHour.contains(":00")) {
                    // stringBuffer.append("");
                    stringBuffer.append(StringUtils.substringBefore(cargoStartHour, ":"));
                    stringBuffer.append("点");
                } else {
                    stringBuffer.append(" ");
                    stringBuffer.append(cargoStartHour);
                }
            }
            return stringBuffer.toString();
        }
    }

    /**
     * 返回特运通发货日期
     */
    private static String getCargoPubData(long time) {
        Date date = new Date(time);
        long day = DateUtil.betweenDay(date, new Date(), true);

        if (day == 0L) {
            return "今天 ";
        } else if (day == 1L) {
            return "明天 (" + DateUtil.format(date, "MM/dd") + ")";
        } else if (day == 2L) {
            return "后天 (" + DateUtil.format(date, "MM/dd") + ")";
        } else {
            return DateUtil.format(date, "MM/dd");
        }
    }

    /**
     * 发货时间
     */
    private static String getCargoPubHour(long lastTime) {
        return DateUtil.format(new Date(lastTime), "HH:mm");
    }

    public static void main(String[] args) {
        System.out.println(dealLoadAndUnloadTime(null, null, null, null));
        System.out.println(dealLoadAndUnloadTime(new Date(1758211200000L), new Date(1758297600000L), new Date(1758384000000L), new Date(1758405600000L)));
        System.out.println(dealLoadAndUnloadTime(null, null, null, new Date(1758301200000L)));
    }
}
