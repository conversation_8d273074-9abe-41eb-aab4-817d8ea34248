package com.teyuntong.goods.service.service.biz.specialcar.service;


import com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarDTO;
import com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarInfoDTO;
import com.teyuntong.goods.service.service.biz.specialcar.dto.SigningCarUserVO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SigningCarDO;
import com.teyuntong.user.service.client.car.vo.CarRpcVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 宏信签约车辆 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-20
 */
public interface SigningCarService {

    List<SigningCarDTO> getSigningCarList(List<SigningCarDTO> cars);

    List<SigningCarInfoDTO> getAssignableCarsByRoute(String startCity, String destCity);

    List<SigningCarInfoDTO> getAssignableCarsByCity(List<String> cities);

    List<SigningCarInfoDTO> getAssignableCarsByProvince(List<String> provinces);

    List<SigningCarInfoDTO> getAssignableCarsByCountry(int start, int pageSize);

    SigningCarDO getByCellPhone(String cellPhone);

    void updateCooperNum(Long signingId);

    void updateCarStatusByUserId(Long userId, Integer status);

    boolean getSigningCarBlack(Long userId);

    boolean checkCarDriver(Long userId, Long id, Long carId, Long driverId);


    List<CarRpcVO> getSigningCarList(Long userId);


    SigningCarDO getByUserId(Long userId);

    List<SigningCarUserVO> getSigningId(Long id);

    void updateNumId(Long id);

    void updateSigningNUm(Long id, String phone);

}
