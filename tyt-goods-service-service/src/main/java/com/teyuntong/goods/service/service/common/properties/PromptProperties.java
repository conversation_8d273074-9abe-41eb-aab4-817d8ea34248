package com.teyuntong.goods.service.service.common.properties;

import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 货源诊断提示文案配置
 *
 * <AUTHOR>
 * @since 2024/12/12 16:55
 */
@Setter
// @Getter
@Component
@ConfigurationProperties(prefix = "tyt.prompt")
public class PromptProperties {

    // 货源诊断文案
    private Map<Integer, Map<String, String>> goodsDiagnosis;
    // 弹窗出价文案
    private Map<String, String> popupPriceBox;
    // 挽留弹窗文案
    private Map<String, String> showRetentionBox;
    // 发布弹窗出价文案
    private Map<String, String> publishPopupPriceBox;

    /**
     * 返回货源诊断文案
     *
     * @param type 0模块标题，1去填价，2加价，3转一口价，4补全货物信息，5调整装货时间
     * @param key  属性key
     */
    public String getDiagnosisPrompt(int type, String key) {
        Map<String, String> map = goodsDiagnosis.get(type);
        return map == null ? "" : map.get(key);
    }

    /**
     * 返回弹窗出价文案
     *
     * @param priority 优先级
     * @param x        X价格
     * @param y        Y相似货源数
     */
    public String getPriceBoxPrompt(int priority, Integer x, Integer y) {
        String prompt = popupPriceBox.get("prompt-" + priority);
        if (x != null) {
            prompt = prompt.replace("X", x.toString());
        }
        if (y != null) {
            prompt = prompt.replace("Y", y.toString());
        }
        return prompt;
    }

    /**
     * 返回挽留弹窗文案
     *
     * @param hasPrice true有价 false无价
     * @param x        参数，如果为空返回默认文案
     */
    public String getRetentionPrompt(int type, boolean hasPrice, Integer x) {
        String key = (type == 1 ? "title" : "task") + (hasPrice ? "-price" : "-priceless") + (x == null ? "" : "-x");
        String title = showRetentionBox.get(key);
        if (x != null) {
            title = title.replace("X", x.toString());
        }
        return title;
    }

    /**
     * 获取撤销并重发的挽留弹窗文案
     *
     * @return
     */
    public String getRetentionRepublish() {
        String key = "task-republish";
        return showRetentionBox.get(key);
    }

    public String getOnlineEdit() {
        String key = "task-onlineEdit";
        return showRetentionBox.get(key);
    }

    /**
     * 发布弹窗出价文案
     *
     * @param type 类型
     * @param x        第一个参数
     * @param y        第二个参数
     */
    public String getPublishPriceBoxPrompt(String type, String x, String y) {
        String text = publishPopupPriceBox.get(type);
        if (x != null) {
            text = text.replace("X", x);
        }
        if (y != null) {
            text = text.replace("Y", y);
        }
        return text;
    }
}
