package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 记录APP拨打电话的信息（电话标注） 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Getter
@Setter
@TableName("tyt_app_call_log")
public class AppCallLogDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 电话日志所在的模块，暂定 1:货物（如果有其他由此需求需要在这里进行添加说明）
     */
    private Integer callModule;

    /**
     * 所属车辆
     */
    private String fromCar;

    /**
     * 所属车辆
     */
    private String carId;

    /**
     * 拨打电话的时间
     */
    private Date callTime;

    /**
     * 拨打电话用户的ID
     */
    private Long callerId;

    /**
     * 用户拨打电话咨询的信息的id(如果是货物模块，则就是货物id)
     */
    private Long calledInfoId;

    /**
     * 电话咨询信息的结果，货物模块定义如下值：
            1：达成交易 2：需要再沟通 3：价格没谈妥 4：电话打不通 5：虚假交易 6：已经拉走 (具体值通过tyt_source查询，group_code值为app_call_result_code)
     */
    private Integer callResultCode;

    /**
     * 电话标注
     */
    private String callResultName;

    /**
     * 标记货主code，多选以逗号分割(具体值通过tyt_source查询，group_code值为app_call_result_code)
     */
    private String markerOwnerCodes;

    /**
     * 标记货主具体信息
     */
    private String markerOwnerNames;

    /**
     * 标记货主code，多选以逗号分割(具体值通过tyt_source查询，group_code值为app_call_result_code)
     */
    private String markerOwnerCode;

    /**
     * 标记货主具体信息
     */
    private String markerOwnerName;

    /**
     * 发布用户ID
     */
    private Long pubUserId;

    /**
     * 原货物信息ID
     */
    private Long srcMsgId;

    /**
     * 拨打备注
     */
    private String reference;

    /**
     * 客户端标识
     */
    private String platId;

    /**
     * 首次拨打时间
     */
    private Date createTime;

}
