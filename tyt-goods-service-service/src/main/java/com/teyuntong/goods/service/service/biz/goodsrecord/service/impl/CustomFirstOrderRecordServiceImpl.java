package com.teyuntong.goods.service.service.biz.goodsrecord.service.impl;

import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.mapper.CustomFirstOrderRecordMapper;
import com.teyuntong.goods.service.service.biz.goodsrecord.service.CustomFirstOrderRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CustomFirstOrderRecordServiceImpl implements CustomFirstOrderRecordService {

    private final CustomFirstOrderRecordMapper customFirstOrderRecordMapper;

    @Override
    public int countFinishOrder(String cellPhone) {
        return customFirstOrderRecordMapper.countFinishOrder(cellPhone);
    }

}
