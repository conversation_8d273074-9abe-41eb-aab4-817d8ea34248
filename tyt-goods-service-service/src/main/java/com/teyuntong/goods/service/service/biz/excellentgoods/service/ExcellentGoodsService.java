package com.teyuntong.goods.service.service.biz.excellentgoods.service;

import com.teyuntong.goods.service.client.excellentgoods.vo.TytExcellentGoodsCardUserDetailCanUseCountVO;
import com.teyuntong.goods.service.client.excellentgoods.vo.TytExcellentGoodsCardUserDetailVO;

import java.util.List;

public interface ExcellentGoodsService {

    List<TytExcellentGoodsCardUserDetailVO> getAllNoUseCarListByUserId(Long userId, Integer pageNum);

    TytExcellentGoodsCardUserDetailCanUseCountVO getAllCanUseCarCountNumByUserId(Long userId);

    List<TytExcellentGoodsCardUserDetailVO> getAllCanUseCarListByUserId(Long userId, Integer pageNum);

}
