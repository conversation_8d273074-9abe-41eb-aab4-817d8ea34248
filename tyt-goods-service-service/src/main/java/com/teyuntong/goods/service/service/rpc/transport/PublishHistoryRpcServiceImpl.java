package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.service.PublishHistoryRpcService;
import com.teyuntong.goods.service.client.transport.vo.PublishTaskContentHistoryVO;
import com.teyuntong.goods.service.client.transport.vo.PublishLocationHistoryVO;
import com.teyuntong.goods.service.service.biz.transport.service.PublishTransportDataSnapService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 发货历史记录RPC服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class PublishHistoryRpcServiceImpl implements PublishHistoryRpcService {

    private final PublishTransportDataSnapService publishTransportDataSnapService;

    @Override
    public List<PublishLocationHistoryVO> getStartLocationHistory(Long userId) {
        log.info("RPC调用：查询用户出发地历史记录，userId: {}", userId);
        
        if (userId == null) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        
        return publishTransportDataSnapService.getStartLocationHistory(userId);
    }

    @Override
    public List<PublishLocationHistoryVO> getDestLocationHistory(Long userId) {
        log.info("RPC调用：查询用户目的地历史记录，userId: {}", userId);
        
        if (userId == null) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        
        return publishTransportDataSnapService.getDestLocationHistory(userId);
    }

    @Override
    public List<PublishTaskContentHistoryVO> getTaskContentHistory(Long userId) {
        log.info("RPC调用：查询用户货物历史记录，userId: {}", userId);
        
        if (userId == null) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        
        return publishTransportDataSnapService.getTaskContentHistory(userId);
    }

    @Override
    public void delete(Long id, Integer type) {
        publishTransportDataSnapService.delete(id, type);
    }
}
