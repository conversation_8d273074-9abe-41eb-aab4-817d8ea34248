package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.SameTransportPushService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 首次由非相似货源变为相似货源push
 *
 * <AUTHOR>
 * @since 2025/07/17 18:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SameTransportPushPostHandler {

    private final SameTransportPushService sameTransportPushService;

    @Async("threadPoolExecutor")
    public void handler(TransportMainDO transportMain) {
        // 发布货源并且不是自动发布，判断是否是首次由非相似货源变为相似货源，如果是则发送短信push站内信
        try {
            if (transportMain.getStatus() == 1) {
                sameTransportPushService.sameTransportPush(transportMain);
            }
        } catch (Exception e) {
            log.info("首次由非相似货源变为相似货源push 逻辑处理异常 ", e);
        }
    }
}
