package com.teyuntong.goods.service.service.biz.specialcar.service.impl;

import com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SigningDriverBlackMapper;
import com.teyuntong.goods.service.service.biz.specialcar.service.SigningDriverBlackService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 专车拉黑解除表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SigningDriverBlackServiceImpl implements SigningDriverBlackService {
    private final SigningDriverBlackMapper signingDriverBlackMapper;

    @Override
    public int checkDriverBlackStatus(Long driverUserId) {
        return signingDriverBlackMapper.checkDriverBlackStatus(driverUserId);
    }
}
