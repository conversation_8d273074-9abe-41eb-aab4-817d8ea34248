package com.teyuntong.goods.service.service.rpc.publish.post;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportDispatchViewService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.message.bean.MessagePushBase;
import com.teyuntong.infra.common.message.bean.NewsMessagePush;
import com.teyuntong.infra.common.message.bean.NotifyMessagePush;
import com.teyuntong.infra.common.message.enums.NativePageEnum;
import com.teyuntong.infra.common.message.enums.NotifyOpenTypeEnum;
import com.teyuntong.infra.common.message.service.MessageCenterService;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 加价操作后置处理
 *
 * <AUTHOR>
 * @since 2025/03/07 18:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddPricePostHandler {

    private final RedisUtil redisUtil;
    private final TransportDispatchViewService transportDispatchViewService;
    private final TytConfigRemoteService configRemoteService;
    private final MessageCenterService messageCenterService;

    @Async("threadPoolExecutor")
    public void handler(DirectPublishProcessBO processBO) {
        if (PublishOptEnum.ADD_PRICE.equals(processBO.getOptEnum())) {

            Long srcMsgId = processBO.getDirectPublishBO().getSrcMsgId();
            // 更新加价次数
            String addPriceCountKey = ConfigKeyConstant.TRANSPORT_ADD_PRICE_TIMES + srcMsgId;
            redisUtil.increment(addPriceCountKey, 1);
            redisUtil.expire(addPriceCountKey, Duration.ofDays(1));

            // 保存10分钟加价间隔
            String addPriceTimeKey = ConfigKeyConstant.TRANSPORT_ADD_PRICE_INTERVAL + srcMsgId;
            redisUtil.set(addPriceTimeKey, srcMsgId, Duration.ofMinutes(10));

            // 发送加价mq
            // MqTransportAddMoneyMsg addMoneyMsg = new MqTransportAddMoneyMsg();
            // addMoneyMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
            // addMoneyMsg.setMessageType(MqBaseMessageConstant.MESSAGETYPE_ADDMONEY_TRANSPORT_MESSAGE);
            // addMoneyMsg.setSrcMsgId(srcMsgId);
            // BigDecimal beforePrice = new BigDecimal(processBO.getOldMain().getPrice());
            // BigDecimal afterPrice = new BigDecimal(processBO.getTransportMain().getPrice());
            // addMoneyMsg.setAddPrice(afterPrice.subtract(beforePrice));
            // tytMqMessageService.sendMqMessage(addMoneyMsg, addMoneyMsg.getMessageSerailNum(), addMoneyMsg.getMessageType(), 0L);

            // 加价后push推送车主
            try {
                BigDecimal beforePrice = new BigDecimal(processBO.getOldMain().getPrice());
                BigDecimal afterPrice = new BigDecimal(processBO.getTransportMain().getPrice());
                BigDecimal addPrice = afterPrice.subtract(beforePrice);
                sendAddPricePush(processBO, addPrice);
            } catch (Exception e) {
                log.error("加价PUSH异常：", e);
            }
        }
    }

    /**
     * 给查看过、拨打过的车主推送加价PUSH
     */
    private void sendAddPricePush(DirectPublishProcessBO processBO, BigDecimal addPrice) {
        // 校验是否在推送时间段内 7:00~22:00
        int curHour = DateUtil.hour(new Date(), true);
        if (curHour < 7 || curHour >= 22) {
            return;
        }

        // 查询查看过、拨打过的车主
        Long srcMsgId = processBO.getDirectPublishBO().getSrcMsgId();
        List<TransportDispatchViewDO> dispatchViewList = transportDispatchViewService.getBySrcMsgId(srcMsgId);
        if (CollectionUtils.isEmpty(dispatchViewList)) {
            return;
        }

        TransportMainDO mainDO = processBO.getTransportMain();
        Long tsId = processBO.getTransport().getId();

        int pushMaxCount = configRemoteService.getIntValue(ConfigKeyConstant.MARKUP_PUSH_COUNT, 10);
        for (TransportDispatchViewDO dispatchViewDO : dispatchViewList) {
            Long carUserId = dispatchViewDO.getCarUserId();
            // 当前货源推送用户的次数，格式：3,2,1。3为总PUSH次数，2为拨打过PUSH次数，1为查看过PUSH次数
            String cacheKey = RedisKeyConstant.GOODS_ADD_PRICE_PUSH + srcMsgId + ":" + carUserId;
            String cacheValue = redisUtil.getString(cacheKey);
            log.info("货源加价push: 货源已推送次数：{}，srcMsgId:{}，carUserId:{}", cacheValue, srcMsgId, carUserId);

            int[] pushTimes = cacheValue == null ? new int[3]
                    : Arrays.stream(cacheValue.split(",")).mapToInt(Integer::parseInt).toArray();

            // 总push次数最多不能超过10次
            if (pushTimes[0] >= pushMaxCount) {
                continue;
            }

            int contactCount = dispatchViewDO.getContactCount() == null ? 0 : dispatchViewDO.getContactCount();
            int viewCount = dispatchViewDO.getViewCount() == null ? 0 : dispatchViewDO.getViewCount();
            // 先判断是否拨打过，没拨打过再判断是否查看过
            if (contactCount != 0) {
                if (pushTimes[1] < 3) { // 拨打过最多push3次
                    pushMessage(mainDO, addPrice, carUserId, "拨打", tsId);
                }
            } else if (viewCount != 0) {
                if (pushTimes[2] < 1) { // 查看过最多push1次
                    pushMessage(mainDO, addPrice, carUserId, "查看", tsId);
                }
            }
        }
    }

    private void pushMessage(TransportMainDO mainDO, BigDecimal addPrice, Long pushUserId, String type, Long tsId) {
        TransportLabelJson labelJson = TransportUtil.getLabelJson(mainDO);
        // 优车运价货源单独样式
        if (Objects.equals(labelJson.getGoodCarPriceTransport(), 1)) {
            String content = "您好，您之前%s的%s到%s的货源，涨价了%d元,快来看看吧！".formatted(type, mainDO.getStartProvinc(), mainDO.getDestProvinc(), addPrice.intValue());
            String linkUrl = configRemoteService.getStringValue(ConfigKeyConstant.TYT_PUBLIC_HOST) + "/jump.html?t=c&jp=cgd&id=" + mainDO.getSrcMsgId();
            String title = "货源加价提醒";

            NotifyMessagePush notifyMessage = new NotifyMessagePush();
            notifyMessage.setPushCode("goods_add_price_push");
            notifyMessage.setTitle(title);
            notifyMessage.setContent(content + linkUrl);
            notifyMessage.setRemarks(title);
            notifyMessage.setLinkUrl(linkUrl);
            notifyMessage.setCarPush((short) 1);
            notifyMessage.addUserId(pushUserId);

            JSONObject extraJson = new JSONObject();
            extraJson.put("srcMsgId", mainDO.getSrcMsgId());
            extraJson.put("price", mainDO.getPrice());
            extraJson.put("addPrice", addPrice);
            extraJson.put("title", title);
            extraJson.put("content", String.format("%s%s - %s%s 加价%d元",
                    mainDO.getStartCity().replaceAll("市", ""), mainDO.getStartArea().replaceAll("区", ""),
                    mainDO.getDestCity().replaceAll("市", ""), mainDO.getDestArea().replaceAll("区", ""),
                    addPrice.intValue()));
            extraJson.put("infoText", String.format("%s%s %s吨 %s米",
                    mainDO.getType(), mainDO.getGoodTypeName(), mainDO.getWeight(),
                    Stream.of(mainDO.getLength(), mainDO.getWide(), mainDO.getHigh()).filter(StringUtils::isNotBlank).collect(Collectors.joining("*"))));
            notifyMessage.setExtraData(extraJson.toString());

            messageCenterService.sendMultiMessage(null, null, notifyMessage);
        } else {
            String title = "您%s过的货源涨价了>>".formatted(type);
            String content = "您好，您之前%s的%s到%s的货源，涨价了%s元，快来看看吧!".formatted(type, mainDO.getStartPoint(), mainDO.getDestPoint(), addPrice);

            MessagePushBase messagePushBase = new MessagePushBase();
            messagePushBase.addUserId(pushUserId);
            messagePushBase.setTitle(title);
            messagePushBase.setContent(content);
            messagePushBase.setRemarks(title);
            messagePushBase.setPushType(1, 0);

            // 改版字段
            NewsMessagePush newsMessage = NewsMessagePush.createByPushBase(messagePushBase);
            newsMessage.setDetails(0);
            newsMessage.setSummary(content);
            newsMessage.setNewDesign(1);
            newsMessage.setEnterDetail(1);
            newsMessage.setJumpPage(1);
            newsMessage.setJumpButtonText("查看详情");
            newsMessage.setJumpPageUrl(String.format("callAppGoodsDetail?srcMsgId=%d&tsId=%d", mainDO.getSrcMsgId(), tsId));

            //通知
            NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
            notifyMessage.setPushCode("goods_add_price_push");
            // 设置打开方式为1打开链接 0打开应用
            notifyMessage.setOpenType(NotifyOpenTypeEnum.link.getCode());
            notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
            notifyMessage.addNativeParameter("id", mainDO.getSrcMsgId().toString());

            messageCenterService.sendMultiMessage(null, newsMessage, notifyMessage);
        }

    }
}
