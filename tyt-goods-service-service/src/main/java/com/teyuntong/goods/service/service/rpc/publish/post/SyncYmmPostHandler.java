package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportSyncYmmService;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 同步ymm货源
 *
 * <AUTHOR>
 * @since 2025/04/11 10:11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncYmmPostHandler {

    private final TransportSyncYmmService transportSyncYmmService;

    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO processBO) {
        TransportMainDO transportMain = processBO.getTransportMain();

        // 如果是秒抢货源，且不是YMM货源，则通知YMM下架货源
        if (Objects.equals(processBO.getMainExtend().getSeckillGoods(), 1)
                && !SourceTypeEnum.isYmm(transportMain.getSourceType())) {
            transportSyncYmmService.noticeYmmBackoutTransport(transportMain.getSrcMsgId());
        } else {
            // 加价填价转一口价转电议需要同步ymm编辑货源
            if (PublishOptEnum.isEdit(processBO.getOptEnum())) {
                transportSyncYmmService.noticeYmmEditTransport(transportMain,
                        processBO.getOptEnum().equals(PublishOptEnum.ADD_PRICE));
            }
        }
    }
}
