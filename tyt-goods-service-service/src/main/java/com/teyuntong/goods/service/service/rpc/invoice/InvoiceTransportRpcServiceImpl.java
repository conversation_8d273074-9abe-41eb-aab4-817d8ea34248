package com.teyuntong.goods.service.service.rpc.invoice;

import com.teyuntong.goods.service.client.invoice.service.InvoiceTransportRpcService;
import com.teyuntong.goods.service.client.invoice.vo.HbwjInvoiceTransportOverrunCheckReq;
import com.teyuntong.goods.service.client.invoice.vo.PublishTransportInvoiceVo;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceEnterpriseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class InvoiceTransportRpcServiceImpl implements InvoiceTransportRpcService {

    private final InvoiceEnterpriseService invoiceEnterpriseService;

    @Override
    public Boolean hbwjInvoiceTransportOverrunCheck(HbwjInvoiceTransportOverrunCheckReq overrunCheckReq) {
        return invoiceEnterpriseService.hbwjInvoiceTransportOverrunCheck(overrunCheckReq);
    }

    @Override
    public PublishTransportInvoiceVo getPublishTransportInvoice() {
        return invoiceEnterpriseService.getPublishTransportInvoice();
    }

}
