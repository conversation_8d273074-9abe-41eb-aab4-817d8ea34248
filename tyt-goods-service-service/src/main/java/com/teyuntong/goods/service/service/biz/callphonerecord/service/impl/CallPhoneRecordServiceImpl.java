package com.teyuntong.goods.service.service.biz.callphonerecord.service.impl;

import com.teyuntong.goods.service.service.biz.callphonerecord.bean.GoodsContactTimeDTO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.CallPhoneRecordMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.CallPhoneRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 拨打电话记录 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-04-07
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CallPhoneRecordServiceImpl implements CallPhoneRecordService {

    private final CallPhoneRecordMapper callPhoneRecordMapper;


    @Override
    public CallPhoneRecordDO getLatestCallRecord(List<Long> srcMsgIds) {
        return callPhoneRecordMapper.getLatestCallRecord(srcMsgIds);
    }

    /**
     * 获取指定时间到今天的所有货源首次联系时长
     *
     * @param startCity
     * @param destCity
     * @param startTime
     * @return
     */
    @Override
    public List<GoodsContactTimeDTO> getGoodsContactTimeByRoute(String startCity, String destCity, Date startTime) {
        if (StringUtils.isAnyBlank(startCity, destCity) || Objects.isNull(startTime)) {
            return new ArrayList<>();
        }
        return callPhoneRecordMapper.getGoodsContactTimeByRoute(startCity, destCity, startTime);
    }

}
