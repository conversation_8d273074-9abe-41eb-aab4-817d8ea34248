package com.teyuntong.goods.service.service.rpc.publish.pre;

import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.enums.TransportStatusEnum;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 隐藏历史货源的可见状态，在已过期列表里不显示
 *
 * <AUTHOR>
 * @since 2025/02/23 17:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HideHistoryTransportPreHandler {

    private final TransportMainService transportMainService;

    public void handler(DirectPublishProcessBO processBO) {
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        // 历史货源的display_type置为不可见，如果是已成交货源直接发布，不隐藏
        if (directPublishBO.isHistoryGoods()
                && !processBO.getOldMain().getStatus().equals(TransportStatusEnum.DEAL.getCode())) {
            transportMainService.hideHistoryTransport(directPublishBO.getSrcMsgId());
        }
    }

}
