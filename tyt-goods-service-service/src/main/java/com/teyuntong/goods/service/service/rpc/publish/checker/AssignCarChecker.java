package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class AssignCarChecker {

    private final UserRemoteService userRemoteService;

    public void check(PublishBO publishBO) {
        if (Objects.equals(YesOrNoEnum.YES.getId(), publishBO.getDispatchTransport())
                && StringUtils.isNotBlank(publishBO.getCarTelPhone())){
            UserRpcVO user = userRemoteService.getUserInfoByCellPhone(publishBO.getCarTelPhone());
            if (null == user){
                throw BusinessException.createException(GoodsErrorCode.INVOICE_DRIVER_NOT_EXIST.getCode(), "指派司机不存在");
            }
            publishBO.setCarUserid(user.getId());
        }

    }
}
