package com.teyuntong.goods.service.service.rpc.publish.bo;

import com.teyuntong.goods.service.client.publish.dto.CalcSpecialGoodsPriceResultDTO;
import com.teyuntong.goods.service.client.transport.dto.UserDispatchTransportDTO;
import lombok.Getter;
import lombok.Setter;

/**
 * 直接发布请求参数BO
 *
 * <AUTHOR>
 * @since 2025/02/11 10:05
 */
@Getter
@Setter
public class PublishProcessBO extends BasePublishProcessBO {

    /**
     * 请求信息
     */
    private PublishBO publishBO;
    /**
     * 专车运费计算结果
     */
    private CalcSpecialGoodsPriceResultDTO specialGoodsPriceResult;


}
