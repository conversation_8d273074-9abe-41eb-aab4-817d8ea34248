package com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.entity.TytSyncgoodsUserBlackDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 同步YMM用户黑名单表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-05-15
 */
@Mapper
public interface TytSyncgoodsUserBlackMapper extends BaseMapper<TytSyncgoodsUserBlackDO> {

    /**
     * 根据用户ID查询黑名单记录
     *
     * @param userId 用户ID
     * @return 黑名单记录
     */
    TytSyncgoodsUserBlackDO selectByUserId(@Param("userId") Long userId);

    /**
     * 插入黑名单记录
     *
     * @param userId 用户ID
     * @param reason 原因
     * @param createUsername 创建人用户名
     * @return 影响行数
     */
    int insertBlackRecord(@Param("userId") Long userId,
                          @Param("reason") String reason,
                          @Param("createUsername") String createUsername);
}
