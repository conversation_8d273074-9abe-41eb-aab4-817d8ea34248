package com.teyuntong.goods.service.service.rpc.publish.post;

import cn.hutool.core.bean.BeanUtil;
import com.teyuntong.goods.service.service.biz.transport.dto.DispatchTransportCountDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchReceiveConfigDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.UserDispatchAuthInfoDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.UserDispatchTransportInfoDO;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchReceiveConfigService;
import com.teyuntong.goods.service.service.biz.transport.service.UserDispatchAuthInfoService;
import com.teyuntong.goods.service.service.biz.transport.service.UserDispatchTransportInfoService;
import com.teyuntong.goods.service.service.common.enums.DispatchDealStatusEnum;
import com.teyuntong.goods.service.service.common.enums.DispatchTypeEnum;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.mq.constant.TopicConstant;
import com.teyuntong.goods.service.service.mq.pojo.DispatchAcceptMqBean;
import com.teyuntong.goods.service.service.remote.user.CsBusinessUserRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.rocketmq.core.RocketMqProducer;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.common.rocketmq.message.MqMessageFactory;
import com.teyuntong.user.service.client.custom.dto.CsBusinessUserQueryDTO;
import com.teyuntong.user.service.client.custom.vo.CsBusinessUserVO;
import com.teyuntong.user.service.client.user.vo.DwsNewIdentiwoDataRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 新代调货源分配调度经理
 *
 * <AUTHOR>
 * @since 2025/02/23 17:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DispatchTransportPoster {

    private final DispatchReceiveConfigService dispatchReceiveConfigService;
    private final UserDispatchAuthInfoService userDispatchAuthInfoService;
    private final UserDispatchTransportInfoService userDispatchTransportInfoService;
    private final UserRemoteService userRemoteService;

    private final CsBusinessUserRemoteService csBusinessUserRemoteService;

    private final RocketMqProducer rocketMqProducer;
    private final MqMessageFactory mqMessageFactory;

    /**
     * 编辑发布分配调度经理
     */
    @Async("threadPoolExecutor")
    public void handler(PublishProcessBO processBO) {
        PublishBO publishBO = processBO.getPublishBO();
        TransportMainDO transport = processBO.getTransportMain();
        UserRpcVO user = processBO.getUser();

        // 查询该货源之前的调度记录
        UserDispatchTransportInfoDO oldDispatchInfo = userDispatchTransportInfoService.getBySrcMsgId(transport.getSrcMsgId());
        // 是否是调度货源
        boolean isDispatchTransport = Objects.equals(publishBO.getDispatchTransport(), 1);

        // 该货源之前分配过调度
        if (oldDispatchInfo != null) {
            // 该货源是调度货源
            if (isDispatchTransport) {
                // 更新货源调度记录
                log.info("新代调分配调度，该货源之前分配过调度，srcMsg:{}，调度：{}", transport.getSrcMsgId(), oldDispatchInfo.getDispatchName());
                UserDispatchTransportInfoDO updateDispatchInfo = new UserDispatchTransportInfoDO();
                updateDispatchInfo.setId(oldDispatchInfo.getId());
                updateDispatchInfo.setUserInfoFee(transport.getInfoFee());
                updateDispatchInfo.setDispatchTime(new Date());
                updateDispatchInfo.setDispatchDealStatus(DispatchDealStatusEnum.CONFIRMING.getCode());
                updateDispatchInfo.setCancelAuthTime(null); // 把取消授权时间置空
                updateDispatchInfo.setModifyTime(new Date());
                updateDispatchInfo.setModifyName(user.getUserName());
                userDispatchTransportInfoService.updateById(updateDispatchInfo);

            } else {
                // 取消货源的调度授权
                log.info("新代调分配调度，取消货源的调度授权，srcMsg:{}，调度：{}", transport.getSrcMsgId(), oldDispatchInfo.getDispatchName());
                UserDispatchTransportInfoDO updateDispatchInfo = new UserDispatchTransportInfoDO();
                updateDispatchInfo.setId(oldDispatchInfo.getId());
                updateDispatchInfo.setDispatchDealStatus(DispatchDealStatusEnum.CANCEL_AUTH.getCode());
                updateDispatchInfo.setCancelAuthTime(new Date());
                updateDispatchInfo.setModifyTime(new Date());
                updateDispatchInfo.setModifyName(user.getUserName());
                userDispatchTransportInfoService.updateById(updateDispatchInfo);
            }

            // 首发货源
        } else {
            // 该货源是调度货源
            if (isDispatchTransport) {
                // 新代调货源首发分配调度经理
                log.info("新代调分配调度，首发分配调度，srcMsg:{}", transport.getSrcMsgId());
                DispatchReceiveConfigDO dispatchConfig = firstPublishDispatchConfig(user);
                if (dispatchConfig == null) {
                    throw new BusinessException(GoodsErrorCode.NO_DISPATCH_USER);
                }
                log.info("新代调分配调度，首发分配调度，srcMsg:{}，调度：{}", transport.getSrcMsgId(), dispatchConfig.getDispatchName());

                // 保存货源调度信息
                UserDispatchTransportInfoDO newDispatchInfo = new UserDispatchTransportInfoDO();
                newDispatchInfo.setSrcMsgId(transport.getSrcMsgId());
                newDispatchInfo.setUserInfoFee(transport.getInfoFee());
                newDispatchInfo.setDispatchId(dispatchConfig.getDispatchId());
                newDispatchInfo.setDispatchConcatPhone(dispatchConfig.getContactPhone());
                newDispatchInfo.setDispatchName(dispatchConfig.getDispatchName());
                newDispatchInfo.setDispatchTime(new Date());
                newDispatchInfo.setDispatchDealStatus(DispatchDealStatusEnum.CONFIRMING.getCode());
                newDispatchInfo.setCreateTime(new Date());
                newDispatchInfo.setCreateName(user.getUserName());
                userDispatchTransportInfoService.save(newDispatchInfo);

            } else {
                // 货源不是调度货源，不做处理
                log.info("新代调分配调度，货源不是调度货源，不做处理，srcMsg:{}", transport.getSrcMsgId());
            }
        }

    }

    /**
     * 新代调货源首发分配调度经理:
     * 1. 首次授权用户：在岗当天分配货量最少的调度经理（分配数量一致随机取）
     * 2. 非首次授权的货源：优先判断用户的调度经理是否满足在岗，若满足，则分配给该用户的调度经理。若调度经理不满足条件，按首次授权规则匹配。
     * 3. 调度经理全都不在岗（异常情况）：有调度经理优先分配给调度经理，没调度经理分配给货量最少的。
     */
    private DispatchReceiveConfigDO firstPublishDispatchConfig(UserRpcVO user) {
        // 1. 如果之前授权过，用之前的代调账户
        UserDispatchAuthInfoDO authInfo = userDispatchAuthInfoService.getByUserId(user.getId());

        // 1.1 如果代调取消接单，重新分配调度，否则使用之前授权过的代调账户
        DispatchReceiveConfigDO curDispatchConfig = null; // 本次调度
        DispatchReceiveConfigDO dispatchConfig = null;
        if (authInfo != null) {
            dispatchConfig = dispatchReceiveConfigService.getByDispatchId(authInfo.getDispatchId());
            if (dispatchConfig != null && dispatchConfig.getStatus() == 1) {
                log.info("新代调分配调度，之前授权过，使用原来的配置，userId:{}，代调账户：{}", user.getId(), authInfo.getDispatchName());
                curDispatchConfig = dispatchConfig;
            }
        }

        // 2. 如果是第一次授权或代调不再接单，筛选一个代调账户
        if (curDispatchConfig == null) {
            // 2.1 查询所有调度人员
            CsBusinessUserQueryDTO bizUserQuery = new CsBusinessUserQueryDTO();
            bizUserQuery.setCsDepartmentId(34L); // 调度部门
            List<CsBusinessUserVO> bizUserList = csBusinessUserRemoteService.queryBizUser(bizUserQuery);
            if (CollectionUtils.isEmpty(bizUserList)) {
                log.info("新代调分配调度，没有调度人员");
                return dispatchConfig;
            }

            // 2.2 筛选所有可接单的调度
            List<Long> dispatchIds = bizUserList.stream().map(CsBusinessUserVO::getId).toList();
            List<DispatchReceiveConfigDO> dispatchList = dispatchReceiveConfigService.getDispatchList(dispatchIds);
            List<DispatchReceiveConfigDO> validDispatchList = dispatchList.stream()
                    .filter(dispatch -> dispatch.getStatus() == 1).collect(Collectors.toList());

            // 2.3 如果有可接单的调度，返回最少分配的调度
            if (CollectionUtils.isNotEmpty(validDispatchList)) {
                log.info("新代调分配调度，有可接单的调度，返回当天分配最少得调度");
                curDispatchConfig = getLeastDispatch(validDispatchList);
            } else {
                // 2.4 如果没有可接单的调度，有调度经理优先分配给调度经理，没调度经理分配给货量最少的
                if (dispatchConfig != null) {
                    log.info("新代调分配调度，调度人员全部不可接单，配置之前的调度");
                    return dispatchConfig;
                } else {
                    log.info("新代调分配调度，调度人员全部不可接单，返回货量最少的调度");
                    List<DispatchReceiveConfigDO> inValidDispatchList = dispatchList.stream()
                            .filter(dispatch -> dispatch.getStatus() != 1).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(inValidDispatchList)) {
                        curDispatchConfig = getLeastDispatch(inValidDispatchList);
                    } else {
                        // 兼容处理，如果调度配置表一条数据也没有，返回一条调度数据
                        curDispatchConfig = new DispatchReceiveConfigDO();
                        CsBusinessUserVO bizUser = bizUserList.get(0);
                        curDispatchConfig.setDispatchId(bizUser.getId());
                        curDispatchConfig.setDispatchName(bizUser.getName());
                        curDispatchConfig.setContactPhone(bizUser.getLoginPhoneNo());
                    }
                }
            }
            log.info("新代调分配调度，返回当天分配最少得调度，调度：{}", curDispatchConfig.getDispatchName());
        }
        // 3. 如果是第一次授权，添加授权信息
        if (authInfo == null) {
            // 添加授权信息
            UserDispatchAuthInfoDO newAuthInfo = new UserDispatchAuthInfoDO();
            newAuthInfo.setUserId(user.getId());
            newAuthInfo.setCellPhone(user.getCellPhone());
            newAuthInfo.setUserName(user.getUserName());
            DwsNewIdentiwoDataRpcVO userIdentity = userRemoteService.getDwsNewIdentiwoDataByUserId(user.getId());
            newAuthInfo.setUserIdentity(userIdentity == null ? 0 : userIdentity.getType());
            newAuthInfo.setDispatchId(curDispatchConfig.getDispatchId());
            newAuthInfo.setDispatchName(curDispatchConfig.getDispatchName());
            newAuthInfo.setCreateTime(new Date());
            newAuthInfo.setCreateName(user.getUserName());
            userDispatchAuthInfoService.save(newAuthInfo);
        }

        return curDispatchConfig;
    }

    /**
     * 返回最少分配的调度
     */
    private DispatchReceiveConfigDO getLeastDispatch(List<DispatchReceiveConfigDO> dispatchList) {
        // 统计当天调度的调货数量
        Map<Long, Integer> dispatchNumMap = userDispatchTransportInfoService.countTodayDispatchNum().stream()
                .collect(Collectors.toMap(DispatchTransportCountDTO::getDispatchId, DispatchTransportCountDTO::getNum));

        dispatchList.sort(Comparator.comparingInt(o -> dispatchNumMap.getOrDefault(o.getDispatchId(), 0)));
        return dispatchList.get(0);
    }

    /**
     * 自动重发分配调度经理
     */
    public void handler(DirectPublishProcessBO processBO) {
        // 0. 自动重发货源，如果之前货源处理状态是代调已发布，不管调度经理是否在岗，都分给上一票货源的调度经理。
        if (PublishOptEnum.AUTO_RESEND.equals(processBO.getOptEnum())) {
            TransportMainDO oldMain = processBO.getOldMain();
            if (SourceTypeEnum.NEW_DISPATCH.getCode().equals(oldMain.getSourceType())) {
                UserDispatchTransportInfoDO oldDispatchInfo = userDispatchTransportInfoService.getBySrcMsgId(oldMain.getSrcMsgId());
                log.info("自动重发货源，分配上一票货源的调用经理：{}", oldDispatchInfo.getDispatchName());
                // 根据之前的记录生成新货源调度记录
                UserDispatchTransportInfoDO newDispatchInfo = BeanUtil.copyProperties(oldDispatchInfo, UserDispatchTransportInfoDO.class);
                // 货源id更新为当前货源id，时间都更新为当前时间
                newDispatchInfo.setId(null);
                newDispatchInfo.setSrcMsgId(processBO.getTransportMain().getSrcMsgId());
                newDispatchInfo.setCreateTime(new Date());
                newDispatchInfo.setModifyTime(new Date());
                // newDispatchInfo.setDispatchDealStatus(DispatchDealStatusEnum.DISPATCH_PUBLISH.getCode());
                newDispatchInfo.setDispatchTime(new Date());
                newDispatchInfo.setDispatchPubTime(new Date());
                newDispatchInfo.setCancelAuthTime(null);

                userDispatchTransportInfoService.save(newDispatchInfo);
            }
        }
    }

    public void dealDispatchTransportInfo(PublishBO publishBO) {
        Long srcMsgId = publishBO.getSrcMsgId();
        if (Objects.equals(YesOrNoEnum.YES.getId(), publishBO.getDispatchTransport())) {
            UserDispatchTransportInfoDO infoDO = userDispatchTransportInfoService.getBySrcMsgId(srcMsgId);
            infoDO.setDispatchConcatPhone(publishBO.getDispatchConcatPhone());
            infoDO.setOwnerFreight(publishBO.getOwnerFreight());
            infoDO.setGiveGoodsName(publishBO.getDispatchGiveGoodsName());
            infoDO.setGiveGoodsPhone(publishBO.getDispatchGiveGoodsPhone());
            infoDO.setModifyName(publishBO.getOperator());
            infoDO.setModifyTime(new Date());
            if (Objects.equals(publishBO.getDispatchType(), DispatchTypeEnum.DISPATCH_TAKE_PUBLISH.getCode())) {
                infoDO.setDispatchDealStatus(DispatchDealStatusEnum.DISPATCH_PUBLISH.getCode());
                infoDO.setDispatchPubTime(new Date());
                // 接单找车推送企业微信
                DispatchAcceptMqBean mqBean = new DispatchAcceptMqBean();
                mqBean.setSrcMsgId(srcMsgId);
                String key = UUID.randomUUID().toString();
                MqMessage mqMessage = mqMessageFactory.create(TopicConstant.GOODS_CENTER_TOPIC, TopicConstant.NEW_DISPATCH_ACCEPT, key, mqBean);
                rocketMqProducer.sendNormal(mqMessage);
            }
            userDispatchTransportInfoService.updateById(infoDO);

        }

    }

}
