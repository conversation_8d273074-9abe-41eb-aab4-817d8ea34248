package com.teyuntong.goods.service.service.rpc.transport;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.publish.dto.DispatchDirectPublishDTO;
import com.teyuntong.goods.service.client.publish.service.TransportDirectPublishRpcService;
import com.teyuntong.goods.service.client.transport.dto.UserDispatchTransportDTO;
import com.teyuntong.goods.service.client.transport.enums.CancelAuthOperateTypeEnum;
import com.teyuntong.goods.service.client.transport.service.UserDispatchTransportInfoRpcService;
import com.teyuntong.goods.service.client.transport.vo.UserDispatchTransportInfoVO;
import com.teyuntong.goods.service.client.transport.vo.UserDispatchTransportVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.UserDispatchTransportInfoDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportService;
import com.teyuntong.goods.service.service.biz.transport.service.UserDispatchTransportInfoService;
import com.teyuntong.goods.service.service.common.enums.DispatchDealStatusEnum;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 用户授权货源
 *
 * <AUTHOR>
 * @since 2025-09-15 14:53
 */
@RestController
@Slf4j
public class UserDispatchTransportInfoRpcServiceImpl implements UserDispatchTransportInfoRpcService {

    @Resource
    private UserDispatchTransportInfoService userDispatchTransportInfoService;
    @Resource
    private TransportDirectPublishRpcService transportDirectPublishRpcService;
    @Resource
    private TransportMainService transportMainService;
    @Resource
    private TransportService transportService;


    /**
     * 用户取消授权、代调取消授权、超时释放大厅
     *
     * @param dto
     * @return
     */
    @Override
    public UserDispatchTransportVO cancelDispatchAuth(UserDispatchTransportDTO dto) {
        if (Objects.isNull(dto.getSrcMsgId())) {
            throw new BusinessException(GoodsErrorCode.ERROR_SRC_MSG_ID_LACK);
        }
        UserDispatchTransportVO vo = new UserDispatchTransportVO();
        UserDispatchTransportInfoDO transportInfoDO = userDispatchTransportInfoService.getBySrcMsgId(dto.getSrcMsgId());
        if (Objects.isNull(transportInfoDO) ||
                (!Objects.equals(DispatchDealStatusEnum.CONFIRMING.getCode(), transportInfoDO.getDispatchDealStatus()) &&
                        !Objects.equals(DispatchDealStatusEnum.DISPATCH_PUBLISH.getCode(), transportInfoDO.getDispatchDealStatus()))) {
            log.info("授权货源记录不存在或者不是【待接单、代调已发布】状态，不处理。{}", JSONObject.toJSONString(transportInfoDO));
            return vo;
        }

        // 释放到大厅
        DispatchDirectPublishDTO directPublishDTO = new DispatchDirectPublishDTO();
        directPublishDTO.setSrcMsgId(dto.getSrcMsgId());
        dto.setUserInfoFee(transportInfoDO.getUserInfoFee()); // 用于还原用户信息费
        directPublishDTO.setUserDispatchTransportDTO(dto);
        transportDirectPublishRpcService.cancelDispatchAuth(directPublishDTO);

        // 处理状态：用户取消授权
        transportInfoDO.setDispatchDealStatus(CancelAuthOperateTypeEnum.getDispatchDealStatus(dto.getOperateType()));
        transportInfoDO.setModifyTime(new Date());
        transportInfoDO.setCancelAuthTime(new Date());
        transportInfoDO.setModifyName(dto.getOperatorName());
        userDispatchTransportInfoService.cancelDispatchAuth(transportInfoDO);

        return vo;
    }

    @Override
    public UserDispatchTransportInfoVO getUserDispatchTransportBySrcMsgId(Long srcMsgId) {
        return BeanUtil.copyProperties(userDispatchTransportInfoService.getBySrcMsgId(srcMsgId), UserDispatchTransportInfoVO.class);
    }
}
