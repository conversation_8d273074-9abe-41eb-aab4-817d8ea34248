package com.teyuntong.goods.service.service.biz.callphonerecord.service.impl;

import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.AppCallLogMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.AppCallLogService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 记录APP拨打电话的信息（电话标注）  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AppCallLogServiceImpl implements AppCallLogService {

    private final AppCallLogMapper appCallLogMapper;

    @Override
    public int getViewLogCountBySrcMsgId(Long srcMsgId) {
        return appCallLogMapper.getViewLogCountBySrcMsgId(srcMsgId);
    }
}
