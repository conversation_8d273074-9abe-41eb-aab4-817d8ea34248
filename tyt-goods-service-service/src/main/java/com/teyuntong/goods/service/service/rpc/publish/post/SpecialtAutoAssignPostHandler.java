package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.mq.constant.TopicConstant;
import com.teyuntong.goods.service.service.mq.pojo.SpecialCarDispatchBean;
import com.teyuntong.infra.common.rocketmq.core.RocketMqProducer;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.common.rocketmq.message.MqMessageFactory;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 专车自动派单
 *
 * <AUTHOR>
 * @since 2025/02/23 17:42
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpecialtAutoAssignPostHandler {

    private final RocketMqProducer rocketMqProducer;
    private final MqMessageFactory mqMessageFactory;

    /**
     * 专车自动派单
     */
    // @Async("specialCarThreadPoolExecutor")
    public void specialAssign(TransportMainDO transportMain) {
        // 发送专车派单mq
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(transportMain.getExcellentGoods())) {
            SpecialCarDispatchBean specialCarDispatchBean = new SpecialCarDispatchBean();
            specialCarDispatchBean.setUserId(transportMain.getUserId());
            specialCarDispatchBean.setSrcMsgId(transportMain.getSrcMsgId());
            String key = UUID.randomUUID().toString();
            MqMessage mqMessage = mqMessageFactory.create(TopicConstant.GOODS_CENTER_TOPIC, TopicConstant.DISPATCH_SPECIAL_FLAG, key, specialCarDispatchBean);
            rocketMqProducer.sendNormal(mqMessage);
        }
    }

}
