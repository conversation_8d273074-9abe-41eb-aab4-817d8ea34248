package com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.entity.CustomFirstOrderRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Mapper
public interface CustomFirstOrderRecordMapper extends BaseMapper<CustomFirstOrderRecordDO> {

    // 根据用户手机号查询
    CustomFirstOrderRecordDO getByCustomPhone(@Param("customPhone") String customPhone);

    // 更新订单完成时间
    void updateFirstFinishOrderTimeByCustomPhone(@Param("customPhone") String customPhone,
                                                 @Param("firstFinishOrderTime") Date firstFinishOrderTime);

    /**
     * 查询是否有首履过
     *
     * @param cellPhone
     * @return
     */
    int countFinishOrder(@Param("cellPhone") String cellPhone);


}
