package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运单状态 1有效（发布中），0无效（已过期），2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TransportStatusEnum {
    VALID(1, "发布中"),
    INVALID(0, "无效"),
    PENDING(2, "待定（暂未使用）"),
    BLOCKED(3, "阻止（暂未使用）"),
    DEAL(4, "成交"),
    CANCEL(5, "取消"),
    CONFIRMING(6, "待确认"),
    ;

    private final Integer code;
    private final String desc;

    public static boolean isPublish(Integer code) {
        return VALID.getCode().equals(code);
    }

    public static boolean needCancel(Integer code) {
        return VALID.getCode().equals(code) || CONFIRMING.getCode().equals(code);
    }
}
