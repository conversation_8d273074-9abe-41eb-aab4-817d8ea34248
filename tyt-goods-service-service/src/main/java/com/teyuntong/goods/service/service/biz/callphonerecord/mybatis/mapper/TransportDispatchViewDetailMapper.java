package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 货源查看、联系详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-04-10
 */
@Mapper
public interface TransportDispatchViewDetailMapper extends BaseMapper<TransportDispatchViewDetailDO> {

    List<Long> queryViewCarUser(@Param("srcMsgId") Long srcMsgId);
}
