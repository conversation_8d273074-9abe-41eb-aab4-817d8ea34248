package com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.entity.TytSyncgoodsUserRejectDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 同步YMM用户拒绝授权记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-05-15
 */
@Mapper
public interface TytSyncgoodsUserRejectMapper extends BaseMapper<TytSyncgoodsUserRejectDO> {

    /**
     * 插入拒绝授权记录
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int insertRejectRecord(@Param("userId") Long userId);

    /**
     * 统计用户拒绝授权次数
     *
     * @param userId 用户ID
     * @return 拒绝次数
     */
    int countRejectByUserId(@Param("userId") Long userId);

    /**
     * 清空用户拒绝授权记录
     *
     * @param userId 用户ID
     */
    void cleanAllByUserId(@Param("userId") Long userId);

}
