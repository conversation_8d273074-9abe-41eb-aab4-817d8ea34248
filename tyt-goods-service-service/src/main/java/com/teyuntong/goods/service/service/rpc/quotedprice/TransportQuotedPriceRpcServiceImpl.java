package com.teyuntong.goods.service.service.rpc.quotedprice;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.client.quotedprice.dto.QuotedPriceDTO;
import com.teyuntong.goods.service.client.quotedprice.service.TransportQuotedPriceRpcService;
import com.teyuntong.goods.service.client.quotedprice.vo.*;
import com.teyuntong.goods.service.client.transport.dto.GoodCarPriceTransportTabAndBIPriceDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportCarryDTO;
import com.teyuntong.goods.service.client.transport.vo.TransportMainVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.*;
import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.common.enums.TransportStatusEnum;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.teyuntong.goods.service.service.common.error.GoodsErrorCode.*;

/**
 * 货源报价相关
 *
 * <AUTHOR>
 * @since 2024-11-05 15:27
 */
@RestController
public class TransportQuotedPriceRpcServiceImpl implements TransportQuotedPriceRpcService {
    @Autowired
    private TransportQuotedPriceService transportQuotedPriceService;
    @Autowired
    private SeckillGoodsTransportService seckillGoodsTransportService;
    @Autowired
    private TransportMainService transportMainService;
    @Autowired
    private TransportService transportService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private GoodCarPriceTransportService goodCarPriceTransportService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     *车获取某个货源自己的报价
     *
     * @param carUserId
     * @param srcMsgId
     * @return
     */
    @Override
    public TransportQuotedPriceCarVO getCarToTransportQuotedPrice(Long carUserId, Long srcMsgId) {
        return transportQuotedPriceService.getCarToTransportQuotedPrice(carUserId, srcMsgId);
    }

    /**
     * 货源详情顶部报价列表
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    @Override
    public TransportQuotedPriceDataInDetailPageVO getQuotedPriceListInSingleDetailPage(Long srcMsgId, Long userId) {
        if (Objects.isNull(srcMsgId) || Objects.isNull(userId)) {
            return new TransportQuotedPriceDataInDetailPageVO();
        }
        TransportMainDO transportMainDO = transportMainService.getById(srcMsgId);
        if (transportMainDO == null || !Objects.equals(transportMainDO.getStatus(), TransportStatusEnum.VALID.getCode())
                || transportMainDO.getSourceType().equals(SourceTypeEnum.NEW_DISPATCH.getCode())) {
            return new TransportQuotedPriceDataInDetailPageVO();
        }
        List<TransportQuotedPriceTransportVO> quotedPriceList = transportQuotedPriceService.getQuotedPriceListInSingleDetailPage(srcMsgId, userId);
        TransportQuotedPriceDataInDetailPageVO detailPageVO = new TransportQuotedPriceDataInDetailPageVO();
        detailPageVO.setTransportQuotedPriceTransportVOS(quotedPriceList);
        detailPageVO.setTransportQuotedPriceCount(transportQuotedPriceService.getTransportQuotedPriceCountBySrcMsgId(srcMsgId));

        if (!quotedPriceList.isEmpty()) {
            for (TransportQuotedPriceTransportVO transportQuotedPriceTransportVO : quotedPriceList) {
                if (transportQuotedPriceTransportVO.getQuotedType() != null && transportQuotedPriceTransportVO.getQuotedType() == 0
                        && transportQuotedPriceTransportVO.getFinalQuotedPriceIsDone() != null && transportQuotedPriceTransportVO.getFinalQuotedPriceIsDone() == 1) {
                    transportQuotedPriceTransportVO.setHaveCallToCarButton(1);
                }

                // 设置新标
                String caUserIdStr = stringRedisTemplate.opsForValue().get("transportCarQuoteNew" + ":" + transportQuotedPriceTransportVO.getSrcMsgId());
                if (caUserIdStr != null) {
                    List<String> carUserIdList = JSON.parseArray(caUserIdStr, String.class);
                    if (carUserIdList.contains(transportQuotedPriceTransportVO.getCarId().toString())) {
                        transportQuotedPriceTransportVO.setHaveNewFlag(true);
                    }
                }
            }
        }

        stringRedisTemplate.delete("transportCarQuoteNew" + ":" + srcMsgId);

//        boolean haveNoLookTransport = quotedPriceList.stream().anyMatch(TransportQuotedPriceTransportVO::getTransportNoLook);
//
//        int transportHaveOptionQuotedPriceCount = transportQuotedPriceService.getTransportHaveOptionQuotedPriceCount(srcMsgId);
//        String bubbleClick = redisUtil.getString(RedisKeyConstant.TRANSPORT_QUOTED_PRICE_BUBBLE_CLICK + ":" + srcMsgId);
//        if (!haveNoLookTransport && StringUtils.isBlank(bubbleClick) && transportHaveOptionQuotedPriceCount == 0) {
//            detailPageVO.setShowBubble(true);
//        }
        return detailPageVO;
    }

    /**
     * 车离开货源详情报价挽留弹窗
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public Boolean getCarLeaveTransportSingleDetailTabData(Long srcMsgId, Long userId) {
        if (Objects.isNull(srcMsgId) || Objects.isNull(userId)) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        return transportQuotedPriceService.getCarLeaveTransportSingleDetailTabData(srcMsgId, userId);
    }

    /**
     * 获取报价货源详情
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    @Override
    public TransportMainVO getTransportVO(Long srcMsgId, Long userId) {
        if (Objects.isNull(srcMsgId) || Objects.isNull(userId)) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        boolean isTransportOwner = transportMainService.checkUserIsTransportOwner(userId, srcMsgId);
        if (!isTransportOwner) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        TransportMainVO mainVO = new TransportMainVO();
        TransportMainDO main = transportMainService.getTransportMainForId(srcMsgId);
        BeanUtils.copyProperties(main, mainVO);
        return mainVO;
    }

    /**
     * 货在报价列表web页面顶部氛围文案
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public String getTransportQuotedPricePageWord(Long srcMsgId) {
        if (Objects.isNull(srcMsgId)) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        return transportQuotedPriceService.getTransportQuotedPricePageWord(srcMsgId);
    }

    /**
     * 货获取所有发布中货源的所有报价列表
     *
     * @param userId
     * @return
     */
    @Override
    public List<AllTransportQuotedPriceVO> getAllPublishingTransportQuotedPriceList(Long userId) {
        if (Objects.isNull(userId)) {
            return new ArrayList<>();
        }
        return transportQuotedPriceService.getAllPublishingTransportQuotedPriceList(userId);
    }

    /**
     * 获取货源最新一条是出价记录还是沟通记录
     *
     * @param userId
     * @return
     */
    @Override
    public RecordTypeVO transportNewestRecordType(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        return transportQuotedPriceService.transportNewestRecordType(userId);
    }

    /**
     * 货主同意出价
     *
     * @param agreeDTO
     */
    @Override
    public QuotedPriceResultVO transportAgree(QuotedPriceDTO agreeDTO) {
        if (Objects.isNull(agreeDTO.getTransportQuotedPriceId()) || Objects.isNull(agreeDTO.getSrcMsgId())
                || Objects.isNull(agreeDTO.getUserId())) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }

        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(agreeDTO.getSrcMsgId())) {
            throw new BusinessException(SECKILL_TRANSPORT_IS_LOCK, null);
        }

        if (!transportMainService.checkUserIsTransportOwner(agreeDTO.getUserId(), agreeDTO.getSrcMsgId())) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }

        transportQuotedPriceService.checkTransportValidityV2(agreeDTO.getSrcMsgId(), true);

        return transportQuotedPriceService.transportAgree(agreeDTO);
    }

    /**
     * 货主出价/拒绝报价
     *
     * @param priceDTO
     */
    @Override
    public QuotedPriceResultVO transportQuotedPrice(QuotedPriceDTO priceDTO) {
        if (Objects.isNull(priceDTO.getTransportQuotedPriceId()) || Objects.isNull(priceDTO.getSrcMsgId())
                || Objects.isNull(priceDTO.getUserId()) || Objects.isNull(priceDTO.getPrice()) || priceDTO.getPrice() <= 0) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }

        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(priceDTO.getSrcMsgId())) {
            throw new BusinessException(SECKILL_TRANSPORT_IS_LOCK, null);
        }

        if (!transportMainService.checkUserIsTransportOwner(priceDTO.getUserId(), priceDTO.getSrcMsgId())) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }

        transportQuotedPriceService.transportQuotedPrice(priceDTO);
        return new QuotedPriceResultVO();
    }

    /**
     * 车主出价
     *
     * @param priceDTO
     */
    @Override
    public QuotedPriceResultVO carQuotedPrice(QuotedPriceDTO priceDTO) {
        if (Objects.isNull(priceDTO.getUserId()) || Objects.isNull(priceDTO.getSrcMsgId()) ||
                Objects.isNull(priceDTO.getPrice()) || priceDTO.getPrice() <=0) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }

        // 秒抢货源判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(priceDTO.getSrcMsgId())) {
            throw new BusinessException(SEC_KILL_GOODS_LOCK_MSG, null);
        }

        return transportQuotedPriceService.carQuotedPrice(priceDTO);
    }

    /**
     * 车同意报价
     *
     * @param priceDTO
     */
    @Override
    public QuotedPriceResultVO carAgree(QuotedPriceDTO priceDTO) {
        if (Objects.isNull(priceDTO.getUserId()) || Objects.isNull(priceDTO.getSrcMsgId())) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }

        // 秒抢货源判断
        if (seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(priceDTO.getSrcMsgId())) {
            throw new BusinessException(SEC_KILL_GOODS_LOCK_MSG, null);
        }

        transportQuotedPriceService.carAgree(priceDTO);
        return new QuotedPriceResultVO();
    }

    /**
     * 货方货源详情顶部报价列表点击提醒报价气泡
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public Boolean clickTransportQuotedPriceBubble(Long srcMsgId) {
        if (Objects.isNull(srcMsgId)) {
            throw new BusinessException(ERROR_NO_PARAM);
        }
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        boolean isTransportOwner = transportMainService.checkUserIsTransportOwner(loginUser.getUserId(), srcMsgId);
        if (!isTransportOwner) {
            throw new BusinessException(ERROR_NO_BELONG_TO_ONESELF);
        }
        redisUtil.set(RedisKeyConstant.TRANSPORT_QUOTED_PRICE_BUBBLE_CLICK + ":" + srcMsgId, "1", Duration.ofSeconds(60 * 60 * 30));
        return true;
    }

    /**
     * 货主是否有货源被车方出价
     *
     * @return
     */
    @Override
    public Boolean getTransportHaveAnyQuotedPrice(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        return transportQuotedPriceService.getTransportHaveAnyQuotedPrice(userId);
    }

    /**
     * 车主是否存在货方回价了但车方还没想响应的货
     *
     * @param userId
     * @return
     */
    @Override
    public Boolean getCarHaveNewTransportQuotedPrice(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        return transportQuotedPriceService.getCarHaveNewTransportQuotedPrice(userId);
    }

    /**
     * 车方 被反馈（报价被货方同意）、有回价气泡内容
     *
     * @param userId
     * @return
     */
    @Override
    public String getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice(Long userId) {
        if (Objects.isNull(userId)) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        return transportQuotedPriceService.getCarHaveNewTransportQuotedPriceOrAgreeQuotedPrice(userId);
    }

    /**
     * 货报价挽留弹窗
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public TransportQuotedPriceLeaveTabVO getTransportQuotedPriceLeaveTab(Long srcMsgId) {
        return transportQuotedPriceService.getTransportQuotedPriceLeaveTab(srcMsgId);
    }

    @Override
    public TransportQuotedPriceTabDataVO getTransportQuotedPriceTabData(Long transportQuotedPriceId) {
        if (Objects.isNull(transportQuotedPriceId)) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        return transportQuotedPriceService.getTransportQuotedPriceTabData(transportQuotedPriceId);
    }

    /**
     * 货获取某个货源的所有报价列表
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    @Override
    public List<TransportQuotedPriceTransportVO> getTransportQuotedPriceList(Long srcMsgId, Long userId) {
        return transportQuotedPriceService.getTransportQuotedPriceList(srcMsgId, userId);
    }

    /**
     * 校验用户是否在出价ab测
     *
     * @param userId
     * @return
     */
    @Override
    public Boolean checkUserIsInTransportQuotedPriceABTest(Long userId) {
        return transportQuotedPriceService.checkUserIsInTransportQuotedPriceABTest(userId);
    }

    /**
     * 判断要出价货源是否有效
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public Boolean checkTransportValidity(Long srcMsgId) {
        return transportQuotedPriceService.checkTransportValidityV2(srcMsgId, true);
    }

    @Override
    public CarShowQuotedPriceBoxVO carShowQuotedPriceBox(Long srcMsgId) {
        CarShowQuotedPriceBoxVO result = new CarShowQuotedPriceBoxVO();

        //获取优车2.0建议价
        TransportMainDO mainDO = transportMainService.getById(srcMsgId);
        if (mainDO == null || mainDO.getId() == null) {
            //兼容app传错ID
            TransportDO byId = transportService.getById(srcMsgId);
            mainDO = new TransportMainDO();
            BeanUtils.copyProperties(byId, mainDO);
        }
        TransportCarryDTO carryDTO = buildTransportCarryDTO(mainDO);
        GoodCarPriceTransportTabAndBIPriceDTO showGoodCarPriceTransportTab = goodCarPriceTransportService.isShowGoodCarPriceTransportTab(carryDTO);
        if (!Objects.isNull(showGoodCarPriceTransportTab) && showGoodCarPriceTransportTab.getShowTab()
                && showGoodCarPriceTransportTab.getFixPriceMin() != null && showGoodCarPriceTransportTab.getFixPriceMin() > 0) {
            result.setAvgDealPrice(showGoodCarPriceTransportTab.getFixPriceMin());
        }

        result.setAddPriceOptions(getAddPriceOptions(mainDO.getPrice()));

        result.setPrice(mainDO.getPrice());
        result.setSrcMsgId(srcMsgId);

        return result;
    }

    private List<Integer> getAddPriceOptions(String price) {
        if (StringUtils.isBlank(price)) {
            return new ArrayList<>();
        }
        BigDecimal priceDecimal = new BigDecimal(price);
        if (priceDecimal.compareTo(new BigDecimal("1000")) <= 0) {
            return Arrays.asList(50, 100, 150, 200);
        } else if (priceDecimal.compareTo(new BigDecimal("2000")) <= 0) {
            return Arrays.asList(100, 150, 200, 300);
        } else if (priceDecimal.compareTo(new BigDecimal("5000")) <= 0) {
            return Arrays.asList(100, 200, 300, 500);
        } else {
            return Arrays.asList(100, 200, 500, 800);
        }
    }

    private TransportCarryDTO buildTransportCarryDTO(TransportMainDO mainDO) {
        TransportCarryDTO transportCarryDTO = new TransportCarryDTO();
        transportCarryDTO.setStartProvince(mainDO.getStartProvinc());
        transportCarryDTO.setStartCity(mainDO.getStartCity());
        transportCarryDTO.setStartArea(mainDO.getStartArea());
        transportCarryDTO.setDestProvince(mainDO.getDestProvinc());
        transportCarryDTO.setDestCity(mainDO.getDestCity());
        transportCarryDTO.setDestArea(mainDO.getDestArea());
        transportCarryDTO.setGoodsWeight(mainDO.getWeight());
        transportCarryDTO.setGoodsLength(mainDO.getLength());
        transportCarryDTO.setGoodsWide(mainDO.getWide());
        transportCarryDTO.setGoodsHigh(mainDO.getHigh());
        transportCarryDTO.setUserId(mainDO.getUserId());
        transportCarryDTO.setDistance(mainDO.getDistance() != null ? mainDO.getDistance().toString() : "0");
        transportCarryDTO.setGoodTypeName(mainDO.getGoodTypeName());
        return transportCarryDTO;
    }
}
