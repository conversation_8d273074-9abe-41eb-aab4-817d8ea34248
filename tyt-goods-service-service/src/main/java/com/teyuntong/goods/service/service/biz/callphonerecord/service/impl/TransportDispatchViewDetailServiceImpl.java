package com.teyuntong.goods.service.service.biz.callphonerecord.service.impl;

import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDetailDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.TransportDispatchViewDetailMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportDispatchViewDetailService;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 货源查看、联系详情表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-04-10
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportDispatchViewDetailServiceImpl implements TransportDispatchViewDetailService {
    private final TransportDispatchViewDetailMapper transportDispatchViewDetailMapper;

    /**
     * 保存货源查看或联系记录详情
     *
     * @param user      车主信息
     * @param srcMsgId  货源ID
     * @param type      查看类型 1：查看  2：联系
     */
    @Override
    public void saveViewDetail(UserRpcVO user, Long srcMsgId, Integer type) {
        TransportDispatchViewDetailDO tytTransportDispatchViewDetail = new TransportDispatchViewDetailDO();
        tytTransportDispatchViewDetail.setSrcMsgId(srcMsgId);
        tytTransportDispatchViewDetail.setCarUserId(user.getId());
        tytTransportDispatchViewDetail.setCarUserName(TransportUtil.formatUserName(user.getTrueName(), String.valueOf(user.getId())));
        tytTransportDispatchViewDetail.setCarNickName(TransportUtil.formatUserName(user.getUserName(), String.valueOf(user.getId())));
        tytTransportDispatchViewDetail.setCarPhone(user.getCellPhone());
        //1:查看  2：联系
        tytTransportDispatchViewDetail.setType(type);
        tytTransportDispatchViewDetail.setCreateTime(new Date());
        tytTransportDispatchViewDetail.setModifyTime(new Date());
        transportDispatchViewDetailMapper.insert(tytTransportDispatchViewDetail);
    }

    @Override
    public List<Long> queryViewCarUser(Long srcMsgId) {
        return transportDispatchViewDetailMapper.queryViewCarUser(srcMsgId);
    }
}
