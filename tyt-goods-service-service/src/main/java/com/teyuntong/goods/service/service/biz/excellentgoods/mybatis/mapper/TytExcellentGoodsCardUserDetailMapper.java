package com.teyuntong.goods.service.service.biz.excellentgoods.mybatis.mapper;

import com.teyuntong.goods.service.client.excellentgoods.vo.TytExcellentGoodsCardUserDetailCanUseCountVO;
import com.teyuntong.goods.service.service.biz.excellentgoods.mybatis.entity.TytExcellentGoodsCardUserDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytExcellentGoodsCardUserDetailMapper {

    List<TytExcellentGoodsCardUserDetail> getAllNoUseCarListByUserId(@Param("userId") Long userId, @Param("startNum") Integer startNum, @Param("pageSize") Integer pageSize);

    TytExcellentGoodsCardUserDetailCanUseCountVO getAllCanUseCarCountNumMax100AndLimitTimeByUserId(@Param("userId") Long userId);

    List<TytExcellentGoodsCardUserDetail> getAllCanUseCarListByUserIdPage(@Param("userId") Long userId, @Param("startNum") Integer startNum, @Param("pageSize") Integer pageSize);

    List<TytExcellentGoodsCardUserDetail> getAllCanUseCarListByUserId(@Param("userId") Long userId);

}