package com.teyuntong.goods.service.service.biz.callphonerecord.service;


import com.teyuntong.goods.service.client.callphonerecord.vo.PrivacyPhoneNumGoodIdReq;

public interface PrivacyPhoneNumService {

    /**
     * 根据参数获取虚拟号（调用本方法前请保证bizId在业务中存在）
     * @param privacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq
     * @return resultMsgBean data(隐私号字符串)（返回data可能为null）
     */
    String getPrivacyPhoneNum(PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq);

    String makeAXBUserFieldParam(Long driverUserId, Long goodsId);

    void updateAXBExpirationDate(Long driverUserId, Long goodsId);

}
