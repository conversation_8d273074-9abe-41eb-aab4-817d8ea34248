package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.UserCallPhoneRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 * 获取电话记录表（货源与用户关系）每个用户一条数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Mapper
public interface UserCallPhoneRecordMapper extends BaseMapper<UserCallPhoneRecordDO> {

    Integer getUserCallPhoneCount(@Param("userId") Long userId,
                                  @Param("startTime") Date startTime,
                                  @Param("endTime") Date endTime);

    Integer getCallStatusByUserIdAndTsId(@Param("srcMsgId") Long goodsId,
                                         @Param("userId") Long userId,
                                         @Param("startTime") Date startTime);
}
