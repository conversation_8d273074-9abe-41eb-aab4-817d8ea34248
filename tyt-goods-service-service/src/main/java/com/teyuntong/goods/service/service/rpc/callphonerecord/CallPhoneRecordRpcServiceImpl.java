package com.teyuntong.goods.service.service.rpc.callphonerecord;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import com.teyuntong.goods.service.client.callphonerecord.service.CallPhoneRecordRpcService;
import com.teyuntong.goods.service.client.callphonerecord.vo.CallPhoneRecordVo;
import com.teyuntong.goods.service.client.callphonerecord.vo.GetCarPhoneVo;
import com.teyuntong.goods.service.client.callphonerecord.vo.TransportRecordVo;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordTransportCountDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.AppCallLogMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.CallPhoneRecordMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.UserCallPhoneRecordService;
import com.teyuntong.goods.service.service.biz.exposure.service.ExposureCardGiveawayService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.UserDispatchTransportInfoDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.biz.transport.service.UserDispatchTransportInfoService;
import com.teyuntong.goods.service.service.common.enums.DispatchDealStatusEnum;
import com.teyuntong.goods.service.service.biz.transport.service.TransportQuotedPriceService;
import com.teyuntong.goods.service.service.common.enums.RatingTypeEnum;
import com.teyuntong.goods.service.service.common.enums.TransportStatusEnum;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.IdCardUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.NoticePopupTemplRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.market.CouponRemoteService;
import com.teyuntong.goods.service.service.remote.order.FeedBackRemoteService;
import com.teyuntong.goods.service.service.remote.order.TradeRemoteService;
import com.teyuntong.goods.service.service.remote.user.SigningCarRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.infra.basic.resource.client.popup.dto.PopupTypeEnum;
import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.market.activity.client.coupon.vo.VipNoticeForGoodsVo;
import com.teyuntong.trade.service.client.feedBack.dto.UserFeedbackRatingAndLabelDTO;
import com.teyuntong.trade.service.client.orders.dto.UserTradeGoodsTypeRpcVO;
import com.teyuntong.user.service.client.permission.dto.AuthPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.ServicePermissionEnum;
import com.teyuntong.user.service.client.permission.vo.AuthPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.ApiDataUserCreditInfoRpcVO;
import com.teyuntong.user.service.client.user.vo.CreditUserRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant.TRANSMISSION_AB_TEST_KEY;

@Slf4j
@RestController
@RequiredArgsConstructor
public class CallPhoneRecordRpcServiceImpl implements CallPhoneRecordRpcService {

    private final TransportMainService transportMainService;

    private final CallPhoneRecordMapper callPhoneRecordMapper;

    private final AppCallLogMapper appCallLogMapper;

    private final StringRedisTemplate stringRedisTemplate;

    private final UserCallPhoneRecordService userCallPhoneRecordService;

    private final UserDispatchTransportInfoService userDispatchTransportInfoService;

    private final UserPermissionRemoteService userPermissionRemoteService;

    private final UserRemoteService userRemoteService;
    private final NoticePopupTemplRemoteService noticePopupTemplRemoteService;
    private final CouponRemoteService couponRemoteService;


    private final ExposureCardGiveawayService exposureCardGiveawayService;

    private final TytConfigRemoteService tytConfigRemoteService;
    private final ABTestRemoteService abTestRemoteService;

    private final FeedBackRemoteService feedBackRemoteService;

    private final SigningCarRemoteService signingCarRemoteService;

    private final TradeRemoteService tradeRemoteService;

    private final TransportQuotedPriceService transportQuotedPriceService;

    private static final String TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY = "transportCallPhoneRecoredCount";

    @Override
    public List<TransportRecordVo> transportList(Long userId) {

        List<TransportMainDO> myTransportList = transportMainService.getTodayPublishTransport(userId);
        if (CollectionUtils.isEmpty(myTransportList)) {
            return List.of();
        }

        List<Long> srcMsgIdList = myTransportList.stream().filter(t -> !SourceTypeEnum.isNewDispatch(t.getSourceType())).map(TransportMainDO::getSrcMsgId).toList();
        if(CollUtil.isEmpty(srcMsgIdList)){
            return List.of();
        }
        List<CallPhoneRecordTransportCountDO> callRecordList = callPhoneRecordMapper.getRecordBySrcMsgIdList(srcMsgIdList);
        if (CollectionUtils.isEmpty(callRecordList)) {
            return List.of();
        }

        List<Long> giveawayRecord = exposureCardGiveawayService.getGiveawayRecord(srcMsgIdList);
        Map<Long, TransportMainDO> transportMap = myTransportList.stream().collect(Collectors.toMap(TransportMainDO::getId, t -> t));

        return callRecordList.stream().map(callRecord -> {
            TransportRecordVo transportRecordVo = makeTransportRecordVo(callRecord, transportMap.get(callRecord.getSrcMsgId()));
            transportRecordVo.setGiveawayExposureCard(giveawayRecord.contains(callRecord.getSrcMsgId()) ? 1 : 0);
            return transportRecordVo;
        }).toList();
    }

    private TransportRecordVo makeTransportRecordVo(CallPhoneRecordTransportCountDO callRecord, TransportMainDO transportMainDO) {
        TransportRecordVo transportRecordVo = new TransportRecordVo();
        transportRecordVo.setSrcMsgId(callRecord.getSrcMsgId());
        transportRecordVo.setAppCallLogCount(callRecord.getCount());
        transportRecordVo.setStartPoint(transportMainDO.getStartPoint());
        transportRecordVo.setDestPoint(transportMainDO.getDestPoint());
        transportRecordVo.setTaskContent(transportMainDO.getTaskContent());
        transportRecordVo.setStartDetailAdd(transportMainDO.getStartDetailAdd());
        transportRecordVo.setDestDetailAdd(transportMainDO.getDestDetailAdd());
        transportRecordVo.setWeightCode(transportMainDO.getWeightCode());
        transportRecordVo.setWeight(transportMainDO.getWeight());
        transportRecordVo.setLength(transportMainDO.getLength());
        transportRecordVo.setWide(transportMainDO.getWide());
        transportRecordVo.setHigh(transportMainDO.getHigh());
        transportRecordVo.setStartCity(transportMainDO.getStartCity());
        transportRecordVo.setStartProvinc(transportMainDO.getStartProvinc());
        transportRecordVo.setStartArea(transportMainDO.getStartArea());
        transportRecordVo.setDestProvinc(transportMainDO.getDestProvinc());
        transportRecordVo.setDestCity(transportMainDO.getDestCity());
        transportRecordVo.setDestArea(transportMainDO.getDestArea());
        transportRecordVo.setPublishType(transportMainDO.getPublishType());
        transportRecordVo.setRefundFlag(transportMainDO.getRefundFlag());
        transportRecordVo.setSourceType(transportMainDO.getSourceType());

        //构造未查看红点字段
        transportRecordVo.setHaveNoLookCallLog(stringRedisTemplate.opsForHash().hasKey(TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + transportMainDO.getUserId(), callRecord.getSrcMsgId().toString()));
        return transportRecordVo;
    }

    /**
     * 返回拨打次数
     *
     * @param srcMsgId 货源srcMsgId
     * @param userId   车端用户id
     * @return 拨打次数，默认为0
     */
    @Override
    public Integer getCallCount(Long srcMsgId, Long userId) {
        return appCallLogMapper.getCallCountOfGoods(srcMsgId, userId);
    }

    /**
     * 返回拨打次数
     *
     * @param userId    车用户id
     * @param startTime 开始时间
     * @param endTime   截止时间
     * @return 拨打次数，默认为0
     */
    @Override
    public Integer getCallCountOfPeriod(Long userId, Date startTime, Date endTime) {
        return appCallLogMapper.getCallCountOfPeriod(userId, startTime, endTime);
    }

    @Override
    public Integer getUserCallPhoneCount(Long userId, Date startTime, Date endTime) {
        return userCallPhoneRecordService.getUserCallPhoneCount(userId, startTime, endTime);
    }

    @Override
    public GetCarPhoneVo getPhone(Long linkUserId) {
        GetCarPhoneVo getCarPhoneVo = new GetCarPhoneVo();
        LoginUserDTO user = LoginHelper.getRequiredLoginUser();
        // 校验用户有没有拨打货源的权益
        AuthPermissionRpcDTO authPermissionRpcDTO = new AuthPermissionRpcDTO();
        authPermissionRpcDTO.setUserId(user.getUserId());
        authPermissionRpcDTO.setServicePermissionEnum(ServicePermissionEnum.货源回拨联系人);
        AuthPermissionRpcVO authPermissionRpcVO = userPermissionRemoteService.authPermission(authPermissionRpcDTO);
        if (authPermissionRpcVO.isUse()) {
            List<UserRpcVO> linkUserList = userRemoteService.getUserByIdList(List.of(linkUserId));
            if (CollUtil.isNotEmpty(linkUserList)) {
                getCarPhoneVo.setCarPhone(linkUserList.get(0).getCellPhone());
            } else {
                throw BusinessException.createException(GoodsErrorCode.ERROR_NO_PARAM.getCode(), "要回拨车方用户错误");
            }
        } else {
            log.info("用户{}没有回拨联系人的权益", user.getUserId());
            VipNoticeForGoodsVo noticeVo = null;
            try {
                Integer userType = abTestRemoteService.getUserType(TRANSMISSION_AB_TEST_KEY, user.getUserId());
                if (Objects.equals(userType, YesOrNoEnum.YES.getId())) {
                    noticeVo = couponRemoteService.getVipNoticeForGoodsPublish(linkUserId);
                }
            } catch (Exception e) {
                log.error("货源回拨调用market-service失败,userId:{}", user.getUserId());
            }
            if (noticeVo != null) {
                throw new BusinessException(GoodsErrorCode.PUBLISH_NO_PERMISSION_NOTICE, null, noticeVo);
            } else {
                PopupTypeEnum popupTypeEnum = PopupTypeEnum.getByName(authPermissionRpcVO.getPermissionPopupTypeEnum().name());
                NoticePopupTemplVo noticePopupTemplVo = noticePopupTemplRemoteService.getByType(popupTypeEnum);
                throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
            }
        }
        return getCarPhoneVo;
    }

    @Override
    public GetCarPhoneVo getPhoneNoAuth(Long linkUserId) {
        LoginUserDTO user = LoginHelper.getRequiredLoginUser();
        log.info("货方用户：" + user.getUserId() + " 通过无权限校验接口获取车方用户：" + linkUserId + " 手机号");
        GetCarPhoneVo getCarPhoneVo = new GetCarPhoneVo();
        List<UserRpcVO> linkUserList = userRemoteService.getUserByIdList(List.of(linkUserId));
        if (CollUtil.isNotEmpty(linkUserList)) {
            getCarPhoneVo.setCarPhone(linkUserList.get(0).getCellPhone());
        }
        return getCarPhoneVo;
    }

    @Override
    public List<CallPhoneRecordVo> contactedList(Long srcMsgId, Long userId) {
        List<CallPhoneRecordVo> callPhoneRecordVos = new ArrayList<>();
        if (srcMsgId == null) {
            return callPhoneRecordVos;
        }
        TransportMainDO transportMainDO = transportMainService.getById(srcMsgId);
        if (transportMainDO == null || !Objects.equals(transportMainDO.getStatus(), TransportStatusEnum.VALID.getCode())) {
            return callPhoneRecordVos;
        }
        List<CallPhoneRecordDO> callPhoneRecords = callPhoneRecordMapper.getCallPhoneRecords(srcMsgId);
        callPhoneRecordVos = this.increaseVo(callPhoneRecords);
        String tytServerPictureUrl = tytConfigRemoteService.getStringValue("tyt_server_picture_url_old", "http://www.teyuntong.com/rootdata");

        List<Long> payUserIdList = Opt.ofEmptyAble(callPhoneRecordVos).stream().flatMap(List::stream).map(CallPhoneRecordVo::getCarUserId).toList();
        Map<Long, Long> goodsPositiveNumMap = feedBackRemoteService.getGoodsPositiveNum(userId, payUserIdList);

        for (CallPhoneRecordVo callPhoneRecord : callPhoneRecordVos) {
            UserRpcVO user = userRemoteService.getUser(callPhoneRecord.getCarUserId());

            List<CreditUserRpcVO> creditUserInfos = userRemoteService.
                    getPayUserInfos(Collections.singletonList(callPhoneRecord.getCarUserId()), userId);
            if (CollectionUtils.isNotEmpty(creditUserInfos)) {
                CreditUserRpcVO creditUserInfo = creditUserInfos.get(0);
                callPhoneRecord.setCoopNums(creditUserInfo.getCoopNums());
                callPhoneRecord.setTradeNums(creditUserInfo.getTradeNums());
            }

            Long goodsPositiveReviewNum = goodsPositiveNumMap.getOrDefault(callPhoneRecord.getCarUserId(), 0L);
            callPhoneRecord.setGoodsPositiveReviewNum(goodsPositiveReviewNum);

            try {
                if (StringUtils.isNotBlank(user.getIdCard())) {
                    String showName = callPhoneRecord.getCarUserName().charAt(0) + IdCardUtil.getCallGender(user.getIdCard());
                    callPhoneRecord.setCarUserName(showName);
                } else {
                    callPhoneRecord.setCarUserName(callPhoneRecord.getCarUserName());
                }
            } catch (Exception e) {
                log.error("contactedList setCarUserName error", e);
            }


            //返回拼好的头像
            if (user != null && StringUtils.isNotBlank(user.getHeadUrl())) {
                String pattern = "^(http|https)";
                Pattern compiledPattern = Pattern.compile(pattern);
                Matcher matcher = compiledPattern.matcher(user.getHeadUrl());
                if (!matcher.find()) {
                    callPhoneRecord.setHeadUrl(tytServerPictureUrl + user.getHeadUrl());
                } else {
                    callPhoneRecord.setHeadUrl(user.getHeadUrl());
                }
            }

            //返回是否实名认证
            if (user.getVerifyPhotoSign() != null && user.getVerifyPhotoSign() == 1) {
                callPhoneRecord.setRealNameAuthentication(true);
            } else {
                callPhoneRecord.setRealNameAuthentication(false);
            }

            //查询车方的好评率和标签
            UserFeedbackRatingAndLabelDTO userFeedbackRatingAndLabel = feedBackRemoteService.getUserFeedbackRatingAndLabel(callPhoneRecord.getCarUserId(), 1);
            if (userFeedbackRatingAndLabel != null) {
                callPhoneRecord.setRating(userFeedbackRatingAndLabel.getRating());
                callPhoneRecord.setPositiveCount(userFeedbackRatingAndLabel.getPositiveCount());
                callPhoneRecord.setPositiveLabels(userFeedbackRatingAndLabel.getPositiveLabels());
                callPhoneRecord.setNegativeLabels(userFeedbackRatingAndLabel.getNegativeLabels());
                // 30天保护期
                if (DateUtils.addDays(user.getCtime() == null ? new Date() : user.getCtime(), 30).before(new Date())) {
                    //总评价量
                    Long total = userFeedbackRatingAndLabel.getTotal();
                    //好评数
                    Long positiveCount = userFeedbackRatingAndLabel.getPositiveCount();
                    if (total == 0) {
                        callPhoneRecord.setRatingType(RatingTypeEnum.RECENT_NO_RATE.getCode());
                    } else if (total > 0 && total < 3) {
                        //有好评
                        if (positiveCount > 0) {
                            callPhoneRecord.setRatingType(RatingTypeEnum.RECENT_RECEIVE_POSITIVE.getCode());
                        } else { //无好评
                            callPhoneRecord.setRatingType(RatingTypeEnum.RECENT_NOT_RECEIVE_POSITIVE.getCode());
                        }
                    } else if (total >= 3) {
                        callPhoneRecord.setRatingType(RatingTypeEnum.SHOW_RATING.getCode());
                    }
                } else {
                    callPhoneRecord.setRating(null);
                    callPhoneRecord.setRatingType(RatingTypeEnum.NOT_SHOW_RATING.getCode());
                    callPhoneRecord.setNegativeLabels(Collections.emptyList());
                }

            }

            //返回备注字段
            if (callPhoneRecord.getSrcMsgId() != null && callPhoneRecord.getCarUserId() != null) {
                String callPhoneRecordsRemark = getCallPhoneRecordsRemark(callPhoneRecord.getSrcMsgId(), callPhoneRecord.getCarUserId());
                if (StringUtils.isNotBlank(callPhoneRecordsRemark)) {
                    callPhoneRecord.setRemark(callPhoneRecordsRemark);
                }
            }

            // 专车车主标签
            Boolean isSigningCar = signingCarRemoteService.checkValidSignintCar(callPhoneRecord.getCarUserId());
            callPhoneRecord.setSigningCar(isSigningCar);

            // 货类经验标签
            String carGoodsTypeLabel = transportQuotedPriceService.getCarGoodsTypeLabel(callPhoneRecord.getCarUserId(), transportMainDO.getGoodTypeName());
            callPhoneRecord.setCarGoodsTypeLabel(carGoodsTypeLabel);
        }

        //获取意向车源数据后把未查看的数量去掉
        //货主查看意向车源，就需要把未查看的数量去掉
        String redisKey = TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY + ":" + userId;
        if (stringRedisTemplate.hasKey(redisKey)
                && stringRedisTemplate.opsForHash().hasKey(redisKey, srcMsgId.toString())) {
            //将该货源的货主未浏览通话记录次数清空
            stringRedisTemplate.opsForHash().delete(redisKey, srcMsgId.toString());
        }
        return callPhoneRecordVos;
    }

    private List<CallPhoneRecordVo> increaseVo(List<CallPhoneRecordDO> callPhoneRecords) {
        List<CallPhoneRecordVo> callPhoneRecordVos = new ArrayList<>();

        for (CallPhoneRecordDO callPhoneRecord : callPhoneRecords) {
            CallPhoneRecordVo callPhoneRecordVo = new CallPhoneRecordVo();
            BeanUtils.copyProperties(callPhoneRecord, callPhoneRecordVo);
            ApiDataUserCreditInfoRpcVO userCreditInfo = userRemoteService.getUserCreditInfo(callPhoneRecord.getCarUserId());
            if (userCreditInfo != null) {
                if (null != userCreditInfo.getCarTotalServerScore()) {
                    callPhoneRecordVo.setCarTotalServerScore(userCreditInfo.getCarTotalServerScore());
                }
                if (null != userCreditInfo.getCarServerRankScore()) {
                    callPhoneRecordVo.setCarServerRankScore(userCreditInfo.getCarServerRankScore());
                }
                callPhoneRecordVo.setCarCreditRankLevel(userCreditInfo.getCarCreditRankLevel());
            }
            callPhoneRecordVos.add(callPhoneRecordVo);
        }
        return callPhoneRecordVos;
    }

    private String getCallPhoneRecordsRemark(Long srcMsgId, Long carUserId) {
        return callPhoneRecordMapper.getCallPhoneRecordsRemark(srcMsgId, carUserId);
    }

}
