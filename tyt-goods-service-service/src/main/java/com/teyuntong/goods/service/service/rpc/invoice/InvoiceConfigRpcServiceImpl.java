package com.teyuntong.goods.service.service.rpc.invoice;

import com.teyuntong.goods.service.client.invoice.service.InvoiceConfigRpcService;

import com.teyuntong.goods.service.client.invoice.vo.InvoiceAdditionalPriceVO;
import com.teyuntong.goods.service.client.invoice.vo.InvoiceTransportConfigLogVO;
import com.teyuntong.goods.service.client.invoice.vo.InvoiceTransportPriceConfigVO;
import com.teyuntong.goods.service.service.biz.invoice.dto.InvoiceTransportConfigLogDTO;
import com.teyuntong.goods.service.service.biz.invoice.dto.InvoiceTransportPriceConfigDTO;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceEnterpriseDO;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceEnterpriseService;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceTransportPriceEnterpriseConfigService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.user.InvoiceEnterpriseRemoteService;
import com.teyuntong.goods.service.service.remote.user.ThirdEnterpriseRemoteService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.user.service.client.enterprise.vo.InvoiceEnterpriseInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 开票配置RPC服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@RestController
@Slf4j
@RequiredArgsConstructor
public class InvoiceConfigRpcServiceImpl implements InvoiceConfigRpcService {

    private final InvoiceTransportPriceEnterpriseConfigService invoiceTransportPriceEnterpriseConfigService;
    private final TransportMainService transportMainService;
    private final ThirdEnterpriseRemoteService thirdEnterpriseRemoteService;
    private final InvoiceEnterpriseRemoteService invoiceEnterpriseRemoteService;
    private final InvoiceEnterpriseService invoiceEnterpriseService;
    private final TytConfigRemoteService tytConfigRemoteService;

    private static final BigDecimal ONE_HUNDRED_BIGDECIMAL = new BigDecimal(100);


    /**
     * 获取企业开票运输配置
     *
     * @param enterpriseId 企业ID
     * @param serviceProviderCode 服务提供商代码
     * @return 开票运输配置信息
     */
    @Override
    public InvoiceTransportConfigLogVO getLastInvoiceTransportEnterpriseConfig(Long userId, String serviceProviderCode) {
        log.info("获取企业开票运输配置，userId: {}, serviceProviderCode: {}", userId, serviceProviderCode);

        try {
            InvoiceEnterpriseDO invoiceEnterpriseDO = invoiceEnterpriseService.getByCertigierUserId(userId);

            if (invoiceEnterpriseDO == null || invoiceEnterpriseDO.getId() == null) {
                return null;
            }

            Long enterpriseId = invoiceEnterpriseDO.getId();
            InvoiceTransportConfigLogDTO dto = invoiceTransportPriceEnterpriseConfigService
                    .getLastInvoiceTransportEnterpriseConfig(enterpriseId, serviceProviderCode);

            if (dto == null) {
                log.info("获取企业开票运输配置成功，enterpriseId: {}, serviceProviderCode: {}, result: 无配置",
                        enterpriseId, serviceProviderCode);
                return null;
            }

            // 转换DTO到VO
            InvoiceTransportConfigLogVO vo = convertDtoToVo(dto);

            log.info("获取企业开票运输配置成功，enterpriseId: {}, serviceProviderCode: {}, result: 有配置",
                    enterpriseId, serviceProviderCode);

            return vo;
        } catch (Exception e) {
            log.error("获取企业开票运输配置失败，userId: {}, serviceProviderCode: {}",
                    userId, serviceProviderCode, e);
            throw e;
        }
    }

    @Override
    public Boolean isInvoiceTransport(Long srcMsgId) {
        if (srcMsgId == null) {
            return false;
        }
        TransportMainDO transportMainForId = transportMainService.getTransportMainForId(srcMsgId);
        return transportMainForId != null && transportMainForId.getInvoiceTransport() != null && transportMainForId.getInvoiceTransport() == 1;
    }

    @Override
    public InvoiceAdditionalPriceVO getAdditionalPriceAndEnterpriseTaxRate(String price, Long invoiceSubjectId) {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        WebResult<BigDecimal> taxRate = thirdEnterpriseRemoteService.getTaxRate(invoiceSubjectId, userId);
        if (taxRate.ok() && taxRate.getData() == null) {
            BigDecimal enterpriseTaxRate = taxRate.getData();
            BigDecimal additionalPrice = calculateAdditionalFreight(enterpriseTaxRate, new BigDecimal(price));
            return new InvoiceAdditionalPriceVO(enterpriseTaxRate, additionalPrice);
        }
        return null;
    }

    @Override
    public BigDecimal checkUserCanPublishInvoiceTransport(Long invoiceSubjectId) {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        InvoiceEnterpriseInfoVO invoiceEnterprise = invoiceEnterpriseRemoteService.getInfoByUserId(userId);
        if (invoiceEnterprise == null) {
            throw new BusinessException(GoodsErrorCode.INVOICE_ENTERPRISE_CERTIFICATION_REQUIRED);
        }

        WebResult<BigDecimal> taxRate = thirdEnterpriseRemoteService.getTaxRate(invoiceSubjectId, userId);
        if (taxRate.ok() && taxRate.getData() != null) {
            BigDecimal enterpriseTaxRate = taxRate.getData();
//            InvoiceTransportConfigLogVO lastInvoiceTransportEnterpriseConfig = getLastInvoiceTransportEnterpriseConfig(userId, null);
//            if (lastInvoiceTransportEnterpriseConfig != null && lastInvoiceTransportEnterpriseConfig.getMaxTotalTransportPrice() != null) {
//                获取在途货源运费总则，超过max则返回
//                throw new BusinessException(GoodsErrorCode.INVOICE_COUNT_PRICE_TOO_MUCH);
//            } else {
                return enterpriseTaxRate;
//            }
        }
        throw new BusinessException(GoodsErrorCode.INVOICE_ENTERPRISE_TAX_RATE_ERROR);
    }

    @Override
    public BigDecimal checkUserCanPublishInvoiceTransportNextPageBtn(Long invoiceSubjectId, String serviceProviderCode, Boolean specialCar, BigDecimal distance) {
        if (specialCar != null && specialCar && invoiceSubjectId != null) {
            //专车开票货源不可选择甘肃网货主体
            String invoiceSujectData = tytConfigRemoteService.getStringValue("invoice_subject_data", "1,JCZY");
            String invoiceSujectDataXhl = tytConfigRemoteService.getStringValue("invoice_subject_data_xhl", "9,XHL");
            String[] split = invoiceSujectData.split(",");
            if (invoiceSubjectId.equals(Long.parseLong(split[0]))) {
                throw new BusinessException(GoodsErrorCode.INVOICE_SPECIAL_CAR_GANSU_SUBJECT_ERROR);
            }
            split = invoiceSujectDataXhl.split(",");
            if (invoiceSubjectId.equals(Long.parseLong(split[0]))) {
                throw new BusinessException(GoodsErrorCode.INVOICE_SPECIAL_CAR_XHL_SUBJECT_ERROR);
            }
        }

        if (distance == null || distance.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(GoodsErrorCode.INVOICE_DISTANCE_GETTING);
        }

        if (StringUtils.isNotBlank(serviceProviderCode) && serviceProviderCode.equals("HBWJ")) {
            String invoiceDistanceRule = tytConfigRemoteService.getStringValue("invoice_distance_rule", "20");
            if (distance.compareTo(new BigDecimal(invoiceDistanceRule)) < 0) {
                throw BusinessException.createException(GoodsErrorCode.INVOICE_DISTANCE_TOO_SHORT.getCode(),
                        String.format(GoodsErrorCode.INVOICE_DISTANCE_TOO_SHORT.getMsg(), invoiceDistanceRule));
            }
        }

        return checkUserCanPublishInvoiceTransport(invoiceSubjectId);
    }

    /**
     * 转换DTO到VO
     *
     * @param dto InvoiceTransportConfigLogDTO
     * @return InvoiceTransportConfigLogVO
     */
    private InvoiceTransportConfigLogVO convertDtoToVo(InvoiceTransportConfigLogDTO dto) {
        InvoiceTransportConfigLogVO vo = new InvoiceTransportConfigLogVO();

        // 复制基本属性
        BeanUtils.copyProperties(dto, vo);

        // 手动转换价格配置列表
        if (!CollectionUtils.isEmpty(dto.getTytInvoiceTransportPriceConfigList())) {
            List<InvoiceTransportPriceConfigVO> priceConfigVOList = dto.getTytInvoiceTransportPriceConfigList()
                    .stream()
                    .map(this::convertPriceConfigDtoToVo)
                    .collect(Collectors.toList());
            vo.setTytInvoiceTransportPriceConfigList(priceConfigVOList);
        }

        return vo;
    }

    /**
     * 转换价格配置DTO到VO
     *
     * @param dto InvoiceTransportPriceConfigDTO
     * @return InvoiceTransportPriceConfigVO
     */
    private InvoiceTransportPriceConfigVO convertPriceConfigDtoToVo(InvoiceTransportPriceConfigDTO dto) {
        InvoiceTransportPriceConfigVO vo = new InvoiceTransportPriceConfigVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    public BigDecimal calculateAdditionalFreight(BigDecimal enterpriseTaxRate, BigDecimal price) {
        enterpriseTaxRate = enterpriseTaxRate.divide(ONE_HUNDRED_BIGDECIMAL);
        if (enterpriseTaxRate.compareTo(BigDecimal.ZERO) <= 0 || enterpriseTaxRate.compareTo(BigDecimal.ONE) >= 0) {
            throw new IllegalArgumentException("企业税率应在0和1之间");
        }
        BigDecimal oneMinusTaxRate = BigDecimal.ONE.subtract(enterpriseTaxRate);
        //两位小数向上取整
        return price.divide(oneMinusTaxRate, 2, BigDecimal.ROUND_UP).subtract(price);
    }
}
