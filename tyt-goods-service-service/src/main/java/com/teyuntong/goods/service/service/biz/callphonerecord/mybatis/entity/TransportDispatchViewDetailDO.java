package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货源查看、联系详情表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-04-10
 */
@Getter
@Setter
@TableName("tyt_transport_dispatch_view_detail")
public class TransportDispatchViewDetailDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 车主id
     */
    private Long carUserId;

    /**
     * 车主姓名
     */
    private String carUserName;

    /**
     * 车主昵称
     */
    private String carNickName;

    /**
     * 车主手机号
     */
    private String carPhone;

    /**
     * 类型（1查看；2联系）
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

}
