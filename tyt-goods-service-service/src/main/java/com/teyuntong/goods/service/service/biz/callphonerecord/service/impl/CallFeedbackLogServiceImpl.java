package com.teyuntong.goods.service.service.biz.callphonerecord.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallFeedbackLogDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.CallFeedbackLogMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.CallFeedbackLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 拨打反馈页面填写记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CallFeedbackLogServiceImpl extends ServiceImpl<CallFeedbackLogMapper, CallFeedbackLogDO> implements CallFeedbackLogService {

    private final CallFeedbackLogMapper callFeedbackLogMapper;

    /**
     * 获取车主的货源反馈记录
     */
    @Override
    public CallFeedbackLogDO getLastFeedback(Long carUserId, Long srcMsgId) {
        List<CallFeedbackLogDO> lastFeedback = callFeedbackLogMapper.getLastFeedback(carUserId, List.of(srcMsgId));
        return lastFeedback.isEmpty() ? null : lastFeedback.get(0);
    }

    /**
     * 批量获取车主的货源反馈记录
     *
     * @return {srcMsgId:"最新一条反馈记录"}
     */
    @Override
    public Map<Long, String> getLastFeedback(Long carUserId, List<Long> srcMsgIds) {
        if (carUserId == null || CollectionUtils.isEmpty(srcMsgIds)) {
            return Map.of();
        }
        List<CallFeedbackLogDO> lastFeedback = callFeedbackLogMapper.getLastFeedback(carUserId, srcMsgIds);
        Map<Long, String> map = new HashMap<>();
        for (CallFeedbackLogDO t : lastFeedback) {
            if (!map.containsKey(t.getSrcMsgId())) {
                // 优先二级选项，为空返回一级选项
                map.put(t.getSrcMsgId(), StringUtils.isNotBlank(t.getFeedback2()) ? t.getFeedback2() : t.getFeedback1());
            }
        }
        return map;
    }

    /**
     * 是否填写过反馈页面
     */
    @Override
    public Integer hasFeedback(Long carUserId, Long srcMsgId) {
        List<CallFeedbackLogDO> lastFeedback = callFeedbackLogMapper.getLastFeedback(carUserId, List.of(srcMsgId));
        return lastFeedback.isEmpty() ? 0 : 1;
    }
}
