package com.teyuntong.goods.service.service.biz.callphonerecord.bean;

import com.teyuntong.goods.service.client.callphonerecord.vo.PrivacyPhoneTabInfo;
import lombok.Data;

@Data
public class CellPhoneListBean {
    private String tel;  //联系人1
    private String tel3; //联系人2
    private String tel4;  //联系人3
    private String uploadCellPhone;//发货人账号

    /**
     * 装货联系电话
     */
    private String loadCellPhone;

    /**
     * 卸货联系电话
     */
    private String unloadCellPhone;
    private Long srcMsgId;// 原货物唯一标识
    private String tsOrderNo;
    private String isInfoFee;
    private String callStatus;
    private Integer callStatusCode = 0;
    private Integer isCanCall = 0;  //是否可以打电话，0可以，1不可以
    private String hasMakeOrder;// 是否下过单:0否、1是
    private Long userId;
    private Integer goodStatus;
    /**
     * 拨打电话时提示框提示信息
     */
    private String noGoodsMemberText;

    /**
     * 接单限制信息
     */
    private AcceptOrderLimitInfo acceptOrderLimitInfo;

    private PrivacyPhoneTabInfo privacyPhoneTabInfo;

}
