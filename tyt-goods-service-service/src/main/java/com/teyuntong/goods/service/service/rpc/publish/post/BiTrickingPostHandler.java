package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.tricking.service.EventTrickingService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 直接发布BI数据埋点
 *
 * <AUTHOR>
 * @since 2025/02/23 17:39
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BiTrickingPostHandler {

    private final EventTrickingService eventTrickingService;

    public void directPublish(DirectPublishProcessBO processBO) {
        eventTrickingService.directPublish(processBO);
    }

    @Async("threadPoolExecutor")
    public void savePublishTricking(PublishProcessBO processBO, PublishBO publishBO) {
        eventTrickingService.savePublishTricking(processBO,publishBO);
    }
}
