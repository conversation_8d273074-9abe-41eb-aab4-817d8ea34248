package com.teyuntong.goods.service.service.biz.callphonerecord.bean;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.teyuntong.trade.service.client.feedBack.dto.UserFeedbackRatingAndLabelDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * transport 相关表中 label_json 内部属性
 * 在命名规范的前提下，尽量缩短字段名称
 *
 * <AUTHOR>
 * @date 2023/1/29 13:54
 */
@Data
public class TransportLabelJson {

    /**
     * 用户标签名称
     */
    private String goodService;

    /**
     * 用户标签id，0:无标签，1:正向（服务好），2:负向（客诉多）
     */
    private Integer userLabelIcon;

    /**
     * 加价次数.
     */
    private Integer addPriceCount;

    private UserFeedbackRatingAndLabelDTO feedbackLabel;

    //用户认证状态（1通过）
    private Integer userAuthStatus;

    //企业认证状态（1通过）
    private Integer enterpriseAuthStatus;

    /**
     * 调用BI优车好货接口返回结果
     */
    private Integer iGBIResultData;

    /**
     * 调用BI优车好货接口返回结果的分数
     */
    private BigDecimal goodsModelScore;

    /**
     * 优车运价货源 1:是；null：否
     */
    private Integer goodCarPriceTransport;

    /**
     * 是否抽佣货源 1:是；0:否
     */
    private Integer commissionTransport;

    /**
     * 是否满足抽佣条件 0：否 1：是 2:首单免佣
     */
    private Integer meetCommissionRules;

    /**
     * 是否是参与现金奖活动的货源 1:是；0:否
     */
    private Integer isCashPrizeActivityGoods;

    /**
     * 是否是抽佣货源
     *
     * @return
     */
    public boolean isCommissionTransport() {
        return Objects.equals(commissionTransport, 1);
    }
    /**
     * 经分给的低吨重货源的不抽佣原因
     */
    private String commissionLabel;

    /**
     * 是否是手动填写的技术服务费 1是；0否
     * 代调发货，使用客户端的技术服务费
     */
    private Integer isManualTecServiceFee;

    /**
     * 价格标签 :0.无标签 1.该货源价格高于历史成交均价
     */
    private Integer priceLabel;

    /**
     * 是否是秒抢货源变成非秒抢 1:是；0:否
     */
    private Integer seckillDowngrade;


    /**
     * 是否显示超额保障标签 1是 0否
     */
    private Integer showExcessCoverageLabel = 0;


    @JsonIgnore
    @JSONField(serialize = false)
    public String getJsonText() {
        String jsonText = JSONUtil.toJsonStr(this);

        if (jsonText.equals("{}")) {
            jsonText = "";
        }
        return jsonText;
    }


}
