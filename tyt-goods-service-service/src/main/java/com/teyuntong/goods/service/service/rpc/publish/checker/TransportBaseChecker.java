package com.teyuntong.goods.service.service.rpc.publish.checker;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.teyuntong.goods.service.client.transport.dto.GoodsPointDTO;
import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportHistoryService;
import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.INFE_FEE_PRICE_LIMIT_RATE;

/**
 * 拼车货源校验是否满足拼车条件
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportBaseChecker {

    private final TytConfigRemoteService tytConfigRemoteService;
    private final TransportHistoryService transportHistoryService;



    /**
     * 校验货源参数
     */
    public void checkBaseParams(PublishBO publishBO) {

        // 校验装卸货时间
        if ((publishBO.getLoadingTime() != null && new Date().compareTo(publishBO.getLoadingTime()) > 0) || (publishBO.getUnloadTime() != null && new Date().compareTo(publishBO.getUnloadTime()) > 0)) {
            throw new BusinessException(GoodsErrorCode.LOADING_TIME_EXPIRE);
        }

        // 订金在不可退的情况下，订金不能大于等于运费
        if (Objects.equals(publishBO.getRefundFlag(), 0) && publishBO.getInfoFee() != null && TransportUtil.hasPrice(publishBO.getPrice())) {
            BigDecimal price = new BigDecimal(publishBO.getPrice());
            if (publishBO.getInfoFee().compareTo(price) >= 0) {
                throw new BusinessException(GoodsErrorCode.INFO_FEE_TOO_HIGH);
            }

            // 订金在不可退的情况下，订金不能大于限制价格=X*运费
            String limitRate = tytConfigRemoteService.getStringValue(INFE_FEE_PRICE_LIMIT_RATE, "1");
            if (publishBO.getInfoFee().compareTo(price.multiply(new BigDecimal(limitRate))) > 0) {
                throw BusinessException.createException(GoodsErrorCode.INFO_FEE_TOO_HIGH.getCode(), "订金过高，请重新填写");
            }
        }
    }

    public void checkGoodsStatusForOnlineEdit(TransportMainDO oldTransportMain, Integer dispatchTransport, Integer dispatchType) {
        if (oldTransportMain == null){
            throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
        }
        if(dispatchTransport.equals(YesOrNoEnum.YES.getId()) && !oldTransportMain.getSourceType().equals(SourceTypeEnum.NEW_DISPATCH.getCode())){
            throw BusinessException.createException(GoodsErrorCode.GOODS_STATUS_ERROR.getCode(), "货源已取消授权");
        }
        if (dispatchTransport.equals(YesOrNoEnum.YES.getId()) && dispatchType.equals(DispatchTypeEnum.DISPATCH_TAKE_PUBLISH.getCode())){
            if (!Objects.equals(oldTransportMain.getStatus(), TransportStatusEnum.CONFIRMING.getCode()) || oldTransportMain.getCtime().before(DateUtil.beginOfDay(new Date()))){
                throw BusinessException.createException(GoodsErrorCode.GOODS_STATUS_ERROR.getCode(), "货源不是待发布状态，无法转发");
            }
        }else {
            if (Objects.equals(oldTransportMain.getStatus(), GoodsStatusEnum.DONE.getCode())){
                throw BusinessException.createException(GoodsErrorCode.GOODS_STATUS_ERROR.getCode(), "货源已成交，不可编辑");
            }
            if (Objects.equals(oldTransportMain.getStatus(), GoodsStatusEnum.CANCEL.getCode())){
                throw BusinessException.createException(GoodsErrorCode.GOODS_STATUS_ERROR.getCode(), "货源已撤销，不可编辑");
            }
            if (oldTransportMain.getCtime().before(DateUtil.beginOfDay(new Date()))){
                throw BusinessException.createException(GoodsErrorCode.GOODS_STATUS_ERROR.getCode(), "货源已过期，不可编辑");
            }
            if (!Objects.equals(oldTransportMain.getStatus(), GoodsStatusEnum.PUBLISHING.getCode())){
                throw new BusinessException(GoodsErrorCode.GOODS_STATUS_ERROR);
            }
            if (oldTransportMain.getSourceType().equals(SourceTypeEnum.YMM.getCode())){
                throw BusinessException.createException(GoodsErrorCode.YMM_PUBLISH_ERROR.getCode(), "运满满货源暂不支持编辑");
            }
        }
    }

    /**
     * 检查货源在线编辑次数
     */
    public void checkOnlineEditTimes(Long srcMsgId) {
        String value = tytConfigRemoteService.getStringValue(ConfigKeyConstant.GOODS_ONLINE_EDIT_CONFIG_KEY);
        if (StringUtils.isBlank(value) || !NumberUtil.isInteger(value)){
            return;
        }
        int times = Integer.parseInt(value);
        if (times <= 0){
            return;
        }
        int editTimes = transportHistoryService.getGoodsOnlineEditTimes(srcMsgId);
        if (editTimes >= times){
            throw new BusinessException(GoodsErrorCode.GOODS_ONLINE_EDIT_TIMES_ERROR);
        }
    }




}
