package com.teyuntong.goods.service.service.rpc.callphonerecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.callphonerecord.service.AxbRpcService;
import com.teyuntong.goods.service.client.callphonerecord.vo.PrivacyPhoneNumGoodIdReq;
import com.teyuntong.goods.service.client.callphonerecord.vo.PrivacyPhoneTabInfo;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.PrivacyPhoneNumService;
import com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TransportTecServiceFeeDO;
import com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TransportTecServiceFeeMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.order.InfoFeeRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.ABTestVo;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.permission.dto.UserPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.vo.UserPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RestController
@RequiredArgsConstructor
@Slf4j
public class AxbRpcServiceImpl implements AxbRpcService {

    private final TransportMainService transportMainService;

    private final TytConfigRemoteService tytConfigRemoteService;

    private final PrivacyPhoneNumService privacyPhoneNumService;

    private final UserRemoteService userRemoteService;

    private final UserPermissionRemoteService userPermissionRemoteService;

    private final TransportTecServiceFeeMapper transportTecServiceFeeMapper;

    private final ABTestRemoteService abTestRemoteService;

    private final InfoFeeRemoteService infoFeeRemoteService;

    private final OrdersRemoteService ordersRemoteService;

    @Override
    public String getPrivacyPhoneNumCToT(PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq) {
        log.info("绑定虚拟号C2T请求参数：{}", JSONObject.toJSONString(privacyPhoneNumGoodIdReq));
        if (privacyPhoneNumGoodIdReq == null || privacyPhoneNumGoodIdReq.getGoodId() == null || privacyPhoneNumGoodIdReq.getOperateType() == null
                || privacyPhoneNumGoodIdReq.getGoodUserPhoneIDX() == null || StringUtils.isBlank(privacyPhoneNumGoodIdReq.getDriverUserPhone())) {
            log.info("请求参数错误");
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        TransportMainDO transportMainDO = transportMainService.getById(privacyPhoneNumGoodIdReq.getGoodId());
        if (transportMainDO == null) {
            log.info("货源不存在");
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        String goodUserPhone = privacyPhoneNumGoodIdReq.getGoodUserPhoneIDX();
        if (StringUtils.isBlank(goodUserPhone)) {
            log.info("货主手机号请求参数错误");
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }

        //是否使用虚拟号开关
        int isUsePrivacyPhoneNum = tytConfigRemoteService.getIntValue("USE_PRIVACY_PHONE_NUM", 1);
        if (isUsePrivacyPhoneNum != 1) {
            return goodUserPhone;
        }

        privacyPhoneNumGoodIdReq.setDriverUserId(privacyPhoneNumGoodIdReq.getUserId());
        privacyPhoneNumGoodIdReq.setGoodUserPhone(goodUserPhone);
        log.info("getPrivacyPhoneNumByGoodId GoodId is 【{}】, GoodUserPhone is 【{}】, DriverUserPhone is 【{}】", privacyPhoneNumGoodIdReq.getGoodId(), privacyPhoneNumGoodIdReq.getGoodUserPhone(), privacyPhoneNumGoodIdReq.getDriverUserPhone());
        String resultMsgBeanByGetPrivacyPhoneNum = privacyPhoneNumService.getPrivacyPhoneNum(privacyPhoneNumGoodIdReq);
        if (StringUtils.isBlank(resultMsgBeanByGetPrivacyPhoneNum)) {
            log.info("获取虚拟号失败 GoodId is 【{}】, GoodUserPhone is 【{}】, DriverUserPhone is 【{}】", privacyPhoneNumGoodIdReq.getGoodId(), privacyPhoneNumGoodIdReq.getGoodUserPhone(), privacyPhoneNumGoodIdReq.getDriverUserPhone());
            //绑定虚拟号失败则直接返回真实手机号，保证可用性
            return goodUserPhone;
        } else {
            return resultMsgBeanByGetPrivacyPhoneNum;
        }
    }

    @Override
    public String getPrivacyPhoneNumTToC(PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq) {
        log.info("绑定虚拟号T2C请求参数：{}", JSONObject.toJSONString(privacyPhoneNumGoodIdReq));
        if (privacyPhoneNumGoodIdReq == null || privacyPhoneNumGoodIdReq.getGoodId() == null || privacyPhoneNumGoodIdReq.getOperateType() == null
                || StringUtils.isBlank(privacyPhoneNumGoodIdReq.getGoodUserPhone()) || privacyPhoneNumGoodIdReq.getDriverUserId() == null) {
            log.info("请求参数错误");
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        TransportMainDO transportMainDO = transportMainService.getById(privacyPhoneNumGoodIdReq.getGoodId());
        if (transportMainDO == null) {
            log.info("货源不存在");
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        String driverUserPhone = null;

        try {
            UserRpcVO user = userRemoteService.getUser(privacyPhoneNumGoodIdReq.getDriverUserId());
            if (user != null) {
                driverUserPhone = user.getCellPhone();
            }
        } catch (Exception e) {
            log.info("获取车主手机号错误");
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        if (StringUtils.isBlank(driverUserPhone)) {
            log.info("车主手机号请求参数错误");
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }

        //是否使用虚拟号开关
        int isUsePrivacyPhoneNum = tytConfigRemoteService.getIntValue("USE_PRIVACY_PHONE_NUM", 1);
        if (isUsePrivacyPhoneNum != 1) {
            return driverUserPhone;
        }

        privacyPhoneNumGoodIdReq.setDriverUserPhone(driverUserPhone);
        log.info("getPrivacyPhoneNumByGoodId GoodId is 【{}】, GoodUserPhone is 【{}】, DriverUserPhone is 【{}】", privacyPhoneNumGoodIdReq.getGoodId(), privacyPhoneNumGoodIdReq.getGoodUserPhone(), privacyPhoneNumGoodIdReq.getDriverUserPhone());
        String resultMsgBeanByGetPrivacyPhoneNum = privacyPhoneNumService.getPrivacyPhoneNum(privacyPhoneNumGoodIdReq);
        if (StringUtils.isBlank(resultMsgBeanByGetPrivacyPhoneNum)) {
            log.info("获取虚拟号失败 GoodId is 【{}】, GoodUserPhone is 【{}】, DriverUserPhone is 【{}】", privacyPhoneNumGoodIdReq.getGoodId(), privacyPhoneNumGoodIdReq.getGoodUserPhone(), privacyPhoneNumGoodIdReq.getDriverUserPhone());
            //绑定虚拟号失败则直接返回真实手机号，保证可用性
            return driverUserPhone;
        } else {
            return resultMsgBeanByGetPrivacyPhoneNum;
        }
    }

    @Override
    public PrivacyPhoneTabInfo getPrivacyPhoneTabInfo(Long srcMsgId, Long carUserId) {
        log.info("获取虚拟号弹窗 请求参数：srcMsgId:{}, carUserId:{}", srcMsgId, carUserId);
        TransportMainDO transportMainDO = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMainDO == null || transportMainDO.getSrcMsgId() == null || carUserId == null) {
            return null;
        }
        return makeTelTab(transportMainDO, carUserId);
    }

    @Override
    public void updateAXBExpirationDate(Long driverUserId, Long goodsId) {
        if (driverUserId == null || goodsId == null) {
            return;
        }
        privacyPhoneNumService.updateAXBExpirationDate(driverUserId, goodsId);
    }

    private PrivacyPhoneTabInfo makeTelTab(TransportMainDO transportMainDO, Long carUserId) {
        PrivacyPhoneTabInfo privacyPhoneTabInfo = new PrivacyPhoneTabInfo(false, false);

        if (transportMainDO == null || transportMainDO.getSrcMsgId() == null || Objects.equals(transportMainDO.getSourceType(), SourceTypeEnum.NEW_DISPATCH.getCode())) {
            return privacyPhoneTabInfo;
        }

        boolean isCommissionTransport = false;
        if (StringUtils.isNotBlank(transportMainDO.getLabelJson())) {
            TransportLabelJson transportLabelJson = JSON.parseObject(transportMainDO.getLabelJson(), TransportLabelJson.class);
            if (transportLabelJson != null && transportLabelJson.getCommissionTransport() != null && transportLabelJson.getCommissionTransport() == 1) {
                log.info("获取虚拟号弹窗 是抽佣货源");
                TransportTecServiceFeeDO tytTransportTecServiceFeeDO = transportTecServiceFeeMapper.getBySrcMsgId(transportMainDO.getSrcMsgId());
                if (tytTransportTecServiceFeeDO != null) {
                    UserPermissionRpcDTO userPermissionRpcDTO = new UserPermissionRpcDTO();
                    userPermissionRpcDTO.setUserId(carUserId);
                    userPermissionRpcDTO.setServicePermissionTypeId("100101");
                    List<UserPermissionRpcVO> userPermissionByUserId = userPermissionRemoteService.getUserPermissionByUserId(userPermissionRpcDTO);
                    if (!userPermissionByUserId.isEmpty() && userPermissionByUserId.get(0) != null && userPermissionByUserId.get(0).getStatus() != null && userPermissionByUserId.get(0).getStatus() == 1) {
                        log.info("获取虚拟号弹窗 具备找货权益");
                        isCommissionTransport = (tytTransportTecServiceFeeDO.getMemberShowPrivacyPhoneTab() == null || tytTransportTecServiceFeeDO.getMemberShowPrivacyPhoneTab() == 1);
                    } else {
                        log.info("获取虚拟号弹窗 不具备找货权益");
                        isCommissionTransport = (tytTransportTecServiceFeeDO.getNoMemberShowPrivacyPhoneTab() == null || tytTransportTecServiceFeeDO.getNoMemberShowPrivacyPhoneTab() == 1);
                    }
                } else {
                    isCommissionTransport = true;
                }
            }
        }
        if (isCommissionTransport) {
            log.info("获取虚拟号弹窗 符合抽佣货源虚拟号开关开启");
        }

        //首履用户
        boolean firstHonourAnAgreementboolean = ordersRemoteService.checkTransportUserIsFirst(transportMainDO.getUserId());
        if (firstHonourAnAgreementboolean) {
            log.info("获取虚拟号弹窗 符合首履货主条件");
        }

        //如果是发货货主在虚拟号弹窗ab测试中 或者 货源是抽佣货源并且虚拟号开关打开 或者 首履货主 拨打电话弹窗为虚拟号弹窗
        if (isInPrivacyPhoneTabAbTestByTransportUserId(transportMainDO.getUserId()) || isCommissionTransport || firstHonourAnAgreementboolean) {
            log.info("获取虚拟号弹窗 符合弹出虚拟号弹窗条件");
            privacyPhoneTabInfo.setShowPrivacyPhoneTab(true);
        }

        if (privacyPhoneTabInfo.getShowPrivacyPhoneTab()) {
            //如果弹窗为虚拟号弹窗，判断该车主是否已经支付该货源，如果已支付，则可复制真实手机号
            boolean isPay = infoFeeRemoteService.userIsPay(transportMainDO.getSrcMsgId(), carUserId);
            log.info("获取虚拟号弹窗 用户是否已支付该货源:{}", isPay);
            if (isPay) {
                log.info("获取虚拟号弹窗 可复制真实手机号");
                privacyPhoneTabInfo.setCanCopyrealPhoneNum(true);
            }
        }
        return privacyPhoneTabInfo;
    }

    private boolean isShowPrivacyPhoneTabYMM(TransportMainDO transportMainDO) {
        return transportMainDO.getSourceType() == 4 && tytConfigRemoteService.getIntValue("privacy_phone_tab_show_ymm", 1) == 1;
    }

    private boolean isInPrivacyPhoneTabAbTestByTransportUserId(Long transportUserId) {
        List<String> abTestCodeList = new ArrayList<>();
        abTestCodeList.add("privacy_phone_tab_show_transport_abtest");
        List<ABTestVo> userTypeList = abTestRemoteService.getUserTypeList(new ABTestDto(abTestCodeList, transportUserId));
        if (CollectionUtils.isNotEmpty(userTypeList)) {
            boolean result = userTypeList.get(0).getType() == 1;
            log.info("获取虚拟号弹窗 符合AB测试条件");
            return result;
        }
        return false;
    }

}
