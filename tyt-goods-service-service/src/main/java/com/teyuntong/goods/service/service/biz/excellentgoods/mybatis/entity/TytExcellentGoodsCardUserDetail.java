package com.teyuntong.goods.service.service.biz.excellentgoods.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tyt_excellent_goods_card_user_detail")
public class TytExcellentGoodsCardUserDetail {
    @TableId
    private Long id;

    /**
     * 优车发货卡配置表ID
     */
    private Long configId;

    /**
     * 优车发货卡用户汇总信息表ID
     */
    private Long importId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 发放状态 1：待领取；2：待使用；3：已使用（待领取状态暂未启用，发卡后将直接进入待使用状态）
     */
    private Integer type;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 一阶段刷新次数
     */
    private Integer firstRefreshTimes;

    /**
     * 一阶段刷新间隔
     */
    private Integer firstRefreshInterval;

    /**
     * 二阶段刷新次数
     */
    private Integer secondRefreshTimes;

    /**
     * 二阶段刷新间隔
     */
    private Integer secondRefreshInterval;

    /**
     * 有效期开始时间
     */
    private Date validDateBegin;

    /**
     * 有效期结束时间
     */
    private Date validDateEnd;

    /**
     * 发放时间
     */
    private Date createTime;

    /**
     * 发放人用户名
     */
    private String createUserName;

    /**
     * 发放人ID
     */
    private Long createUserId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人用户名
     */
    private String updateUserName;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 使用类型 1优车发货卡 2 优车定价卡
     */
    private Integer useType;

}
