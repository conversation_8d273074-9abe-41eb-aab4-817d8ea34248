package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO;
import com.teyuntong.goods.service.service.mq.constant.TopicConstant;
import com.teyuntong.goods.service.service.mq.pojo.TransportPublishMqBean;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.infra.common.rocketmq.core.RocketMqProducer;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.common.rocketmq.message.MqMessageFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 发送无效货源信息验证mq
 *
 * <AUTHOR>
 * @since 2025/02/23 17:32
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SendPublishMQPostHandler {

    private final RocketMqProducer rocketMqProducer;
    private final MqMessageFactory mqMessageFactory;

    /**
     * 向MQ发送信息
     */
    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO processBO) {
        TransportDO transport = processBO.getTransport();
        // 如果不是发布中货源，不发送mq
        if (transport.getStatus() != 1) {
            return;
        }

        TransportPublishMqBean transportPublishMqBean = new TransportPublishMqBean();
        transportPublishMqBean.setTsId(transport.getId());
        String key = UUID.randomUUID().toString();
        MqMessage mqMessage = mqMessageFactory.create(TopicConstant.GOODS_CENTER_TOPIC, TopicConstant.TRANSPORT_PUBLISH, key, transportPublishMqBean);
        rocketMqProducer.sendNormal(mqMessage);
    }
}
