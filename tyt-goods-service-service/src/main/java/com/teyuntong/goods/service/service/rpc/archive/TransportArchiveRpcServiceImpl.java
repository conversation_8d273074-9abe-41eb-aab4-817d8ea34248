package com.teyuntong.goods.service.service.rpc.archive;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.teyuntong.goods.service.client.archive.service.TransportArchiveRpcService;
import com.teyuntong.goods.service.client.archive.vo.ArchiveVO;
import com.teyuntong.goods.service.service.biz.archive.entity.DwsGoodTypeCnt;
import com.teyuntong.goods.service.service.biz.archive.entity.DwsLvCityCnt;
import com.teyuntong.goods.service.service.biz.archive.service.DwsGoodTypeCntService;
import com.teyuntong.goods.service.service.biz.archive.service.DwsLvCityCntService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 车主档案信息
 *
 * <AUTHOR>
 * @since 2024-11-28 20:14
 */
@RestController
@Slf4j
public class TransportArchiveRpcServiceImpl implements TransportArchiveRpcService {
    @Autowired
    private DwsGoodTypeCntService dwsGoodTypeCntService;
    @Autowired
    private DwsLvCityCntService dwsLvCityCntService;
    @Autowired
    private TransportMainService transportMainService;

    @Override
    public ArchiveVO getArchiveInfoByTransport(Long srcMsgId, Long carUserId) {
        log.info("getArchiveInfoByTransport param srcMsgId:{}, carUserId:{}", srcMsgId, carUserId);
        ArchiveVO archiveVO = new ArchiveVO();
        if (Objects.isNull(carUserId)) {
            return archiveVO;
        }

        TransportMainDO main = transportMainService.getTransportMainForId(srcMsgId);

        List<String> oftenCityList = getOftenCityList(carUserId, main);
        archiveVO.setOftenCityList(oftenCityList);

        List<String> goodTypeNameList = getGoodTypeNameList(carUserId, main);
        archiveVO.setGoodsTypeNameList(goodTypeNameList);

        return archiveVO;
    }

    /**
     * 查询车主承运货物
     * @param carUserId
     * @param main
     * @return
     */
    @NotNull
    private List<String> getGoodTypeNameList(Long carUserId, TransportMainDO main) {
        List<String> goodTypeNameList = new ArrayList<>();
        int limitNum = 3;
        String goodTypeName = null;
        if (Objects.nonNull(main) && StringUtils.isNotEmpty(main.getGoodTypeName())) {
            goodTypeName = main.getGoodTypeName();
            Integer cnt = dwsGoodTypeCntService.selectCntByGoodTypeName(carUserId, goodTypeName);
            if (Objects.nonNull(cnt) && cnt > 0) {
                goodTypeNameList.add(main.getGoodTypeName());
                limitNum = 2;
            }
        }

        List<DwsGoodTypeCnt> goodTypeCnts = dwsGoodTypeCntService.getTopGoodTypeNames(carUserId, goodTypeName, limitNum);
        if (CollectionUtils.isNotEmpty(goodTypeCnts)) {
            goodTypeCnts.forEach(v -> {
                goodTypeNameList.add(v.getGoodTypeName());
            });
        }
        return goodTypeNameList;
    }

    /**
     * 查询车主常跑地点
     * @param carUserId
     * @param main
     * @return
     */
    @NotNull
    private List<String> getOftenCityList(Long carUserId, TransportMainDO main) {
        List<String> oftenCityList = new ArrayList<>();
        List<String> cityList = new ArrayList<>();

        if (Objects.nonNull(main)) {
            cityList.add(main.getStartCity());
            cityList.add(main.getDestCity());
            List<DwsLvCityCnt> cityCnts = dwsLvCityCntService.queryCarUserCityList(carUserId, cityList);
            if (CollectionUtils.isNotEmpty(cityCnts)) {
                cityCnts.forEach(v -> {
                    if (!oftenCityList.contains(v.getCity())) {
                        oftenCityList.add(v.getCity());
                    }
                });
            }
        }

        int limitNum = 3;
        if (CollectionUtils.isNotEmpty(oftenCityList)) {
            limitNum = limitNum - oftenCityList.size();
        }
        List<DwsLvCityCnt> topCities = dwsLvCityCntService.queryCarUserTopCities(carUserId, cityList, limitNum);
        if (CollectionUtils.isNotEmpty(topCities)) {
            topCities.forEach(v -> {
                if (!oftenCityList.contains(v.getCity())) {
                    oftenCityList.add(v.getCity());
                }
            });
        }
        return oftenCityList;
    }
}
