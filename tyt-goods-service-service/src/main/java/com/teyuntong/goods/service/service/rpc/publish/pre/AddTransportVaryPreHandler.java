package com.teyuntong.goods.service.service.rpc.publish.pre;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportVaryDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportVaryService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 保存vary表
 *
 * <AUTHOR>
 * @since 2025/02/23 17:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddTransportVaryPreHandler {

    private final TransportVaryService varyService;
    private final TransportService transportService;

    public void handler(DirectPublishProcessBO processBO) {
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        // 当天货源且vary变更，需要保存vary表
        if (!directPublishBO.isHistoryGoods() && directPublishBO.isSaveVary()) {
            List<Long> tsIds = transportService.getValidTsIdBySrcMsgId(directPublishBO.getSrcMsgId());
            for (Long tsId : tsIds) {
                TransportVaryDO varyDO = new TransportVaryDO();
                varyDO.setTsId(tsId);
                varyDO.setStatus(0);
                varyDO.setUpdateTime(new Date());
                varyService.saveVary(varyDO);
            }
        }
    }

}
