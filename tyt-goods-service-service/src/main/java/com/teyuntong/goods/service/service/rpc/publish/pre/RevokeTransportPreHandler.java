package com.teyuntong.goods.service.service.rpc.publish.pre;

import com.teyuntong.goods.service.client.transport.dto.SaveGoodsStatusDTO;
import com.teyuntong.goods.service.client.transport.dto.UserDispatchTransportDTO;
import com.teyuntong.goods.service.client.transport.enums.CancelAuthOperateTypeEnum;
import com.teyuntong.goods.service.client.transport.service.TransportStatusRpcService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.TransportStatusEnum;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.BackoutReasonEnum;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 撤销货源
 *
 * <AUTHOR>
 * @since 2025/02/23 17:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RevokeTransportPreHandler {

    private final TransportStatusRpcService transportStatusRpcService;

    public void handler(DirectPublishProcessBO processBO) {
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        TransportMainDO oldMain = processBO.getOldMain();

        // 如果货源未过期，且状态为发布中，需要撤销货源
        if (!directPublishBO.isHistoryGoods() && TransportStatusEnum.needCancel(oldMain.getStatus())) {
            SaveGoodsStatusDTO saveGoodsStatusDTO = new SaveGoodsStatusDTO();
            saveGoodsStatusDTO.setUserId(oldMain.getUserId());
            saveGoodsStatusDTO.setSrcMsgId(oldMain.getSrcMsgId());
            BackoutReasonEnum backoutReasonEnum = directPublishBO.getBackoutReasonEnum();
            saveGoodsStatusDTO.setBackoutReasonKey(backoutReasonEnum.getKey());
            saveGoodsStatusDTO.setBackoutReasonValue(backoutReasonEnum.getValue());
            saveGoodsStatusDTO.setOptUserId(oldMain.getUserId());
            saveGoodsStatusDTO.setOptUserName(oldMain.getNickName());
            saveGoodsStatusDTO.setOptionSource(1);

            // 如果是新代调货源的取消找车释放到大厅，需要重新设置操作人和来源，可能是代调操作
            UserDispatchTransportDTO dispatchTransportDTO = processBO.getDirectPublishBO().getUserDispatchTransportDTO();
            if (dispatchTransportDTO != null) {
                saveGoodsStatusDTO.setOptUserId(dispatchTransportDTO.getOperatorId());
                saveGoodsStatusDTO.setOptUserName(dispatchTransportDTO.getOperatorName());
                saveGoodsStatusDTO.setOptionSource(CancelAuthOperateTypeEnum.getOperateSource(dispatchTransportDTO.getOperateType()));
            }

            // 如果是编辑货源或者更新长宽高，需要通知ymm下架，因为ymm编辑接口只支持同步价格等几个字段
            PublishOptEnum optEnum = processBO.getOptEnum();
            if (optEnum == PublishOptEnum.EDIT || optEnum == PublishOptEnum.UPDATE_INFO) {
                transportStatusRpcService.setGoodsBackOut(saveGoodsStatusDTO);
            } else {
                transportStatusRpcService.setGoodsBackOutNoInteraction(saveGoodsStatusDTO);
            }
        }
    }

}
