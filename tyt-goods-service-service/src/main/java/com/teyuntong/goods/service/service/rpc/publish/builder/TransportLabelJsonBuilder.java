package com.teyuntong.goods.service.service.rpc.publish.builder;

import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.goodsrecord.service.CustomFirstOrderRecordService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsTwoEnum;
import com.teyuntong.goods.service.service.common.enums.PublishGoodsTypeEnum;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.order.FeedBackRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrderRiskRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserIdentityRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.trade.service.client.feedBack.dto.UserFeedbackRatingAndLabelDTO;
import com.teyuntong.user.service.client.user.vo.ApiDataUserCreditInfoRpcVO;
import com.teyuntong.user.service.client.user.vo.UserIdentityAuthRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/02/21 15:31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportLabelJsonBuilder {

    private final UserRemoteService userRemoteService;
    private final FeedBackRemoteService feedBackRemoteService;
    private final UserIdentityRemoteService userIdentityRemoteService;
    private final RedisUtil redisUtil;
    private final CustomFirstOrderRecordService customFirstOrderRecordService;
    private final OrderRiskRemoteService orderRiskRemoteService;

    public void build(BasePublishProcessBO processBO) {
        UserRpcVO user = processBO.getUser();
        TransportMainDO transportMain = processBO.getTransportMain();
        TransportMainExtendDO mainExtend = processBO.getMainExtend();
        TransportMainDO oldMain = processBO.getOldMain();

        TransportLabelJson labelJson = new TransportLabelJson();
        if (oldMain != null && oldMain.getLabelJson() != null) {
            labelJson = JSONUtil.toBean(oldMain.getLabelJson(), TransportLabelJson.class);
        }

        String goodService = null;
        BigDecimal complainRate = null;
        ApiDataUserCreditInfoRpcVO userCreditInfo = userRemoteService.getUserCreditInfo(user.getId());
        if (userCreditInfo != null) {
            labelJson.setUserLabelIcon(userCreditInfo.getUserLabelIcon());
            goodService = userCreditInfo.getUserLabelText();
            // 数据部门返回“无标签”时，客户端展示的时无标签。临时处理
            if ("无标签".equals(goodService)) {
                goodService = "";
            }
            complainRate = userCreditInfo.getComplainRate() == null ? BigDecimal.ZERO : userCreditInfo.getComplainRate();
        }

        Integer rankLevel = Optional.ofNullable(userCreditInfo).map(ApiDataUserCreditInfoRpcVO::getRankLevel).orElse(1);

        if (Objects.equals(transportMain.getGuaranteeGoods(), 1) && rankLevel == 5) {
            // 投诉最大百分比 new BigDecimal("0.1")
            if (complainRate != null && complainRate.compareTo(new BigDecimal("0.1")) < 0) {
                goodService = StringUtils.join(goodService, " ", "投诉少").trim();
            }
        }
        labelJson.setGoodService(goodService);

        // 评价标签
        UserFeedbackRatingAndLabelDTO userFeedback = feedBackRemoteService.getUserFeedbackRatingAndLabel(user.getId(), 2);
        if (userFeedback != null) {
            // 30天保护期
            if (DateUtils.addDays(user.getCtime() == null ? new Date() : user.getCtime(), 30).before(new Date())) {
                if (userFeedback.getTotal() >= 3) {
                    userFeedback.setRating("好评率" + userFeedback.getRating() + "%");
                } else if (userFeedback.getTotal() > 0) {
                    userFeedback.setRating("评价少于3");
                } else {
                    userFeedback.setRating(null);
                }
            } else {
                userFeedback.setRating(null);
                userFeedback.setNegativeLabels(Collections.emptyList());
            }
            List<String> positiveLabels = userFeedback.getPositiveLabels();
            if (CollectionUtils.isNotEmpty(positiveLabels)) {
                Set<String> updatedLabels = positiveLabels.stream()
                        .map(label -> label.contains("运费结算快") ? "结算快" : label)
                        .collect(Collectors.toSet());
                positiveLabels.clear();
                positiveLabels.addAll(updatedLabels);
                userFeedback.setPositiveLabels(positiveLabels);
            }
            labelJson.setFeedbackLabel(userFeedback);
        }

        // 用户认证类型
        UserIdentityAuthRpcVO userIdentityAuth = userIdentityRemoteService.getInfoByUserId(user.getId());
        labelJson.setUserAuthStatus(Optional.ofNullable(userIdentityAuth).map(UserIdentityAuthRpcVO::getIdentityStatus).orElse(0));
        labelJson.setEnterpriseAuthStatus(Optional.ofNullable(userIdentityAuth).map(UserIdentityAuthRpcVO::getEnterpriseAuthStatus).orElse(0));

        // 记录好货模型分数
        labelJson.setIGBIResultData(mainExtend.getGoodModelLevel());
        labelJson.setGoodsModelScore(mainExtend.getGoodModelScore());

        if (Objects.equals(transportMain.getExcellentGoodsTwo(), ExcellentGoodsTwoEnum.YES.getCode())) {
            labelJson.setGoodCarPriceTransport(1);
        } else {
            labelJson.setGoodCarPriceTransport(null);
        }

        // 如果是加价，加价次数+1
        if (PublishOptEnum.ADD_PRICE.equals(processBO.getOptEnum())) {
            // 获取加价次数
            String addPriceCountKey = ConfigKeyConstant.TRANSPORT_ADD_PRICE_TIMES + transportMain.getSrcMsgId();
            Integer addPriceNum = redisUtil.getInt(addPriceCountKey);
            int addPriceCount = (addPriceNum == null ? 0 : addPriceNum) + 1;
            labelJson.setAddPriceCount(addPriceCount);
        }

        labelJson.setPriceLabel(0);

        // 如果符合特优货源，就打上特优货源的标签（优车、货主首履）
        if (checkShowExcessCoverageLabel(transportMain, mainExtend)) {
            labelJson.setShowExcessCoverageLabel(YesOrNoEnum.YES.getId());
        } else {
            labelJson.setShowExcessCoverageLabel(null);
        }

        transportMain.setLabelJson(labelJson.getJsonText());
    }

    /**
     * 检查是否展示特优货源标签(优车货源且首履过)
     *
     * @return
     */
    private boolean checkShowExcessCoverageLabel(TransportMainDO transportMainDO,TransportMainExtendDO mainExtend) {
        if (ExcellentGoodsEnums.isExcellent(transportMainDO.getExcellentGoods())) {
            return true;
        }
        return TransportUtil.isExcellentPrice(transportMainDO.getPrice(), mainExtend.getSuggestMinPrice());
    }

}
