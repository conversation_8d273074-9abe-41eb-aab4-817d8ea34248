package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 获取电话记录表（货源与用户关系）每个用户一条数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Getter
@Setter
@TableName("tyt_user_call_phone_record")
public class UserCallPhoneRecordDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运输信息最原始ID
     */
    private Long tsId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 拨打时间
     */
    private Date ctime;

    /**
     * -1: 老电话规则 1：普通用户 2：试用会员 4：VIP会员
     */
    private Integer level;

    /**
     * 路径
     */
    private String path;

    /**
     * 客户端标识
     */
    private String platId;
}
