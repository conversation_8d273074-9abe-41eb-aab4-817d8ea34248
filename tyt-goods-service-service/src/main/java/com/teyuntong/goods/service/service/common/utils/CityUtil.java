package com.teyuntong.goods.service.service.common.utils;


import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 省市区工具
 * @date 2021/11/17 10:39
 */
public class CityUtil {

    /** 直辖市 **/
    public final static String[] municipalit_array = {"北京市", "天津市", "上海市", "重庆市"};
    public final static Set<String> municipality_set = new HashSet<>();

    static {
        municipality_set.addAll(Arrays.asList(municipalit_array));
    }

    /**
     * 是否是直辖市
     * @param city
     * @return
     */
    public static boolean isMunicipalit(String city){
        boolean result = municipality_set.contains(city);
        return result;
    }

    /**
     * 拼接地址信息，不带省
     * @param city
     * @param area
     * @return
     */
    public static String createAddressInfo(String city, String area){
        return createAddressInfo("", city, area);
    }

    /**
     * 拼接地址信息，不带省
     * @param city
     * @param area
     * @return
     */
    public static String createAddressInfo(String province, String city, String area){
        StringBuilder builder = new StringBuilder();

        if(province == null){
            province = "";
        }

        if(CityUtil.isMunicipalit(city)){
            //直辖市去掉“市”字
            province = "";
            city = city.substring(0, city.length() - 1);
        }
        builder.append(province);
        builder.append(city);
        builder.append(area);
        return builder.toString();
    }

    /**
     * 经纬度
     * @param pointNumber
     * @return
     */
    public static BigDecimal toMapPointBigDecimal(Integer pointNumber) {
        if (pointNumber != null) {
            if (pointNumber.toString().length() > 6) {
                return new BigDecimal(pointNumber).movePointLeft(6);
            } else {
                return new BigDecimal(pointNumber).movePointLeft(2);
            }
        }
        return null;
    }

    public static BigDecimal toMapPointBigDecimal(String pointNumber) {
        if (StringUtils.isBlank(pointNumber)) {
            if (pointNumber.length() > 6) {
                return new BigDecimal(pointNumber).movePointLeft(6);
            } else {
                return new BigDecimal(pointNumber).movePointLeft(2);
            }
        }
        return null;

    }

    /**
     * 坐标系转换
     *
     * @param coordNumber
     * @return
     *
     */
    public static BigDecimal toCoordBigDecimal(Integer coordNumber) {
        if (coordNumber != null) {
            return new BigDecimal(coordNumber).movePointLeft(2);
        }
        return null;
    }


    /**
     * 距离转换
     *
     * @param distance
     * @return
     */
    public static BigDecimal moveLeftBigDecimal(Integer distance) {
        if (distance != null) {
            return new BigDecimal(distance).movePointLeft(2).setScale(0, RoundingMode.HALF_UP);
        }
        return null;
    }

    /**
     * 距离转换
     *
     * @param distance
     * @return
     */
    public static Integer moveRightInt(BigDecimal distance) {
        if (distance != null) {
            return distance.movePointRight(2).intValue();
        }
        return null;
    }


}
