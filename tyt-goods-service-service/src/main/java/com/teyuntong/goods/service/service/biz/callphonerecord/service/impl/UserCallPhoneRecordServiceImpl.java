package com.teyuntong.goods.service.service.biz.callphonerecord.service.impl;

import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.UserCallPhoneRecordMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.UserCallPhoneRecordService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import java.util.Date;

/**
 * <p>
 * 获取电话记录表（货源与用户关系）每个用户一条数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class UserCallPhoneRecordServiceImpl implements UserCallPhoneRecordService {

    private final UserCallPhoneRecordMapper userCallPhoneRecordMapper;

    @Override
    public Integer getUserCallPhoneCount(Long userId, Date startTime, Date endTime) {
        return userCallPhoneRecordMapper.getUserCallPhoneCount(userId, startTime, endTime);
    }

    @Override
    public Integer getCallStatusByUserIdAndTsId(Long srcMsgId, Long userId, Date startTime) {
        return userCallPhoneRecordMapper.getCallStatusByUserIdAndTsId(srcMsgId, userId, startTime);
    }
}
