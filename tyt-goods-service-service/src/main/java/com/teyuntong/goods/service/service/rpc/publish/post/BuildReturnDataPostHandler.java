package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.client.transport.vo.DirectPublishResultVO;
import com.teyuntong.goods.service.client.transport.vo.PublishNoticeDataVO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportDispatchViewService;
import com.teyuntong.goods.service.service.biz.exposure.service.ExposureCardGiveawayService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO;
import com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 构建返回数据 post handler
 *
 * <AUTHOR>
 * @since 2025/02/23 17:38
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuildReturnDataPostHandler {

    private final ExposureCardGiveawayService exposureCardGiveawayService;
    private final TransportDispatchViewService transportDispatchViewService;
    private final ABTestRemoteService abTestRemoteService;

    public void handler(DirectPublishProcessBO processBO) {
        TransportDO transport = processBO.getTransport();
        DirectPublishResultVO resultDTO = new DirectPublishResultVO();
        resultDTO.setTsId(transport.getId());
        resultDTO.setTsOrderNo(transport.getTsOrderNo());
        resultDTO.setSrcMsgId(transport.getSrcMsgId());
        processBO.setResultDTO(resultDTO);

        // 如果是曝光，新增曝光弹窗 6670新增ab测逻辑
        if (Objects.equals(abTestRemoteService.getUserType(AbtestKeyConstant.EXPOSURE_CARD_GIVEAWAY, transport.getUserId()), 1)) {
            if (PublishOptEnum.REPUBLISH.equals(processBO.getOptEnum())) {
                PublishNoticeDataVO noticeDataVO = new PublishNoticeDataVO();
                Integer giveawayNum = exposureCardGiveawayService.getGiveawayNum(transport.getSrcMsgId());
                if (giveawayNum != null && giveawayNum > 0) {
                    noticeDataVO.setType(1);
                    noticeDataVO.setContent("<span style=\"font-size:20px;\">该货源线上成交<br>额外送您<span style=\"color:#FF5B00;\">" + giveawayNum + "</span>张曝光卡</span>");
                } else {
                    noticeDataVO.setType(2);
                }
                TransportDispatchViewDO viewCount = transportDispatchViewService.getContactAndViewCount(transport.getSrcMsgId());
                noticeDataVO.setLeftContent(viewCount.getViewCount() + "人查看");
                int expectViewCount = TransportUtil.expectViewCount(transport.getPrice(), processBO.getMainExtend().getGoodModelScore(), viewCount.getViewCount());
                expectViewCount = expectViewCount + viewCount.getViewCount();
                expectViewCount = Math.max(expectViewCount, 10);
                noticeDataVO.setRightContent(expectViewCount + "人查看");
                noticeDataVO.setBtnText("我知道了");

                if (processBO.getDirectPublishBO().getMaxExposureCardUseTimes() != null && processBO.getDirectPublishBO().getMaxExposureCardUseTimes() > 0) {
                    noticeDataVO.setBottomContent("<span style=\"font-size:16px;\">最高可使用" + processBO.getDirectPublishBO().getMaxExposureCardUseTimes() + "张，已使用"
                            + (processBO.getDirectPublishBO().getExposureCardUsedTimes() == null || processBO.getDirectPublishBO().getExposureCardUsedTimes() <= 0 ? "1" : processBO.getDirectPublishBO().getExposureCardUsedTimes() + 1)
                            + "张</span>");
                }

                resultDTO.setNoticeData(noticeDataVO);
            }
        }
    }
}
