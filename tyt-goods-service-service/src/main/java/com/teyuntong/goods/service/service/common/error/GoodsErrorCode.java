package com.teyuntong.goods.service.service.common.error;

import com.teyuntong.infra.common.definition.error.ErrorCodeBase;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特运通货源错误码ErrorCode
 * <p>
 * （1）总共8位，1 2 位保留
 * （2）3 4 位代表具体的子模块
 * （3）5 6 7 8 位表示具体的业务
 * <p>
 */
@AllArgsConstructor
@Getter
public enum GoodsErrorCode implements ErrorCodeBase {
    /**
     * 货源相关错误码,修改业务错误码第3、4位 详见{@link ErrorCodeBase}
     */
    ERROR_SRC_MSG_ID_LACK("00041000", "货源ID不能为空", "error", false),
    ERROR_PHONE_LACK("00041001", "手机号不能为空", "error", false),
    ERROR_OVER_LIMIT("00041002", "超过最大限制", "error", false),
    ERROR_NO_PARAM("00041003", "参数错误", "error", false),
    ERROR_NO_TRANSPORT("00041004", "货源不存在", "error", false),

    USER_AUTH_NO_IDENTITY("00041005", "请认证后重试", "warn", false),
    LOADING_TIME_EXPIRE("00041006", "装卸货时间已过期请重新选择", "warn", false),
    NO_SIGN_SUBJECT("00041007", "请选择签约合作商", "warn", false),
    SECKILL_TRANSPORT_IS_LOCK("00041008", "已有多个司机抢单，正在匹配最优司机，请耐心等待", "warn", false),
    PRICE_TOO_LOW_OR_HIGH("00041009", "运费过低", "warn", false),
    NULLIFY_KEY_WORDS_CONTENT("00041010", "货物非工程机械类，无法发布哦~", "warn", false),
    YMM_PUBLISH_ERROR("00041011", "该运满满货源不允许发布", "warn", false),
    GOODS_NOT_PUBLISH("00041012", "货源已下架", "info", true),
    GOODS_PRICE_TOO_HIGH("00041013", "运费过高，请修改运费", "info", true),
    GOODS_PRICE_EXCEED_LIMIT("00041014", "运费超限，最高支持%s万元，请修改运费", "info", true),
    SHUNTING_QUANTITY_CHECK("00041015", "调车数量不正确", "warn", false),
    GOODS_HAS_CANCELED("00041019", "该货源货主已经取消，不能进行发布", "info", true),
    NO_PUBLISH_EXCELLENT("00041020", "无法发布优车货源，请发布普通货源", "warn", false),
    NO_GET_GOODS_DISTANCE("00041021", "未获取到该货源公里数，不能直接发布", "warn", false),
    CALC_SPECIAL_GOODS_PRICE_FAIL("00041022", "专车货源运费计算失败，不能直接发布", "warn", false),
    TRANSPORT_DUPLICATE("00041023", "您已经发布过此类货源！", "warn", false),
    ADD_PRICE_INTERVAL_LIMIT("00041024", "10分钟内已加价，请稍后再试！", "warn", false),
    DISTANCE_IS_NULL("00041025", "运距获取失败，请返回上一页重选地址或联系客服", "info", true),
    ASSIGN_CAR_FAILURE("00041026", "指派订单失败，货源已撤销，您可在货源列表中点击编辑再发布重新发布", "info", true),
    EXCELLENT_GOODS_AUTH_ERROR_CONFIG("00041027", "您还没有签约优货发布协议，暂不能使用优车功能", "info", true),
    EXCELLENT_GOODS_AUTH_ERROR("00041028", "您还没有签约优货发布协议，暂不能使用优车功能", "info", true),
    EXCELLENT_GOODS_DEPOSIT_ERROR("00041029", "您当前保证金金额不足，暂不能使用优车功能", "info", true),
    NULLIFY_KEY_WORDS_REMARK("00041030", "货物非工程机械类，无法发布哦~", "warn", false),

    NULLIFY_KEY_WORDS_REMARK_NEW("00041038", "货物名称或货名备注存在敏感内容，无法发布哦~", "warn", false),


    FAIL_ENTERPRISE_CERTIFICATION("00041031", "未通过企业认证不可发布“专票”货源，如有需要，可联系客服获取帮助", "info", true),
    FAIL_PUBLISH_INVOICE_GOODS("00041032", "您的企业账户当前无法发布“专票”货源，您可以选择不开发票继续发货，如需帮助请联系客服", "info", true),
    ENTERPRISE_TAX_ERROR("00041034", "企业税率应在0和1之间", "info", true),
    SPECIAL_NO_PUBLISH_OWN_SUBJECT("00041035", "专车暂不支持甘肃网货开票主体，请在发票类型中更换", "info", true),
    ASSIGN_TRANSPORT_PUBLISH_ERROR("00041036", "该货源为指派货源，请使用编辑再发布", "info", true),
    INVOICE_CONSIGNEE_INFO("00041037", "请填写收货人信息", "info", true),
    // 小程序货源
    BACKEND_TRANSPORT_PUBLISHED("00041040", "当前订单已发布，无法重新发布", "info", true),

    TRANSPORT_TOP_LIMIT_TIME("00041051", "该货源已刷新", "warn", false),//刷新间隔过于频繁
    TRANSPORT_MAX_TOP("00041052", "该货源今日已到达刷新上限～", "warn", false),
    TRANSPORT_TOP_USE_UP("00041053", "您的刷新权益已用尽，您可通过线上履约获得更多曝光机会", "warn", false),
    EXPOSURE_CARD_LIMIT("00041054", "您因平台管控已被限制使用曝光卡，如有疑问可联系客服", "error", false),
    EXPOSURE_CARD_RECEIVE("00041055", "暂无可用曝光卡，您有曝光卡待领取", "error", false),


    PULL_NEW_USER_UNVERIFIED("00041060", "您已参与活动并可领取奖励，若要发货，请先进行实名认证","warn", false),

    PUBLISH_NO_PERMISSION_NOTICE("00041061", "发货无权益", "warn", false),
    START_POINT_ERROR("00041062", "出发地坐标不正确，请重新选择", "warn", false),
    DEST_POINT_ERROR("00041063", "目的地坐标不正确，请重新选择", "warn", false),
    CARPOOL_PUBLISH_ERROR("00041064", "该货源为拼车货源，暂不支持直接发布，请编辑再发布", "warn", false),

    ERROR_NO_BELONG_TO_ONESELF("00041070", "非本人货源不可操作", "error", false),
    ERROR_NO_IN_PUBLISH("00041071", "非发布中的货源不可操作", "error", false),
    ERROR_TRANSPORT_INVALID("00041072", "货源已失效", "warn", false),
    REFRESH_NOT_ALLOWED("00041073", "当前相似货多，无法使用曝光卡", "warn", false),

    SEGMENTED_PAYMENTS_PROPORTION_ERROR("00041080", "分段运费比例不能超过上限，请修改", "error", false),

    SEC_KILL_GOODS_LOCK_MSG("00041090", "秒抢货源锁定期间暂不支持报价", "warn", false),
    TRANSPORT_QUOTED_NOT_PERMIT("00041091", "该货源不允许出价", "warn", false),
    TRY_AGAIN_LATER("00041092", "操作频繁，请5秒后重试", "warn", false),
    NO_QUOTED_PRICE("00041093", "报价不存在", "warn", false),
    QUOTED_PRICE_ACCEPTED("00041094", "报价已被接受，不可再次报价", "warn", false),
    QUOTED_PRICE_TOO_HIGH("00041095", "您当前的出价过高，请重新输入", "warn", false),
    QUOTED_PRICE_TOO_LOW("00041096", "您当前的出价过低，请重新输入", "warn", false),
    QUOTED_PRICE_OVER_TIMES("00041097", "报价次数已超限，不可再次进行报价", "warn", false),
    QUOTED_PRICE_REPEATED("00041098", "货方回价车方暂未回应，车方回应前不可重复报价", "warn", false),
    QUOTED_PRICE_USER_NOT_ALLOWED("00041099", "用户不可进行出价", "warn", false),
    QUOTED_PRICE_TRANSPORT_OWNER_NOT_ALLOWED("00041100", "本人的货源不可出价", "warn", false),
    QUOTED_PRICE_LOWER_THAN_TRANSPORT_PRICE("00041103", "您的出价低于或等于当前运费，请重新出价", "warn", false),
    QUOTED_PRICE_CAR_REPEATED("00041104", "车方报价货方暂未回应，货方回应前不可重复报价", "warn", false),

    INFO_FEE_TOO_HIGH("00041101", "您当前订金高于运费，请核对后再发货", "warn", false),
    DELETE_GOODS_IN_PUBLISH("00041102", "发布中的不允许删除", "warn", false),

    EXCELLENT_PRICE_TELE_LIMIT("00041201", "优车电议次数不足", "warn", false),
    TRANSFER_FIXED_PARAM_ERROR("00041202", "转一口价参数异常", "warn", false),
    PERK_ACTIVITY_END("00041203", "货源参加的活动已结束，请使用编辑再发布", "warn", false),
    PERK_RATIO_CHANGE("00041204", "补贴比例更新，请使用编辑再重发","warn", false),
    PUBLISH_PERK_ACTIVITY_END("00041205", "货源参加的活动已结束，请重新编辑", "warn", false),
    PUBLISH_PERK_RATIO_CHANGE("00041206", "补贴比例已更新，请重新编辑","warn", false),

    // 开票相关异常码 - 对应老系统90001-90004
    INVOICE_ENTERPRISE_CERTIFICATION_REQUIRED("00041207", "发布专票货源，需先完成企业认证、企业签约", "info", true),
    INVOICE_ENTERPRISE_TAX_RATE_ERROR("00041208", "您的企业账户当前无法发布\"专票\"货源，您可以选择不开发票继续发货，如需帮助请联系客服", "info", true),
    INVOICE_SPECIAL_CAR_GANSU_SUBJECT_ERROR("00041209", "专车暂不支持甘肃网货开票主体，请在发票类型中更换", "info", true),
    INVOICE_SPECIAL_CAR_XHL_SUBJECT_ERROR("00041210", "专车暂不支持翔和翎开票，请更换专票主体", "info", true),
    INVOICE_DISTANCE_GETTING("00041211", "正在获取运距，请稍后再试", "info", true),
    INVOICE_DISTANCE_TOO_SHORT("00041212", "装货地到卸货地小于%s公里，不符合当前税局开票要求，请修正。", "info", true),
    INVOICE_COUNT_PRICE_TOO_MUCH("00041213", "您的企业账户当前无法发布“专票”货源，如需帮助请联系客服。", "info", true),
    EXPOSURE_CARD_USE_LIMIT("00041214", "该货源曝光卡已达使用上限", "info", false),

    PUBLISH_PRICE_IS_NULL("00041214", "请填写运费", "warn", false),
    GOODS_INFO_OVER_LIMIT("00041215", "货物信息超限", "warn", false),
    INVOICE_SUBJECT_ID_IS_NULL("00041216", "开票主体为空", "warn", false),
    EXCELLENT_GOODS_BLOCK("00041217", "您当前账号因违反平台规则无法发优车货源，如有疑问请联系客服", "warn", false),
    SPECIAL_CAR_ROUTE_NOT_SUPPORT("00041218", "路线不支持专车", "warn", false),
    PLAT_LIMIT("00041219", "该货源享平台权益，请至APP编辑发布", "warn", false),
    EXCELLENT_GOODS_SIZE_IS_NULL("00041220", "优车货源长宽高重不能为空", "warn", false),
    INVOICE_OVER_ERROR("00041221", "吨公里运费过高", "warn", false),
    GOODS_STATUS_ERROR("00041222", "货源状态不正确，不可编辑", "warn", false),
    EXCELLENT_GOODS_LEVEL_ERROR("00041301", "价格不符合优车档位，请重新编辑发布", "info", true),

    INVOICE_DRIVER_NOT_EXIST("00041302", "无该司机信息，请重新选择。", "error", false),
    INVOICE_DRIVER_SPECIAL_EXIST("00041303", "该司机是专车司机，请勿重复申请。", "error", false),
    CAR_NOT_EXIST("00041304", "无该车辆信息，请重新选择。", "error", false),
    USER_CAR_NOT_EXIST("00041305", "该车辆不是当前车主车辆，请重新选择。", "error", false),
    SIGNING_CAR_NOT_EXIST("00041306", "无签约车辆", "error", false),
    INFO_FEE_OPERATE_DATA_DISABLED("00041307", "数据无效，不能进行再此操作", "error", false),
    GOODS_ONLINE_EDIT_TIMES_ERROR("00041308", "该货源编辑次数已用尽", "warn", false),
    PUBLISH_LIMIT("00041309", "您被限制发货，暂不可编辑", "warn", false),
    GOODS_PAID("00041310", "货源已有司机支付，不可编辑", "warn", false),

    COORDINATE_NULL("00041401", "发货地经纬度为空，请重新选择装卸货地", "warn", false),
    NAVIGATION_ERROR("00041402", "调用三方导航异常，请重试", "warn", false),

    NO_DISPATCH_USER("00041501", "无可接单的调度", "warn", false),
    ;

    private final String code;
    private final String msg;
    private final String logLevel;
    private final boolean success;
}
