package com.teyuntong.goods.service.service.rpc.publish.bo;

import com.teyuntong.goods.service.client.publish.dto.UpdateGoodsInfoDTO;
import com.teyuntong.goods.service.client.transport.dto.UserDispatchTransportDTO;
import com.teyuntong.goods.service.service.common.enums.PublishGoodsTypeEnum;
import com.teyuntong.goods.service.service.rpc.publish.enums.BackoutReasonEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 直接发布请求参数BO
 *
 * <AUTHOR>
 * @since 2025/02/11 10:05
 */
@Getter
@Setter
public class DirectPublishBO {

    // ============== 传入字段 ================

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 是否是小程序货源
     */
    private Integer isBackendTransport;

    /**
     * 转一口价/电议 货源类型（电议1，一口价2） 一口价需求新增参数，可传 null
     */
    private Integer publishType;

    /**
     * 修改后价格，可传null
     */
    private String price;

    /**
     * 长、宽、高更新参数
     */
    private UpdateGoodsInfoDTO updateGoodsInfoDTO;

    /**
     * 需要赠送刷新次数
     */
    private int extraRefreshTimes = 0;

    /**
     * 代调请求参数
     */
    private UserDispatchTransportDTO userDispatchTransportDTO;

    // ============== 初始化字段 ================

    /**
     * 当置顶时是否消耗曝光卡
     */
    private boolean useExposure = true;

    /**
     * 是否置顶
     */
    private boolean topFlag = false;

    /**
     * 是否保存vary
     */
    private boolean saveVary = false;

    /**
     * 撤销货源的原因
     */
    private BackoutReasonEnum backoutReasonEnum;

    /**
     * 是否重新计算技术服务费：
     * 只有填价、加价、转一口价、手动直接发布需要重新计算
     */
    private boolean recalculateTecServiceFee = false;

    /**
     * 加价操作是否需要校验时间间隔
     */
    private boolean isCheckAddPriceInterval = true;

    // ============= 透传字段 ================

    /**
     * 是否是历史货源
     */
    private boolean isHistoryGoods = false;

    /**
     * 优车发货卡
     */
    private Long excellCardId = null;

    /**
     * 是否刷新重复货源
     */
    private boolean refreshDuplicate = false;

    /**
     * 确认发布重货
     */
    private Integer confirm;

    /**
     * 是否优车运价货源 1:是 0:否
     */
    private Integer goodCarPriceTransport;

    /**
     * 是否自动优车运价货源
     */
    private boolean automaticGoodCarPriceTransport = false;

    /**
     * 是否需要扣减优车2.0电议次数
     */
    private boolean needUseExcellentGoodsTele = false;

    /**
     * 附加运费
     */
    private String additionalPrice;

    /**
     * 企业税率
     */
    private BigDecimal enterpriseTaxRate;

    /**
     * 优惠金额
     */
    private Integer perkPrice;

    /**
     * 曝光卡最大可使用次数
     */
    private Integer maxExposureCardUseTimes;

    /**
     * 曝光卡已使用次数
     */
    private Integer ExposureCardUsedTimes;

    /**
     * 发布类型枚举
     */
    private PublishGoodsTypeEnum publishGoodsTypeEnum;

}
