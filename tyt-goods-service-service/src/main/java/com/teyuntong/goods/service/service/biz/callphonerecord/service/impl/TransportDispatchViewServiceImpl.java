package com.teyuntong.goods.service.service.biz.callphonerecord.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.TransportDispatchViewMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportDispatchViewDetailService;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportDispatchViewService;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 货源查看、联系统计表 service
 *
 * <AUTHOR>
 * @since 2024/12/02 13:55
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportDispatchViewServiceImpl implements TransportDispatchViewService {

    private final TransportDispatchViewMapper transportDispatchViewMapper;
    private final TransportDispatchViewDetailService transportDispatchViewDetailService;


    /**
     * 货源某个货源的所有拨打查看记录
     *
     * @param srcMsgId
     */
    @Override
    public List<TransportDispatchViewDO> getBySrcMsgId(Long srcMsgId) {
        return transportDispatchViewMapper.selectList(
                new QueryWrapper<TransportDispatchViewDO>().eq("src_msg_id", srcMsgId));
    }

    /**
     * 根据srcMsgId获取联系次数和查看次数
     */
    @Override
    public List<TransportDispatchViewDO> getContactAndViewCount(List<Long> srcMsgIds) {
        return transportDispatchViewMapper.selectViewAndContactCount(srcMsgIds);
    }

    /**
     * 根据srcMsgId获取联系次数和查看次数
     */
    @Override
    public TransportDispatchViewDO getContactAndViewCount(Long srcMsgId) {
        return transportDispatchViewMapper.countContactAndViewBySrcMsgId(srcMsgId);
    }

    /**
     * 保存货源查看或联系记录
     *
     * @param user     车主信息
     * @param srcMsgId 货源ID
     * @param type     查看类型 1：查看  2：联系
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTransportView(UserRpcVO user, Long srcMsgId, Integer type) {
        if (user != null) {
            saveOrUpdateView(user, srcMsgId, type);
            transportDispatchViewDetailService.saveViewDetail(user, srcMsgId, type);
        }
    }

    private void saveOrUpdateView(UserRpcVO user, Long srcMsgId, Integer type) {
        TransportDispatchViewDO viewDO = transportDispatchViewMapper.selectBySrcMsgIdAndCarUserId(srcMsgId, user.getId());
        if (viewDO == null) {
            viewDO = new TransportDispatchViewDO();
            viewDO.setSrcMsgId(srcMsgId);
            viewDO.setCarUserId(user.getId());
            viewDO.setCarUserName(TransportUtil.formatUserName(user.getTrueName(), String.valueOf(user.getId())));
            viewDO.setCarNickName(TransportUtil.formatUserName(user.getUserName(), String.valueOf(user.getId())));
            viewDO.setCarPhone(user.getCellPhone());
            //1:查看  2：联系
            if (type == 1) {
                viewDO.setViewTime(new Date());
                viewDO.setViewCount(1);
                viewDO.setContactCount(0);
            } else {
                viewDO.setContactTime(new Date());
                viewDO.setContactCount(1);
                viewDO.setViewCount(0);
            }
            viewDO.setCreateTime(new Date());
            viewDO.setModifyTime(new Date());
            transportDispatchViewMapper.insert(viewDO);
        } else {
            //1:查看  2：联系
            if (type == 1) {
                viewDO.setViewTime(new Date());
                viewDO.setViewCount(viewDO.getViewCount() == null ? 0 : viewDO.getViewCount() + 1);
            } else {
                viewDO.setContactTime(new Date());
                viewDO.setContactCount(viewDO.getContactCount() == null ? 0 : viewDO.getContactCount() + 1);
            }
            viewDO.setModifyTime(new Date());
            transportDispatchViewMapper.updateById(viewDO);
        }

    }

    @Override
    public Integer hasContactInSrcMsgIds(List<Long> srcMsgIds) {
        return transportDispatchViewMapper.hasContactInSrcMsgIds(srcMsgIds);
    }
}
