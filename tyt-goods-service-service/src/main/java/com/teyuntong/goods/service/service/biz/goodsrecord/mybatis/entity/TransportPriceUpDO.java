package com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货源加价表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@Setter
@TableName("tyt_transport_price_up")
public class TransportPriceUpDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 原货源id(tyt_transport_main主键)
     */
    private Long srcMsgId;

    /**
     * 创建人id
     */
    private Long userId;

    /**
     * 加价次数
     */
    private Integer upNumber;

    /**
     * 运费加价金额（元）
     */
    private Integer upPrice;

    /**
     * 原价格（元）
     */
    private Integer beforePrice;

    /**
     * 加价后价格（元）
     */
    private Integer afterPrice;

    /**
     * 采集时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 原始价格（元）
     */
    private Integer originalPrice;

    /**
     * 最早价格（元）
     */
    private Integer earliestPrice;

    /**
     * 操作类型，取自 PublishOptEnum
     */
    private String publishOpt;
}
