package com.teyuntong.goods.service.service.biz.goodsrecord.service.impl;

import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.entity.ExposurePermissionUsedRecordDO;
import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.mapper.ExposurePermissionUsedRecordMapper;
import com.teyuntong.goods.service.service.biz.goodsrecord.service.ExposurePermissionUsedRecordService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 货源曝光记录服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ExposurePermissionUsedRecordServiceImpl implements ExposurePermissionUsedRecordService {

    private final ExposurePermissionUsedRecordMapper exposurePermissionUsedRecordMapper;

    /**
     * 保存货源曝光记录
     */
    @Override
    @Async
    public void save(TransportMainDO transport) {
        //扣减刷新权益
        ExposurePermissionUsedRecordDO recordDO = new ExposurePermissionUsedRecordDO();
        recordDO.setUserId(transport.getUserId());
        recordDO.setSrcMsgId(transport.getSrcMsgId());
        recordDO.setStartPoint(transport.getStartPoint());
        recordDO.setDestPoint(transport.getDestPoint());
        recordDO.setTaskContent(transport.getTaskContent());
        recordDO.setCtime(new Date());
        exposurePermissionUsedRecordMapper.insert(recordDO);
    }
}
