package com.teyuntong.goods.service.service.rpc.publish.builder;

import cn.hutool.core.util.ArrayUtil;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportGoodModelFactorService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportSeckillFactorService;
import com.teyuntong.goods.service.service.common.enums.GoodsTransportLabelEnum;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.bi.BiGoodModelResult;
import com.teyuntong.goods.service.service.rpc.bi.BiRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.converter.TransportPublishConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 扩展表信息builder
 *
 * <AUTHOR>
 * @since 2025/02/21 20:01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportExtendBuilder {

    private final BiRemoteService biRemoteService;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final TransportSeckillFactorService transportSeckillFactorService;
    private final TransportGoodModelFactorService transportGoodModelFactorService;

    public void build(DirectPublishProcessBO processBO) {
        TransportMainExtendDO newMainExtendDO = TransportPublishConverter.INSTANCE
                .copyMainExtendDO(processBO.getOldMainExtend());

        if (processBO.getDirectPublishBO().isHistoryGoods()) {
            newMainExtendDO.setId(null);
        }

        TransportMainDO newMainDO = processBO.getTransportMain();
        newMainExtendDO.setCreateTime(newMainDO.getCtime());
        newMainExtendDO.setModifyTime(newMainDO.getMtime());

        // 设置优车价格
        CarryPriceVO thPrice = processBO.getThPrice();
        if (thPrice != null) {
            newMainExtendDO.setSuggestMinPrice(thPrice.getFixPriceMin());
            newMainExtendDO.setSuggestMaxPrice(thPrice.getFixPriceMax());
            newMainExtendDO.setFixPriceFast(thPrice.getFixPriceFast());
            newMainExtendDO.setCostPrice(thPrice.getThMinPrice());
        }


        // 设置BI模型分数
        BiGoodModelResult goodModel = biRemoteService.getGoodModel(newMainDO, newMainExtendDO);
        if (goodModel != null) {
            newMainExtendDO.setGoodModelScore(goodModel.getScore());
            newMainExtendDO.setGoodModelLevel(goodModel.getLevel());
            newMainExtendDO.setLimGoodModelScore(goodModel.getLim_score());
            newMainExtendDO.setLimGoodModelLevel(goodModel.getLim_level());
            processBO.setBiGoodModelResult(goodModel);
        }

        // 设置BI抽佣分数
        if (StringUtils.isNotBlank(newMainDO.getPrice())) {
            BiGoodModelResult goodsModelPrice = biRemoteService.getGoodsModelPrice(newMainDO);
            if (goodsModelPrice != null) {
                newMainExtendDO.setCommissionScore(goodsModelPrice.getScore());
            }
        }


        // 6700需求：秒抢货源长时间未成交会变成非秒抢，这列货源再操作不会变成秒抢。
        // 历史货源重新发布，或者没有转非秒抢标签，判断秒抢逻辑
        if (processBO.getDirectPublishBO().isHistoryGoods()
                || !Objects.equals(TransportUtil.getLabelJson(processBO.getOldMain()).getSeckillDowngrade(), 1)) {
            // 设置秒抢货源，后面保存extend时再设置 seckillGoods字段，因为需要根据srcMsgId字段判断实验组、对照组
            processBO.setIsSeckillGoods(this.checkIsSeckillGoods(newMainDO, newMainExtendDO));
        }

        // 设置好中差货标签
        newMainExtendDO.setGoodTransportLabel(0);
        newMainExtendDO.setGoodTransportLabelPart(0);
        int modelLevel = transportGoodModelFactorService.judgeModelLevel(newMainDO, newMainExtendDO);
        if (modelLevel > 0) {
            newMainExtendDO.setGoodTransportLabel(modelLevel);
        } else if (modelLevel < 0) {
            newMainExtendDO.setGoodTransportLabelPart(modelLevel * -1);
        }

        // 设置是否是融合发货
        Integer clientFusion = processBO.getBaseParam().getClientFusion();
        newMainExtendDO.setClientFusion(clientFusion == null ? 0 : clientFusion);
        processBO.setMainExtend(newMainExtendDO);
    }

    /**
     * 判断是否是秒抢货源
     * 1. 剔除：开票/专车/多车的货源/YMM的货源/后台发的货
     * 2. 一口价
     * 3. 定价不能为0
     * 4. 符合好货（好1、好2、好3）的标准 (100%)
     * 4. 或 符合中货的标准 + 指定路线 (8/2开)
     *
     * @return 0：非秒抢货源 1：100%秒抢货源 2:8/2开的秒抢
     * @since 6600
     */
    public Integer checkIsSeckillGoods(TransportMainDO transport, TransportMainExtendDO extendDO) {
        // 是否开启秒抢货源判断开关
        Integer switchV = tytConfigRemoteService.getIntValue("turn_on_seckill_goods_switch", 0);
        if (switchV == 0) {
            return 0;
        }
        // 新代调货源不进秒抢
        if (Objects.equals(transport.getSourceType(), SourceTypeEnum.NEW_DISPATCH.getCode())) {
            log.info("checkIsSeckillGoods 新代调货源，hashCode:{}", transport.getHashCode());
            return 0;
        }
        // 非一口价直接返回
        if (!Objects.equals(transport.getPublishType(), PublishTypeEnum.FIXED.getCode())) {
            log.info("checkIsSeckillGoods 非一口价，hashCode:{}", transport.getHashCode());
            return 0;
        }
        // 定金不能为0
        if (transport.getInfoFee() == null || transport.getInfoFee().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("checkIsSeckillGoods 定金为0，hashCode:{}", transport.getHashCode());
            return 0;
        }

        // 非 开票/专车/多车的货源/YMM的货源/后台发的货 直接返回
        if (Objects.equals(transport.getInvoiceTransport(), 1)
                || Objects.equals(transport.getExcellentGoods(), 2)
                || (transport.getShuntingQuantity() != null && transport.getShuntingQuantity() > 1)
                || !ArrayUtil.contains(new int[]{SourceTypeEnum.NORMAL.getCode(), SourceTypeEnum.OWNER.getCode()}, transport.getSourceType())) {
            log.info("checkIsSeckillGoods 非APP发货，hashCode:{}", transport.getHashCode());
            return 0;
        }
        // 去掉长宽高重限制
        /*if (StringUtils.isBlank(transport.getWeight()) || Double.parseDouble(transport.getWeight()) <= 0
                || StringUtils.isBlank(transport.getLength()) || Double.parseDouble(transport.getLength()) <= 0
                || StringUtils.isBlank(transport.getWide()) || Double.parseDouble(transport.getWide()) <= 0
                || StringUtils.isBlank(transport.getHigh()) || Double.parseDouble(transport.getHigh()) <= 0) {
            log.info("checkIsSeckillGoods 长宽高重为0，srcMsgId:{}", transport.getSrcMsgId());
            return false;
        }*/
        // 符合好货标准
        if (GoodsTransportLabelEnum.isGood(extendDO.getGoodTransportLabel())) {
            log.info("checkIsSeckillGoods 符合好货标准，hashCode:{}", transport.getHashCode());
            return 1;
        } else if (GoodsTransportLabelEnum.isMid(extendDO.getGoodTransportLabel())) {
            if (transportSeckillFactorService.matchCondition(transport.getStartCity(), transport.getDestCity())) {
                log.info("checkIsSeckillGoods 符合中货标准且命中路线，hashCode:{}", transport.getHashCode());
                return 2;
            }
        }
        return 0;
    }
}
