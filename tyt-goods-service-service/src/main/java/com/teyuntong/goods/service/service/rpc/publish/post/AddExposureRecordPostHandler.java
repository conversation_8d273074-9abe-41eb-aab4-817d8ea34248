package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.goodsrecord.service.ExposurePermissionUsedRecordService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 添加曝光记录
 *
 * <AUTHOR>
 * @since 2025/03/07 18:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddExposureRecordPostHandler {

    private final ExposurePermissionUsedRecordService exposurePermissionUsedRecordService;

    @Async("threadPoolExecutor")
    public void handler(DirectPublishProcessBO processBO) {
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        // 置顶且使用曝光卡，记录曝光卡使用记录
        if (directPublishBO.isTopFlag() && directPublishBO.isUseExposure()) {
            TransportMainDO newMainDO = processBO.getTransportMain();
            exposurePermissionUsedRecordService.save(newMainDO);
        }
    }
}
