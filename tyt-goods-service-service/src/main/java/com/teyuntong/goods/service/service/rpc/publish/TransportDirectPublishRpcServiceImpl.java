package com.teyuntong.goods.service.service.rpc.publish;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.client.publish.dto.DirectPublishDTO;
import com.teyuntong.goods.service.client.publish.dto.DispatchDirectPublishDTO;
import com.teyuntong.goods.service.client.publish.dto.UpdateGoodsInfoDTO;
import com.teyuntong.goods.service.client.publish.service.TransportDirectPublishRpcService;
import com.teyuntong.goods.service.client.transport.vo.DirectPublishResultVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainExtendService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.enums.TransportStatusEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.checker.BaseParamChecker;
import com.teyuntong.goods.service.service.rpc.publish.converter.TransportPublishConverter;
import com.teyuntong.goods.service.service.rpc.publish.direct.*;
import com.teyuntong.goods.service.service.rpc.publish.enums.BackoutReasonEnum;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Objects;

/**
 * 直接发布rpc服务
 *
 * <AUTHOR>
 * @since 2025/02/11 10:03
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class TransportDirectPublishRpcServiceImpl implements TransportDirectPublishRpcService {

    private final TransportMainService transportMainService;
    private final TransportMainExtendService transportMainExtendService;

    private final TransportDirectPublishChecker directPublishChecker;
    private final TransportDirectPublishPreHandler directPublishPreHandler;
    private final TransportDirectPublishBuilder directPublishBuilder;
    private final TransportDirectPublishSaver directPublishSaver;
    private final TransportDirectPublishPostHandler directPublishPostHandler;

    private final UserRemoteService userRemoteService;
    private final BaseParamChecker baseParamChecker;

    /**
     * 直接发布
     */
    @Override
    public DirectPublishResultVO directPublish(DirectPublishDTO directPublishDTO) {
        DirectPublishProcessBO processBO = buildDirectPublishBO(directPublishDTO, true);
        processBO.setOptEnum(PublishOptEnum.DIRECT);
        //校验登录端
        baseParamChecker.checkClientSign(processBO.getBaseParam(), processBO.getOldMain(), processBO.getOldMainExtend());

        // 直接发布消耗曝光卡、不置顶
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        directPublishBO.setUseExposure(true);
        directPublishBO.setTopFlag(false);
        directPublishBO.setSaveVary(true);
        directPublishBO.setRefreshDuplicate(true);
        directPublishBO.setRecalculateTecServiceFee(true);
        directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.DIRECT_PUBLISH);

        return process(processBO);
    }

    /**
     * 填价
     */
    @Override
    public DirectPublishResultVO fillPrice(DirectPublishDTO directPublishDTO) {
        DirectPublishProcessBO processBO = buildDirectPublishBO(directPublishDTO, true);
        processBO.setOptEnum(PublishOptEnum.FILL_PRICE);

        // 填价不消耗曝光卡、不置顶
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        directPublishBO.setUseExposure(false);
        directPublishBO.setTopFlag(false);
        directPublishBO.setSaveVary(false);
        directPublishBO.setRefreshDuplicate(false);
        directPublishBO.setRecalculateTecServiceFee(true);
        directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.ADD_PRICE);

        return process(processBO);
    }

    /**
     * 加价
     */
    @Override
    public DirectPublishResultVO addPrice(DirectPublishDTO directPublishDTO) {
        DirectPublishProcessBO processBO = buildDirectPublishBO(directPublishDTO, true);
        processBO.setOptEnum(PublishOptEnum.ADD_PRICE);

        // 加价不消耗曝光卡，置顶
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        directPublishBO.setUseExposure(false);
        directPublishBO.setTopFlag(true);
        directPublishBO.setSaveVary(true);
        directPublishBO.setRefreshDuplicate(false);
        directPublishBO.setRecalculateTecServiceFee(true);
        directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.ADD_PRICE);

        return process(processBO);
    }

    /**
     * 填价/加价接口
     */
    @Override
    public DirectPublishResultVO updatePrice(DirectPublishDTO directPublishDTO) {
        DirectPublishProcessBO processBO = buildDirectPublishBO(directPublishDTO, true);

        if (TransportUtil.hasPrice(processBO.getOldMain().getPrice())) {
            processBO.setOptEnum(PublishOptEnum.ADD_PRICE);
            // 加价不消耗曝光卡，置顶
            DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
            directPublishBO.setUseExposure(false);
            directPublishBO.setTopFlag(true);
            directPublishBO.setSaveVary(true);
            directPublishBO.setRefreshDuplicate(false);
            directPublishBO.setRecalculateTecServiceFee(true);
            directPublishBO.setCheckAddPriceInterval(directPublishDTO.getIsCheckAddPriceInterval() == null
                    || directPublishDTO.getIsCheckAddPriceInterval());
            directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.ADD_PRICE);
        } else {
            processBO.setOptEnum(PublishOptEnum.FILL_PRICE);
            // 填价不消耗曝光卡、不置顶
            DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
            directPublishBO.setUseExposure(false);
            directPublishBO.setTopFlag(false);
            directPublishBO.setSaveVary(false);
            directPublishBO.setRefreshDuplicate(false);
            directPublishBO.setRecalculateTecServiceFee(true);
            directPublishBO.setCheckAddPriceInterval(directPublishDTO.getIsCheckAddPriceInterval() == null
                    || directPublishDTO.getIsCheckAddPriceInterval());
            directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.ADD_PRICE);
        }

        return process(processBO);
    }

    /**
     * 转电议/一口价
     */
    @Override
    public DirectPublishResultVO transfer(DirectPublishDTO directPublishDTO) {
        DirectPublishProcessBO processBO = buildDirectPublishBO(directPublishDTO, true);
        if (PublishTypeEnum.FIXED.getCode().equals(directPublishDTO.getPublishType())) {
            processBO.setOptEnum(PublishOptEnum.TRANSFER_FIXED);
        } else {
            processBO.setOptEnum(PublishOptEnum.TRANSFER_TELE);
        }
        //校验登录端
        baseParamChecker.checkClientSign(processBO.getBaseParam(), processBO.getOldMain(), processBO.getOldMainExtend());

        // 转一口价不消耗曝光卡，不置顶
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        directPublishBO.setUseExposure(false);
        directPublishBO.setTopFlag(false);
        directPublishBO.setSaveVary(false);
        directPublishBO.setRefreshDuplicate(false);
        // 一口价+运费变更才重新计算技术服务费
        if (PublishTypeEnum.FIXED.getCode().equals(directPublishDTO.getPublishType())
                && StringUtils.isNotBlank(directPublishDTO.getPrice())) {
            directPublishBO.setRecalculateTecServiceFee(true);
        }
        directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.TRANSFER_FIXED);

        return process(processBO);
    }

    /**
     * 曝光
     */
    @Override
    public DirectPublishResultVO rePublish(DirectPublishDTO directPublishDTO) {
        DirectPublishProcessBO processBO = buildDirectPublishBO(directPublishDTO, true);
        processBO.setOptEnum(PublishOptEnum.REPUBLISH);

        // 曝光消耗曝光卡，置顶
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        directPublishBO.setUseExposure(true);
        directPublishBO.setTopFlag(true);
        directPublishBO.setSaveVary(true);
        directPublishBO.setRefreshDuplicate(false);
        directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.DIRECT_PUBLISH);

        return process(processBO);
    }

    /**
     * 用户取消授权释放大厅
     */
    @Override
    public DirectPublishResultVO cancelDispatchAuth(DispatchDirectPublishDTO directPublishDTO) {
        DirectPublishProcessBO processBO = buildDirectPublishBO(directPublishDTO, false);
        processBO.setOptEnum(PublishOptEnum.CANCEL_AUTH_PUBLISH);

        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        directPublishBO.setUseExposure(false);
        directPublishBO.setTopFlag(true);
        directPublishBO.setSaveVary(true);
        directPublishBO.setRefreshDuplicate(false);
        directPublishBO.setRecalculateTecServiceFee(true);
        directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.CANCEL_DISPATCH_AUTH);
        // 新代调参数
        directPublishBO.setUserDispatchTransportDTO(directPublishDTO.getUserDispatchTransportDTO());

        return process(processBO);
    }

    /**
     * 更新货源信息
     */
    @Override
    public DirectPublishResultVO updateGoodsInfo(UpdateGoodsInfoDTO updateGoodsInfoDTO) {
        DirectPublishDTO directPublishDTO = new DirectPublishDTO();
        directPublishDTO.setSrcMsgId(updateGoodsInfoDTO.getSrcMsgId());
        DirectPublishProcessBO directPublishProcessBO = buildDirectPublishBO(directPublishDTO, true);
        directPublishProcessBO.setOptEnum(PublishOptEnum.UPDATE_INFO);

        // 设置更新货源信息
        DirectPublishBO directPublishBO = directPublishProcessBO.getDirectPublishBO();
        directPublishBO.setUpdateGoodsInfoDTO(updateGoodsInfoDTO);
        // 不消耗曝光卡，不置顶
        directPublishBO.setUseExposure(false);
        directPublishBO.setTopFlag(false);
        directPublishBO.setSaveVary(false);
        directPublishBO.setRefreshDuplicate(false);
        directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.DIRECT_PUBLISH);

        return process(directPublishProcessBO);
    }

    /**
     * 自动重发货源，定时任务自动重发，无需登录
     */
    @Override
    public DirectPublishResultVO autoResend(DirectPublishDTO directPublishDTO) {
        DirectPublishProcessBO directPublishProcessBO = buildDirectPublishBO(directPublishDTO, false);
        directPublishProcessBO.setOptEnum(PublishOptEnum.AUTO_RESEND);

        // 设置自动重发
        DirectPublishBO directPublishBO = directPublishProcessBO.getDirectPublishBO();
        // 不消耗曝光卡
        directPublishBO.setUseExposure(false);
        directPublishBO.setTopFlag(false);
        directPublishBO.setSaveVary(true);
        directPublishBO.setRefreshDuplicate(true);
        directPublishBO.setRecalculateTecServiceFee(true);
        directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.DIRECT_PUBLISH);

        return process(directPublishProcessBO);
    }

    /**
     * 重新上架
     */
    @Override
    public DirectPublishResultVO reRelease(DirectPublishDTO directPublishDTO) {
        DirectPublishProcessBO directPublishProcessBO = buildDirectPublishBO(directPublishDTO, false);
        directPublishProcessBO.setOptEnum(PublishOptEnum.RERELEASE);

        // 设置自动重发
        DirectPublishBO directPublishBO = directPublishProcessBO.getDirectPublishBO();
        // 不消耗曝光卡
        directPublishBO.setUseExposure(false);
        directPublishBO.setTopFlag(false);
        directPublishBO.setSaveVary(true);
        directPublishBO.setRefreshDuplicate(true);
        directPublishBO.setBackoutReasonEnum(BackoutReasonEnum.DIRECT_PUBLISH);
        directPublishBO.setExtraRefreshTimes(6); // 免费赠送6次曝光次数

        return process(directPublishProcessBO);
    }

    /**
     * 处理发布流程
     */
    private DirectPublishResultVO process(DirectPublishProcessBO processBO) {
        // 校验器
        directPublishChecker.checkChain(processBO);

        // 构造器
        directPublishBuilder.buildChain(processBO);

        // 前置处理器
        directPublishPreHandler.preHandler(processBO);

        // 保存器
        directPublishSaver.saveDirect(processBO);

        // 后置处理器
        directPublishPostHandler.postHandler(processBO);

        // 返回接口响应结果
        return processBO.getResultDTO();
    }

    /**
     * 构建DirectPublishBO
     */
    private DirectPublishProcessBO buildDirectPublishBO(DirectPublishDTO directPublishDTO, boolean needLogin) {

        DirectPublishProcessBO processBO = new DirectPublishProcessBO();

        // 设置直接发布信息
        processBO.setDirectPublishBO(TransportPublishConverter.INSTANCE.toDirectPublishBO(directPublishDTO));
        // 设置旧货源信息
        TransportMainDO oldMainDO = transportMainService.getById(directPublishDTO.getSrcMsgId());
        if (oldMainDO == null) {
            log.error("直接发布-货源ID【{}】不存在！", directPublishDTO.getSrcMsgId());
            throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
        }
        // 校验货源状态 =>校验移动到checker里，重新上架操作需要成交状态
        /*if (Objects.equals(oldMainDO.getStatus(), TransportStatusEnum.DEAL.getCode())) {
            log.error("直接发布-货源【{}】已成交！", directPublishDTO.getSrcMsgId());
            throw new BusinessException(GoodsErrorCode.ERROR_NO_IN_PUBLISH);
        }*/
        processBO.setOldMain(oldMainDO);
        // 设置旧货源扩展信息
        TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(directPublishDTO.getSrcMsgId());
        processBO.setOldMainExtend(mainExtendDO == null ? new TransportMainExtendDO() : mainExtendDO);
        // 判断是不是历史货源
        boolean isHistoryGoods = oldMainDO.getCtime().before(DateUtil.beginOfDay(new Date()))
                || Objects.equals(oldMainDO.getStatus(), TransportStatusEnum.DEAL.getCode());
        processBO.getDirectPublishBO().setHistoryGoods(isHistoryGoods);

        // 如果需要登录
        if (needLogin) {
            // 设置用户信息
            processBO.setUser(userRemoteService.getUser(LoginHelper.getRequiredLoginUser().getUserId()));
            // 设置基础参数
            processBO.setBaseParam(LoginHelper.getBaseParam());
            // 校验是否是当前发货人
            if (!Objects.equals(oldMainDO.getUserId(), processBO.getUser().getId())) {
                log.error("直接发布-非当前发货人，货源【{}】", directPublishDTO.getSrcMsgId());
                throw new BusinessException(GoodsErrorCode.ERROR_NO_BELONG_TO_ONESELF);
            }
        } else { // 不需要登录，需要根据旧货源信息，生成登录用户信息
            // 设置用户信息
            processBO.setUser(userRemoteService.getUser(oldMainDO.getUserId()));
            // 设置基础参数
            BaseParamDTO baseParam = BaseParamDTO.builder()
                    .clientVersion(oldMainDO.getClientVersion())
                    .clientSign(oldMainDO.getPlatId())
                    .build();
            processBO.setBaseParam(baseParam);
        }
        return processBO;
    }

}