package com.teyuntong.goods.service.service.rpc.bi;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.remote.bi.BiGoodModelResult;
import com.teyuntong.goods.service.service.remote.bi.BiResponse;
import com.teyuntong.goods.service.service.remote.bi.BiService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 调用BI获取好货模型分数service
 *
 * <AUTHOR>
 * @since 2025/02/18 16:42
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BiRemoteService {

    private final BiService biService;
    private final Environment environment;

    /**
     * 查询好货模型分数
     * @param transport
     * @return
     */
    @SneakyThrows
    public BiGoodModelResult getGoodModel(TransportMainDO transport, TransportMainExtendDO mainExtendDO) {

        Map<String, Object> params = new HashMap<>();
        params.put("run_type", getRunType());
        if (transport.getUserId() != null) {
            params.put("user_id", transport.getUserId());
        }
        if (StringUtils.isNotBlank(transport.getPrice())) {
            params.put("price", transport.getPrice());
        }
        if (transport.getDistance() != null) {
            params.put("distance", transport.getDistance());
        }
        if (transport.getWeight() != null) {
            params.put("weight", transport.getWeight());
        }
        if (StringUtils.isNotBlank(transport.getStartCity())) {
            params.put("start_city", transport.getStartCity());
        }
        if (StringUtils.isNotBlank(transport.getDestCity())) {
            params.put("dest_city", transport.getDestCity());
        }
        if (transport.getLength() != null) {
            params.put("length", transport.getLength());
        }
        if (transport.getWide() != null) {
            params.put("width", transport.getWide());
        }
        if (transport.getHigh() != null) {
            params.put("height", transport.getHigh());
        }
        if (transport.getInfoFee() != null) {
            params.put("info_fee", transport.getInfoFee());
        }
        if (transport.getRefundFlag() != null) {
            params.put("refund_flag", transport.getRefundFlag());
        }
        if (transport.getStartLatitude() != null) {
            params.put("start_latitude", transport.getStartLatitude());
        }
        if (transport.getStartLongitude() != null) {
            params.put("start_longitude", transport.getStartLongitude());
        }
        if (transport.getDestLatitude() != null) {
            params.put("dest_latitude", transport.getDestLatitude());
        }
        if (transport.getDestLongitude() != null) {
            params.put("dest_longitude", transport.getDestLongitude());
        }
        if (transport.getSourceType() != null) {
            params.put("source_type", transport.getSourceType());
        }
        if (StringUtils.isNotBlank(transport.getStartDetailAdd())) {
            params.put("start_detail_add", transport.getStartDetailAdd());
        }
        if (StringUtils.isNotBlank(transport.getDestDetailAdd())) {
            params.put("dest_detail_add", transport.getDestDetailAdd());
        }
        if (transport.getExcellentGoods() != null) {
            params.put("excellent_goods", transport.getExcellentGoods());
        }
        if (transport.getExcellentGoodsTwo() != null) {
            params.put("excellent_goods_two", transport.getExcellentGoodsTwo());
        }
        if (transport.getPublishGoodsType() != null) {
            params.put("publish_goods_type", transport.getPublishGoodsType());
        }
        if (transport.getPublishGoodsType() != null) {
            params.put("publish_goods_type", transport.getPublishGoodsType());
        }
        if (StringUtils.isNotBlank(transport.getGoodTypeName())) {
            params.put("good_type_name", transport.getGoodTypeName());
        }
        if (mainExtendDO.getSuggestMinPrice() != null) {
            params.put("suggest_min_price", mainExtendDO.getSuggestMinPrice());
        }
        if (mainExtendDO.getSuggestMaxPrice() != null) {
            params.put("suggest_max_price", mainExtendDO.getSuggestMaxPrice());
        }
        if (mainExtendDO.getFixPriceFast() != null) {
            params.put("fix_price_fast", mainExtendDO.getFixPriceFast());
        }

        BiResponse<BiGoodModelResult> response = biService.goodsModel(params).execute().body();
        if (response != null && Objects.equals(200, response.getCode())) {
            if (response.getData() != null) {
                return JSONUtil.toBean(JSONUtil.parseObj(response.getData()), BiGoodModelResult.class);
            }
        }
        return new BiGoodModelResult();
    }

    /**
     * 查询抽佣分数
     * @param transport
     * @return
     */
    @SneakyThrows
    public BiGoodModelResult getGoodsModelPrice(TransportMainDO transport) {
        Map<String, Object> params = new HashMap<>();
        params.put("run_type", getRunType());


        if (transport.getUserId() != null) {
            params.put("user_id", transport.getUserId());
        }
        if (StringUtils.isNotBlank(transport.getPrice())) {
            params.put("price", transport.getPrice());
        }
        if (transport.getDistance() != null) {
            params.put("distance", transport.getDistance());
        }
        if (transport.getWeight() != null) {
            params.put("weight", transport.getWeight());
        }
        if (StringUtils.isNotBlank(transport.getStartCity())) {
            params.put("start_city", transport.getStartCity());
        }
        if (StringUtils.isNotBlank(transport.getDestCity())) {
            params.put("dest_city", transport.getDestCity());
        }
        if (transport.getLength() != null) {
            params.put("length", transport.getLength());
        }
        if (transport.getWide() != null) {
            params.put("width", transport.getWide());
        }
        if (transport.getHigh() != null) {
            params.put("height", transport.getHigh());
        }
        if (transport.getInfoFee() != null) {
            params.put("info_fee", transport.getInfoFee());
        }
        if (transport.getRefundFlag() != null) {
            params.put("refund_flag", transport.getRefundFlag());
        }
        log.info("获取抽佣分数调用BI接口 请求参数：{}", JSONObject.toJSONString(params));
        BiResponse<BiGoodModelResult> response = biService.goodsModelPrice(params).execute().body();
        if (response != null && Objects.equals(200, response.getCode())) {
            if (response.getData() != null) {
                return JSONUtil.toBean(JSONUtil.parseObj(response.getData()), BiGoodModelResult.class);
            }
        }
        return new BiGoodModelResult();
    }

    private String getRunType() {
        // bi环境参数: 1：test环境；2：dev环境；3：release环境；不传或者0为生产环境
        String env = environment.getActiveProfiles()[0];
        return switch (env) {
            case "prod" -> "0";
            case "test" -> "1";
            case "dev" -> "2";
            case "local" -> "2";
            case "release" -> "3";
            default -> "0";
        };
    }
}
