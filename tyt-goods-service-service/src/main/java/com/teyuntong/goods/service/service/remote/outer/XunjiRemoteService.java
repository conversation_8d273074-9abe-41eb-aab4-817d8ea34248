package com.teyuntong.goods.service.service.remote.outer;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.outer.export.service.client.qiwei.service.XunjiRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;

/**
 * 企微相关
 *
 * <AUTHOR>
 * @since 2024-11-01 10:24
 */
@Service
@FeignClient(name = "tyt-outer-export-service", path = "outer-export", contextId = "XunjiRpcService", fallbackFactory = XunjiRemoteService.XunjiFallbackFactory.class)
public interface XunjiRemoteService extends XunjiRpcService {
    class XunjiFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<XunjiRemoteService> {
        protected XunjiFallbackFactory() {
            super(true, XunjiRemoteService.class);
        }
    }
}
