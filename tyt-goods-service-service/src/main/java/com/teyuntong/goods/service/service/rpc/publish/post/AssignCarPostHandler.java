package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.client.transport.dto.SaveGoodsStatusDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.trade.service.client.orders.dto.AssignOrdersSaveBillDTO;
import com.teyuntong.trade.service.client.orders.dto.SaveWayBillRpcDTO;
import com.teyuntong.trade.service.client.orders.vo.SaveWayBillRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
@RequiredArgsConstructor
public class AssignCarPostHandler {

    private final OrdersRemoteService ordersRemoteService;


    public void assignCar(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        TransportMainDO transportMain = publishProcessBO.getTransportMain();

        try {
            if (publishBO.getDispatchTransport().equals(YesOrNoEnum.YES.getId()) && StringUtils.isNotBlank(publishBO.getCarTelPhone())){
                log.info("代调指派司机,srcMsgId:{},userId:{}", transportMain.getSrcMsgId(), transportMain.getUserId());
                SaveWayBillRpcDTO saveWayBillRpcDTO = new SaveWayBillRpcDTO();
                saveWayBillRpcDTO.setUserId(publishBO.getCarUserid());
                saveWayBillRpcDTO.setGoodsId(transportMain.getSrcMsgId());
                saveWayBillRpcDTO.setCarOwnerTelephone(publishBO.getCarTelPhone());
                if (StringUtils.isNotBlank(publishBO.getPrice())){
                    saveWayBillRpcDTO.setCarriageFee(Integer.parseInt(publishBO.getPrice()));
                }
                saveWayBillRpcDTO.setAgencyMoney(publishBO.getInfoFee().longValue());
                saveWayBillRpcDTO.setTecServiceFee(publishBO.getTecServiceFee().longValue());
                ordersRemoteService.saveWayBill(saveWayBillRpcDTO);
            }

        } catch (Exception e) {
            log.error("指派司机失败：", e);
        }

    }
}
