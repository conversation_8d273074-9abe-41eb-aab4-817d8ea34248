package com.teyuntong.goods.service.service.biz.userempower.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 同步YMM用户拒绝授权记录表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-05-15
 */
@Getter
@Setter
@TableName("tyt_syncgoods_user_reject")
public class TytSyncgoodsUserRejectDO {

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建时间
     */
    private Date createTime;
}
