package com.teyuntong.goods.service.service.rpc.publish;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.teyuntong.goods.service.client.navigation.dto.NavigationQueryDTO;
import com.teyuntong.goods.service.client.navigation.service.NavigationRpcService;
import com.teyuntong.goods.service.client.navigation.vo.NavigationResultVO;
import com.teyuntong.goods.service.client.publish.dto.*;
import com.teyuntong.goods.service.client.publish.service.TransportPublishRpcService;
import com.teyuntong.goods.service.client.publish.vo.*;
import com.teyuntong.goods.service.client.transport.dto.*;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;
import com.teyuntong.goods.service.client.transport.vo.TransportPublishVO;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.commission.bo.TecServiceFeeConfigComputeResult;
import com.teyuntong.goods.service.service.biz.cover.service.CoverGoodsConfigService;
import com.teyuntong.goods.service.service.biz.order.dto.SameTransportAvgPriceQueryDTO;
import com.teyuntong.goods.service.service.biz.order.dto.SameTransportAvgPriceResultDTO;
import com.teyuntong.goods.service.service.biz.order.mybatis.entity.TransportAfterOrderDataDO;
import com.teyuntong.goods.service.service.biz.order.service.TransportAfterOrderDataService;
import com.teyuntong.goods.service.service.biz.publish.mybatis.entity.DispatchAuthRulesDO;
import com.teyuntong.goods.service.service.biz.publish.service.DispatchAuthRulesService;
import com.teyuntong.goods.service.service.biz.publish.service.TransportAutoResendService;
import com.teyuntong.goods.service.service.biz.publish.service.TransportPopupTrackingLogService;
import com.teyuntong.goods.service.service.biz.publish.service.TransportPublishService;
import com.teyuntong.goods.service.service.biz.transport.dto.DispatchAuthRuleDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.*;
import com.teyuntong.goods.service.service.biz.transport.service.*;
import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.properties.PromptProperties;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytCityRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.user.ExcellentGoodsCardRemoteService;
import com.teyuntong.goods.service.service.remote.user.ThirdEnterpriseRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.builder.*;
import com.teyuntong.goods.service.service.rpc.publish.checker.*;
import com.teyuntong.goods.service.service.rpc.publish.commission.CalcCommissionTecFeeService;
import com.teyuntong.goods.service.service.rpc.publish.converter.TransportPublishConverter;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.goods.service.service.rpc.publish.post.*;
import com.teyuntong.infra.basic.resource.client.tytcity.vo.TytCityVo;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.trade.service.client.infofee.vo.TransportOrdersListVO;
import com.teyuntong.user.service.client.permission.dto.GainExcellentGoodsCardRpcDTO;
import com.teyuntong.user.service.client.user.vo.ApiDataUserCreditInfoRpcVO;
import com.teyuntong.user.service.client.user.vo.DwsNewIdentiwoDataRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import com.teyuntong.user.service.client.user.vo.UserSubRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Time;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import static cn.hutool.core.text.StrPool.COMMA;
import static com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant.DISPATCH_AUTH_USER;
import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.*;
import static com.teyuntong.goods.service.service.common.enums.PublishStyleEnum.HISTORY_PUBLISH;
import static com.teyuntong.goods.service.service.common.enums.PublishStyleEnum.TODAY_PUBLISH;

/**
 * 货源发布接口
 *
 * <AUTHOR>
 * @since 2024/12/12 16:18
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class TransportPublishRpcServiceImpl implements TransportPublishRpcService {

    private final TransportAutoResendService transportAutoResendService;
    private final TransportMainService transportMainService;
    private final TransportAfterOrderDataService transportAfterOrderDataService;
    private final TransportEnterpriseLogService transportEnterpriseLogService;
    private final ThirdEnterpriseRemoteService thirdEnterpriseRemoteService;
    private final GoodCarPriceTransportService goodCarPriceTransportService;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final DispatchCompanyService dispatchCompanyService;
    private final TytCityRemoteService tytCityRemoteService;
    private final DispatchCargoOwnerService dispatchCargoOwnerService;
    private final DispatchCooperativeService dispatchCooperativeService;
    private final SpecialGoodsBuilder specialGoodsBuilder;
    private final ThPriceService thPriceService;
    private final ExcellentGoodsCardRemoteService excellentGoodsCardRemoteService;
    private final ExcellentTransportChecker excellentTransportChecker;
    private final PromptProperties promptProperties;
    private final UserRemoteService userRemoteService;
    private final CalcCommissionTecFeeService calcCommissionTecFeeService;
    private final CoverGoodsConfigService coverGoodsConfigService;
    private final TransportBackendService transportBackendService;

    private final TransportLabelJsonBuilder transportLabelJsonBuilder;
    private final TransportPublishService transportPublishService;
    private final TransportMainExtendService transportMainExtendService;
    private final TransportPopupTrackingLogService transportPopupTrackingLogService;
    private final TransportExtendBuilder transportExtendBuilder;
    private final AutoResendTransportPostHandler autoResendTransportPostHandler;
    private final TransportPublishLogPostHandler transportPublishLogPostHandler;
    private final YmmTransportChecker ymmTransportChecker;
    private final SpecialTransportChecker specialTransportChecker;
    private final FixedTransportChecker fixedTransportChecker;
    private final SensitiveWordsChecker sensitiveWordsChecker;
    private final PublishPhoneChecker publishPhoneChecker;
    private final AllowPriceChecker allowPriceChecker;
    private final BackendTransportChecker backendTransportChecker;
    private final PublishPermissionChecker publishPermissionChecker;
    private final CarpoolTransportChecker carpoolTransportChecker;
    private final ActivityChecker activityChecker;
    private final AddressChecker addressChecker;
    private final UserPublishLimitChecker userPublishLimitChecker;
    private final UserAuthChecker userAuthChecker;
    private final PersonalDuplicateChecker personalDuplicateChecker;
    private final InvoiceTransportChecker invoiceTransportChecker;
    private final AddDispatchOwnerPostHandler addDispatchOwnerPostHandler;
    private final AddTransportHistoryHandler addTransportHistoryHandler;
    private final ExcellentPermissionPostHandler excellentPermissionPostHandler;
    private final AddMbCargoPostHandler addMbCargoPostHandler;
    private final AddMachineTypePostHandler addMachineTypePostHandler;
    private final BiTrickingPostHandler biTrickingPostHandler;
    private final InvoiceTransportSaveLogPostHandler invoiceTransportSaveLogPostHandler;
    private final SpecialtAutoAssignPostHandler specialtAutoAssignPostHandler;
    private final PublishPermissionPostHandler publishPermissionPostHandler;
    private final CommissionTransportPostHandler commissionTransportPostHandler;
    private final AddExtraRefreshPostHandler addExtraRefreshPostHandler;
    private final SendPublishMQPostHandler sendPublishMQPostHandler;
    private final InvoiceTransportAssignPostHandler invoiceTransportAssignPostHandler;
    private final ExposureCardGiveawayPostHandler exposureCardGiveawayPostHandler;
    private final DispatchTransportPoster dispatchTransportPoster;
    private final CommissionTecFeeBuilder commissionTecFeeBuilder;
    private final AutoConvertExcellentGoodsChecker autoConvertExcellentGoodsChecker;
    private final CalcSpecialGoodsPriceService calcSpecialGoodsPriceService;
    private final BaseParamChecker baseParamChecker;
    private final SameTransportPushPostHandler sameTransportPushPostHandler;
    private final PriceChangeCachePostHandler priceChangeCachePostHandler;
    private final NavigationRpcService navigationRpcService;
    private final TransportBaseChecker transportBaseChecker;
    private final DispatchAuthRulesService dispatchAuthRulesService;
    private final ABTestRemoteService abTestRemoteService;


    private final RedisUtil redisUtil;


    private static final BigDecimal DEFAULT_ENTERPRISE_TAX_RATE = new BigDecimal("6.6");
    private final static BigDecimal FIFTY = new BigDecimal(50);
    private static final BigDecimal FACTOR_0_7 = BigDecimal.valueOf(0.7);
    private static final BigDecimal FACTOR_0_8 = BigDecimal.valueOf(0.8);
    private final OrdersRemoteService ordersRemoteService;


    @Override
    public TransportPublishVO transportPublish(PublishDTO publishDTO) {

        PublishProcessBO publishProcessBO = beforeCheckConvert(publishDTO);


        // 拦截校验及添加部分货源参数
        checkAssert(publishProcessBO);

        // 组装发货实体
        TransportMainDO transportMain = createTransportMain(publishProcessBO);
        publishProcessBO.setTransportMain(transportMain);
        TransportMainExtendDO mainExtend = transportPublishService.createTransportMainExtend(publishProcessBO);
        publishProcessBO.setMainExtend(mainExtend);

        // 组装完发货实体后的后置处理
        dealOfCreated(publishProcessBO);

        // 处理完成后再同步一下数据
        TransportDO transport = new TransportDO();
        TransportExtendDO transportExtend = new TransportExtendDO();
        BeanUtils.copyProperties(transportMain, transport);
        BeanUtils.copyProperties(mainExtend, transportExtend);
        publishProcessBO.setTransport(transport);
        publishProcessBO.setTransportExtend(transportExtend);

        // 发布货源
        transportPublishService.publishing(publishProcessBO);
        //发货完成后的后置处理
        afterPublish(publishProcessBO);
        // 组装返回值
        return buildReturnVO(publishProcessBO);


    }

    private PublishProcessBO beforeCheckConvert(PublishDTO publishDTO) {
        PublishProcessBO publishProcessBO = new PublishProcessBO();

        // 业务参数
        PublishBO publishBO = TransportPublishConverter.INSTANCE.toPublishBO(publishDTO);
        publishProcessBO.setPublishBO(publishBO);
        // 获取当前登录用户信息
        UserRpcVO user = userRemoteService.getUser(LoginHelper.getRequiredLoginUser().getUserId());
        publishProcessBO.setUser(user);
        // 基础参数
        publishProcessBO.setBaseParam(LoginHelper.getBaseParam());


        // 先判断是否是当天发布的货源还是历史货源
        TransportMainDO oldMain = getOldTransportMain(publishProcessBO);
        TransportMainExtendDO oldMainExtend = getOldTransportMainExtend(oldMain);
        publishProcessBO.setOldMain(oldMain);
        publishProcessBO.setOldMainExtend(oldMainExtend);
        // 判断是否是小程序货源
        TransportBackendDO transportBackend = getBackendTransport(publishBO);
        publishProcessBO.setTransportBackend(transportBackend);

        // 先将需要转换的参数都转换完成
        buildConvert(publishProcessBO, user);
        log.info("发布货源参数转换完成,publishBO:{}", JSONUtil.toJsonStr(publishBO));
        return publishProcessBO;
    }

    /**
     * 设置秒抢货源标签
     */
    private void setSeckillGoodsFlag(PublishProcessBO publishProcessBO) {
        // 6700需求：秒抢货源长时间未成交会变成非秒抢，这列货源再操作不会变成秒抢。
        // 历史货源重新发布，或者没有转非秒抢标签，判断秒抢逻辑
        TransportMainDO oldMain = publishProcessBO.getOldMain();
        TransportMainExtendDO mainExtend = publishProcessBO.getMainExtend();
        TransportMainDO transportMain = publishProcessBO.getTransportMain();
        PublishBO publishBO = publishProcessBO.getPublishBO();
        if (!PublishStyleEnum.TODAY_PUBLISH.getCode().equals(publishBO.getPublishStyle())
                || !Objects.equals(TransportUtil.getLabelJson(oldMain).getSeckillDowngrade(), 1)) {
            publishProcessBO.setIsSeckillGoods(transportExtendBuilder.checkIsSeckillGoods(transportMain, mainExtend));
        }
    }

    /**
     * 组装返回值
     *
     * @param publishProcessBO
     */
    private TransportPublishVO buildReturnVO(PublishProcessBO publishProcessBO) {
        TransportDO transport = publishProcessBO.getTransport();
        TransportPublishVO transportPublishVO = new TransportPublishVO();
        BeanUtils.copyProperties(transport, transportPublishVO);
        transportPublishVO.setTsId(transport.getId());
        transportPublishVO.setHasDestDetail((StringUtils.isNotBlank(transport.getDestDetailAdd()) && !Objects.equals(transport.getDestDetailAdd(), transport.getDestArea())) ? 1 : 0);
        return transportPublishVO;

    }

    private TransportMainExtendDO getOldTransportMainExtend(TransportMainDO oldTransportMain) {
        if (oldTransportMain != null) {
            return transportMainExtendService.getBySrcMsgId(oldTransportMain.getSrcMsgId());
        }
        return null;
    }

    private void afterPublish(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        TransportMainDO oldTransportMain = publishProcessBO.getOldMain();
        TransportMainDO transportMain = publishProcessBO.getTransportMain();
        UserRpcVO user = publishProcessBO.getUser();

        // 新代调货源分配调度经理
        dispatchTransportPoster.handler(publishProcessBO);
        // 记录抽佣数据
        commissionTransportPostHandler.handler(publishProcessBO, true);
        // 如果是运满满货源，同步添加到运满满关系表中
        addMbCargoPostHandler.saveMbCargoSyncInfo(publishBO, transportMain);
        // 添加到个人货主
        addDispatchOwnerPostHandler.handler(publishProcessBO);
        //开票货源记录发布时货主的企业信息
        invoiceTransportSaveLogPostHandler.saveForPublish(transportMain, publishBO);
        // 如果是指派货源，调用履约的派单接口
        invoiceTransportAssignPostHandler.invoiceAssignCar(publishProcessBO.getTransportMain(), publishProcessBO.getPublishBO());
        // 扣减发货次数
        publishPermissionChecker.checkPublishPermission(publishProcessBO, true);
        // 更新用户发货次数(user已经将这个功能集成到)
//        publishPermissionPostHandler.updateAuthPermissionUsed(publishProcessBO);
        // 保存到历史表，货源发布新老映射表
        addTransportHistoryHandler.handler(publishProcessBO);
        // 如果是优车货源，扣减优车发货次数并更新优车发货卡状态
        excellentPermissionPostHandler.handler(publishProcessBO);
        // 更新发布次数及月发布次数
        publishPermissionPostHandler.updatePublishNum(publishBO, transportMain);
        // 如果是待审核货名，进入后台的待审核列表
        addMachineTypePostHandler.saveMachineType(publishProcessBO);
        // 货源诊断
        addExtraRefreshPostHandler.handler(publishProcessBO);
        // 如果勾选自动重发，添加到自动重发记录
        autoResendTransportPostHandler.saveAutoResendRecord(publishBO.getIsAutoResend(), user.getId(), transportMain.getSrcMsgId());
        // 记录埋点数据
        biTrickingPostHandler.savePublishTricking(publishProcessBO, publishBO);
        // 记录发货记录
        transportPublishLogPostHandler.handler(publishProcessBO);
        // 专车自动派单,发送mq
        specialtAutoAssignPostHandler.specialAssign(transportMain);
        // 货源发布后发送mq
        sendPublishMQPostHandler.handler(publishProcessBO);
        // 校验是否符合曝光卡发放规则
        exposureCardGiveawayPostHandler.handler(publishProcessBO);
        //首次由非相似货源变为相似货源push
        sameTransportPushPostHandler.handler(transportMain);
        //首次发货、手动编辑发布缓存记录
        priceChangeCachePostHandler.handler(publishProcessBO);
    }


    /**
     * 发货时判断是否是小程序货源
     *
     * @param publishBO
     * @return
     */
    private TransportBackendDO getBackendTransport(PublishBO publishBO) {
        TransportBackendDO transportBackend = null;
        if (publishBO.getBackendId() != null && !Objects.equals(publishBO.getBackendId(), YesOrNoEnum.NO.getId().longValue())) {
            transportBackend = transportBackendService.getById(publishBO.getBackendId());
        }
        if (transportBackend == null && publishBO.getSrcMsgId() != null && Objects.equals(publishBO.getIsBackendTransport(), YesOrNoEnum.YES.getId())) {
            transportBackend = transportBackendService.selectBySrcMsgId(publishBO.getSrcMsgId());
        }
        return transportBackend;
    }


    private void dealOfCreated(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        // 校验货源是否是重货
        personalDuplicateChecker.check(publishProcessBO, publishBO.getAssignCarTel());

        // 设置秒抢货源
        setSeckillGoodsFlag(publishProcessBO);

        // 组装货源标签
        transportLabelJsonBuilder.build(publishProcessBO);

        // 计算货源的抽佣信息
        commissionTecFeeBuilder.build(publishProcessBO);

    }

    /**
     * 发货组装处理
     *
     * @param publishProcessBO
     */
    private TransportMainDO createTransportMain(PublishProcessBO publishProcessBO) {

        PublishBO publishBO = publishProcessBO.getPublishBO();
        UserRpcVO user = publishProcessBO.getUser();
        BaseParamDTO baseParam = publishProcessBO.getBaseParam();
        TransportMainDO oldTransportMain = publishProcessBO.getOldMain();
        // 组装实体
        TransportMainDO transportMain = transportPublishService.buildTransportMain(publishBO, user, oldTransportMain, baseParam);
        // 设置用户信用积分及等级
        ApiDataUserCreditInfoRpcVO userCreditInfo = userRemoteService.getUserCreditInfo(user.getId());
        transportMain.setTotalScore(Optional.ofNullable(userCreditInfo).map(ApiDataUserCreditInfoRpcVO::getTotalScore).orElse(BigDecimal.ZERO));
        transportMain.setRankLevel(Optional.ofNullable(userCreditInfo).map(ApiDataUserCreditInfoRpcVO::getRankLevel).orElse(1));

        // 用户成交单数
        UserSubRpcVO userSub = userRemoteService.getUserSubById(user.getId());
        transportMain.setTradeNum(Optional.ofNullable(userSub).map(UserSubRpcVO::getDealNum).orElse(0));

        // 设置捂货时间
        setPriorityRecommendExpireTime(publishBO, transportMain, oldTransportMain);

        return transportMain;
    }

    private void setPriorityRecommendExpireTime(PublishBO publishBO, TransportMainDO transportMain, TransportMainDO oldTransportMain) {

        if (Objects.equals(publishBO.getPriorityRecommend(), YesOrNoEnum.YES.getId())) {
            // 当日货物编辑发布 保持原来捂货过期时间不变，新货源和历史货源重新发货 重新计算
            if (Objects.equals(publishBO.getPublishStyle(), TODAY_PUBLISH.getCode())) {
                transportMain.setPriorityRecommendExpireTime(oldTransportMain.getPriorityRecommendExpireTime());
            } else {
                Integer xSecond = coverGoodsConfigService.selectXTime();
                Date date = DateUtils.addSeconds(transportMain.getPubDate(), xSecond);
                transportMain.setPriorityRecommendExpireTime(date);
            }
        }
    }


    private void checkAssert(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        UserRpcVO user = publishProcessBO.getUser();
        TransportBackendDO transportBackend = publishProcessBO.getTransportBackend();
        //校验登录端
        baseParamChecker.checkClientSign(publishProcessBO.getBaseParam(), publishProcessBO.getOldMain(), publishProcessBO.getOldMainExtend());
        // 校验地址参数
        addressChecker.checkPublishAddress(publishBO);
        // 用户实名认证校验
        userAuthChecker.checkUserAuth(publishBO, user);
        // 发货限制校验
        userPublishLimitChecker.check(user);
        // 一口价货源限制校验
        fixedTransportChecker.checkFixedTransport(publishBO);
        // 校验货物装卸货时间、订金
        transportBaseChecker.checkBaseParams(publishBO);
        // 校验货物内容(备注)是否处罚敏感词
        sensitiveWordsChecker.checkSensitiveWords(publishBO, user);
        // 专车发货的校验（合作商、专车运价）
        specialTransportChecker.specialCheck(publishProcessBO, user);
        //验证电话是否为本人联系电话，人工派单不进行验证；
        publishPhoneChecker.checkPhone(publishBO, user);
        // 校验用户发货权益
        publishPermissionChecker.checkPublishPermission(publishProcessBO, false);
        // 校验优车权益限制
        excellentTransportChecker.checkExcellentGoods(publishBO, user);
        // 校验运费
        allowPriceChecker.checkPublishPrice(publishBO, user.getId());
        // 校验开票货源
        invoiceTransportChecker.checkInvoiceTransport(publishBO, user);
        // 小程序货源校验
        backendTransportChecker.checkBackend(publishBO, transportBackend, user);
        // 拼车校验
        carpoolTransportChecker.check(publishBO, user);
        // 6510拉新裂变活动
        activityChecker.checkActivity(user);
        // 发布优车2.0电议货源次数校验
        autoConvertExcellentGoodsChecker.checkExcellentGoodsTele(publishProcessBO);
    }


    /**
     * 获取历史货源信息
     *
     * @param processBO
     * @return
     */
    private TransportMainDO getOldTransportMain(PublishProcessBO processBO) {
        PublishBO publishBO = processBO.getPublishBO();
        Long oldSrcMsgId = publishBO.getSrcMsgId();
        TransportMainDO oldTransportMain = null;

        processBO.setOptEnum(PublishOptEnum.PUBLISH);

        if (oldSrcMsgId != null && oldSrcMsgId > 0L) {
            oldTransportMain = transportMainService.getTransportMainForId(oldSrcMsgId);
            if (oldTransportMain == null) {
                publishBO.setPublishStyle(PublishStyleEnum.NEW_PUBLISH.getCode());
            } else {
                if (oldTransportMain.getStatus() == 4) {
                    publishBO.setPublishStyle(PublishStyleEnum.NEW_PUBLISH.getCode());
                    processBO.setOptEnum(PublishOptEnum.PUBLISH);

                } else if ((oldTransportMain.getCtime().after(DateUtil.beginOfDay(new Date())))) {

                    publishBO.setPublishStyle(TODAY_PUBLISH.getCode());
                    processBO.setOptEnum(PublishOptEnum.EDIT);
                } else {
                    publishBO.setPublishStyle(HISTORY_PUBLISH.getCode());
                }
            }
        } else {
            publishBO.setPublishStyle(PublishStyleEnum.NEW_PUBLISH.getCode());
        }
        return oldTransportMain;
    }


    private void buildAddress(PublishBO publishBO) {

        String startCity = publishBO.getStartCity();
        String startArea = publishBO.getStartArea();
        String destCity = publishBO.getDestCity();
        String destArea = publishBO.getDestArea();

        if (StringUtils.isBlank(startCity)) {
            startCity = startArea;
        }
        if (StringUtils.isBlank(destCity)) {
            destCity = destArea;
        }
        TytCityVo startCityVo = null;
        if (StringUtils.isNotBlank(startCity) && StringUtils.isNotBlank(startArea)) {
            startCityVo = tytCityRemoteService.getRegxByName(startCity, startArea, "3");
            if (startCityVo == null) {
                // 提级匹配，比如澳门等
                startArea = startCity;
                startCityVo = tytCityRemoteService.getRegxByName(startArea, startCity, "3");
            }
        }
        if (startCityVo == null && publishBO.getStartCoordX() != null && publishBO.getStartCoordY() != null) {
            String startPx = publishBO.getStartCoordX().stripTrailingZeros().toPlainString();
            String startPy = publishBO.getStartCoordY().stripTrailingZeros().toPlainString();
            startCityVo = tytCityRemoteService.getCityByXY(startPx, startPy, "3");
        }
        if (startCityVo != null) {
            publishBO.setStartProvinc(startCityVo.getProvinceName());
            publishBO.setStartCity(startCityVo.getCityName());
            publishBO.setStartArea(startCityVo.getAreaName());
            publishBO.setStartCoordX(new BigDecimal(startCityVo.getPx()));
            publishBO.setStartCoordY(new BigDecimal(startCityVo.getPy()));
        }
        TytCityVo destCityVo = null;
        if (StringUtils.isNotBlank(destCity) && StringUtils.isNotBlank(destArea)) {
            destCityVo = tytCityRemoteService.getRegxByName(destCity, destArea, "3");
            if (destCityVo == null) {
                // 提级匹配，比如澳门等
                destArea = destCity;
                destCityVo = tytCityRemoteService.getRegxByName(destArea, destCity, "3");
            }
        }
        if (destCityVo == null && publishBO.getDestCoordX() != null && publishBO.getDestCoordY() != null) {
            String destPx = publishBO.getDestCoordX().stripTrailingZeros().toPlainString();
            String destPy = publishBO.getDestCoordY().stripTrailingZeros().toPlainString();
            destCityVo = tytCityRemoteService.getCityByXY(destPx, destPy, "3");
        }
        if (destCityVo != null) {
            publishBO.setDestProvinc(destCityVo.getProvinceName());
            publishBO.setDestCity(destCityVo.getCityName());
            publishBO.setDestArea(destCityVo.getAreaName());
            publishBO.setDestCoordX(new BigDecimal(destCityVo.getPx()));
            publishBO.setDestCoordY(new BigDecimal(destCityVo.getPy()));
        }

        String startPoint = updateAddressPoint(publishBO.getStartProvinc(), publishBO.getStartCity(), publishBO.getStartArea());
        String destPoint = updateAddressPoint(publishBO.getDestProvinc(), publishBO.getDestCity(), publishBO.getDestArea());
        publishBO.setStartPoint(startPoint);
        publishBO.setDestPoint(destPoint);
    }


    private String updateAddressPoint(String province, String city, String area) {
        if (StringUtils.isNotBlank(city) && StringUtils.isNotBlank(area)) {
            if (city.contains(province)) {
                if (area.contains(city)) {
                    return area;
                } else {
                    return city + area;
                }
            } else {
                if (area.contains(city)) {
                    return province + area;
                } else {
                    return province + city + area;
                }
            }
        } else {
            if (StringUtils.isNotBlank(city)) {
                if (city.contains(province)) {
                    return city;
                } else {
                    return province + city;
                }
            } else if (StringUtils.isNotBlank(area)) {
                if (area.contains(province)) {
                    return area;
                } else {
                    return province + area;
                }
            } else {
                return province;
            }
        }
    }


    private void buildConvert(PublishProcessBO publishProcessBO, UserRpcVO user) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        BaseParamDTO baseParam = LoginHelper.getBaseParam();


        // 如果是开票货源，要将订金修改为退还状态（开票货源订金都为退还）
        buildInvoiceInfoFee(publishBO);

        // 如果是编辑发布，去除备注的固定文案
        if (StringUtils.isNotBlank(publishBO.getRemark()) && publishBO.getSrcMsgId() != null) {
            String remarkPrompt = tytConfigRemoteService.getStringValue(GOODS_DETAIL_REMARK_PROMPT);
            if (StringUtils.isNotBlank(remarkPrompt) && publishBO.getRemark().endsWith(remarkPrompt)) {
                publishBO.setRemark(StringUtils.remove(publishBO.getRemark(), remarkPrompt));
            }
        }

        // 如果没有距离，调用地图补充距离
        buildDistance(publishBO);
        // 如果符合条件，自动转优车货源
        autoConvertExcellentGoods(publishBO, user, baseParam);

        // 发货类型新老版本兼容
        setExcellentGoodsType(publishBO, user, baseParam);

        // 校验及匹配出发地目的地地址
        buildAddress(publishBO);

        // 校验装卸货时间
        convertLoadingTime(publishBO);


        // 如果是优车2.0货源，就发一张优车发货卡
        if (Objects.equals(publishBO.getExcellentGoodsTwo(), ExcellentGoodsTwoEnum.YES.getCode())) {
            publishExcellentGoodsCard(publishBO, user);
        }
        // 开票货源转换,添加主体
        buildInvoiceTransport(publishBO);

        // 确定来源
        confirmSourceType(publishBO, user);

        // 标准货源
        if (publishBO.getMatchItemId() != null && publishBO.getMatchItemId() > 0 && publishBO.getMatchItemId() != 9999999) {
            publishBO.setIsStandard(YesOrNoEnum.NO.getId());
        } else {
            // 非标准货源
            publishBO.setIsStandard(YesOrNoEnum.YES.getId());
            if (publishBO.getMatchItemId() == null) {
                publishBO.setMatchItemId(-1);
            }
        }

        // 如是专车货源,计算专车运费及运费类型
        specialGoodsBuilder.build(publishProcessBO, user);


    }

    private void buildInvoiceInfoFee(PublishBO publishBO) {
        if (Objects.equals(publishBO.getInvoiceTransport(), YesOrNoEnum.YES.getId())) {
            // 如果是专票，则将订金改为退还状态
            publishBO.setRefundFlag(YesOrNoEnum.YES.getId());
            // 开票货源如果指派车方的话定金强制修改为0并且不在找货大厅显示
            if (StringUtils.isNotBlank(publishBO.getAssignCarTel())) {
                publishBO.setInfoFee(BigDecimal.ZERO);
                publishBO.setIsShow(YesOrNoEnum.NO.getId());
            }
            // 如果是分段支付，要计算总价
            if (Objects.equals(publishBO.getPaymentsType(), YesOrNoEnum.YES.getId())) {
                BigDecimal prepaidPrice = publishBO.getPrepaidPrice() == null ? BigDecimal.ZERO : publishBO.getPrepaidPrice();
                BigDecimal collectedPrice = publishBO.getCollectedPrice() == null ? BigDecimal.ZERO : publishBO.getCollectedPrice();
                BigDecimal receiptPrice = publishBO.getReceiptPrice() == null ? BigDecimal.ZERO : publishBO.getReceiptPrice();
                BigDecimal allPrice = collectedPrice.add(prepaidPrice).add(receiptPrice);
                publishBO.setPrice(allPrice.toPlainString());

            }
        }
    }

    /**
     * 补充距离
     *
     * @param publishBO
     */
    private void buildDistance(PublishBO publishBO) {
        try {
            if (publishBO.getDistance() == null || publishBO.getDistance().compareTo(BigDecimal.ZERO) <= 0) {
                NavigationQueryDTO navigationQueryDTO = new NavigationQueryDTO();
                navigationQueryDTO.setStartLongitude(publishBO.getStartLongitude());
                navigationQueryDTO.setStartLatitude(publishBO.getStartLatitude());
                navigationQueryDTO.setDestLongitude(publishBO.getDestLongitude());
                navigationQueryDTO.setDestLatitude(publishBO.getDestLatitude());
                navigationQueryDTO.setWeight(new BigDecimal(publishBO.getWeight()));
                NavigationResultVO navigationResultVO = navigationRpcService.navigationDistance(navigationQueryDTO);
                if (navigationResultVO != null && navigationResultVO.getDistance() != null) {
                    publishBO.setDistance(navigationResultVO.getDistance());
                }
            }
        } catch (Exception e) {
            log.error("补充距离异常", e);
        }
    }

    /**
     * 发货类型新老版本兼容
     * <pre>
     *  a. 老版本优车（1.0、2.0），在新版货源根据定价金额映射极速优车、快速优车、特惠优车，若原价格低于特惠优车或无价，映射为特惠优车
     *  b. 老版本优车，若在新版未成功获取优车定价的
     *      i. 有运费：用户出价+原来的议价方式（一口价、电议）
     *      ii. 无运费：普通找车
     *  c. 老版本货源若是优车电议，在新版本也展示为极速优车、快速优车、特惠优车+电议
     *  d. 老版本普货一口价，新版本展示为用户出价+一口价
     *  e. 老版本普货电议有价，新版本展示为用户出价+电议
     *  f. 老版本电议无价，新版本展示为普通找车
     *  g. 以上映射货源，在新版本APP进行直接发布时，按照原货源直接发布
     * </pre>
     */
    private void setExcellentGoodsType(PublishBO publishBO, UserRpcVO user, BaseParamDTO baseParam) {

        // 因为pc端不能发优车货源，所以pc发的货可能没有excellentGoods字段，所以这里需要补上,默认是非优车货源
        if (Objects.equals(baseParam.getClientSign(), ClientSignEnum.PC.getCode())) {
            publishBO.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
            publishBO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
            if (TransportUtil.hasPrice(publishBO.getPrice())) {
                publishBO.setPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode());
            } else {
                publishBO.setPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS.getCode());
            }
            return;
        }

        // 优车三挡价格，如果客户端没传，重新获取
        if (publishBO.getFixPriceMin() == null || publishBO.getFixPriceMax() == null || publishBO.getFixPriceFast() == null) {
            TransportCarryReq TransportCarryReq = buildCarryReq(publishBO, user);
            CarryPriceVO thPrice = thPriceService.getThPrice(TransportCarryReq);
            if (thPrice != null) {
                publishBO.setFixPriceMin(thPrice.getFixPriceMin());
                publishBO.setFixPriceMax(thPrice.getFixPriceMax());
                publishBO.setFixPriceFast(thPrice.getFixPriceFast());
                publishBO.setThMinPrice(thPrice.getThMinPrice());
                publishBO.setThMaxPrice(thPrice.getThMaxPrice());
            }
        }

        // 6710发货改版：新版本发货兼容老字段，老版本发货兼容新字段
        if (publishBO.getPublishGoodsType() != null) { // 新版本发货（该字段为新增字段）
            if (PublishGoodsTypeEnum.isExcellentGoods(publishBO.getPublishGoodsType())) {
                publishBO.setExcellentGoods(ExcellentGoodsEnums.EXCELLENT.getCode());
                publishBO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
            } else if (Objects.equals(publishBO.getPublishGoodsType(), PublishGoodsTypeEnum.SPECIAL_GOODS.getCode())) {
                publishBO.setExcellentGoods(ExcellentGoodsEnums.SPECIAL.getCode());
            } else {
                publishBO.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
            }
        } else { // 老版本发货

            if (ExcellentGoodsEnums.isNormal(publishBO.getExcellentGoods())) {
                // 普通发货映射有价映射为用户出价，无价映射为普通找车
                if (TransportUtil.hasPrice(publishBO.getPrice())) {
                    publishBO.setPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode());
                } else {
                    publishBO.setPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS.getCode());
                }
            } else if (ExcellentGoodsEnums.isSpecial(publishBO.getExcellentGoods())) {
                // 专车货源仍映射为专车货源
                publishBO.setPublishGoodsType(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode());
            } else {
                // 根据优车定价金额映射极速优车、快速优车、特惠优车
                if (publishBO.getFixPriceMin() != null && publishBO.getFixPriceMax() != null && publishBO.getFixPriceFast() != null) {
                    PublishGoodsTypeEnum goodsTypeEnum = TransportUtil.judgeExcellentGoodsLevel(publishBO.getPrice(), publishBO.getFixPriceMin(), publishBO.getFixPriceMax(), publishBO.getFixPriceFast());
                    // 原价格低于特惠优车或无价，映射为特惠优车
                    goodsTypeEnum = goodsTypeEnum == null ? PublishGoodsTypeEnum.EXCELLENT_GOODS : goodsTypeEnum;
                    publishBO.setPublishGoodsType(goodsTypeEnum.getCode());
                } else {
                    // 若在新版未成功获取优车定价，有价=>用户出价，无价=>普通找车
                    if (TransportUtil.hasPrice(publishBO.getPrice())) {
                        publishBO.setPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode());
                        publishBO.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
                        publishBO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
                    } else {
                        publishBO.setPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS.getCode());
                        publishBO.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
                        publishBO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
                    }
                }
            }
        }
    }

    private TransportCarryReq buildCarryReq(PublishBO publishBO, UserRpcVO user) {
        TransportCarryReq transportCarryBean = new TransportCarryReq();
        transportCarryBean.setStartProvince(publishBO.getStartProvinc());
        transportCarryBean.setStartCity(publishBO.getStartCity());
        transportCarryBean.setStartArea(publishBO.getStartArea());
        transportCarryBean.setDestProvince(publishBO.getDestProvinc());
        transportCarryBean.setDestCity(publishBO.getDestCity());
        transportCarryBean.setDestArea(publishBO.getDestArea());
        transportCarryBean.setGoodsName(publishBO.getTaskContent());
        transportCarryBean.setGoodsWeight(new BigDecimal(publishBO.getWeight()));
        transportCarryBean.setGoodsLength(publishBO.getLength());
        transportCarryBean.setGoodsWide(publishBO.getWide());
        transportCarryBean.setGoodsHigh(publishBO.getHigh());
        transportCarryBean.setExcellentGoods(publishBO.getExcellentGoods());
        transportCarryBean.setUserId(user.getId());
        transportCarryBean.setDistance(publishBO.getDistance() != null ? publishBO.getDistance() : BigDecimal.ZERO);
        transportCarryBean.setGoodTypeName(publishBO.getGoodTypeName());
        return transportCarryBean;
    }

    /**
     * 装卸货时间转换
     *
     * @param publishBO
     */
    private void convertLoadingTime(PublishBO publishBO) {
        if (publishBO.getLoadingTime() != null && publishBO.getLoadingTime().getTime() == 0) {
            publishBO.setLoadingTime(null);
        }
        if (publishBO.getBeginLoadingTime() != null && publishBO.getBeginLoadingTime().getTime() == 0) {
            publishBO.setBeginLoadingTime(null);
        }
        if (publishBO.getUnloadTime() != null && publishBO.getUnloadTime().getTime() == 0) {
            publishBO.setUnloadTime(null);
        }
        if (publishBO.getBeginUnloadTime() != null && publishBO.getBeginUnloadTime().getTime() == 0) {
            publishBO.setBeginUnloadTime(null);
        }

    }

    /**
     * 确定来源
     *
     * @param publishBO
     * @param user
     */
    private void confirmSourceType(PublishBO publishBO, UserRpcVO user) {


        // 新代调货源
        if (Objects.equals(publishBO.getDispatchTransport(), 1)) {
            publishBO.setSourceType(SourceTypeEnum.NEW_DISPATCH.getCode());
            return;
        }
        // 运满满货源
        if (Objects.equals(publishBO.getSourceType(), SourceTypeEnum.YMM.getCode())) {
            publishBO.setSourceType(SourceTypeEnum.YMM.getCode());
            return;
        }

        // 如果在内部名单中，统一都为代调或宏信
        List<DispatchCompanyDO> companyDOList = dispatchCompanyService.selectByUserId(user.getId());
        if (CollUtil.isNotEmpty(companyDOList)) {
            SourceTypeEnum sourceTypeEnum = SourceTypeEnum.DISPATCH;
            DispatchCooperativeDO cooperative = dispatchCooperativeService.selectByName("宏信建发");
            if (Objects.nonNull(cooperative) && cooperative.getId().equals(publishBO.getCargoOwnerId())) {
                sourceTypeEnum = SourceTypeEnum.HONGXIN;
            }
            publishBO.setSourceType(sourceTypeEnum.getCode());
            return;
        }

        publishBO.setSourceType(SourceTypeEnum.NORMAL.getCode());

    }

    private void buildInvoiceTransport(PublishBO publishBO) {
        String invoiceSubjectData = tytConfigRemoteService.getStringValue(INVOICE_SUBJECT_DATA, "1,JCZY");
        String[] split = invoiceSubjectData.split(COMMA);
        String subjectId = split[0];
        String subjectCode = split[1];
        if (publishBO.getInvoiceSubjectId() == null || StringUtils.isBlank(publishBO.getServiceProviderCode())) {
            publishBO.setInvoiceSubjectId(Long.valueOf(subjectId));
            publishBO.setServiceProviderCode(subjectCode);
        }
    }

    private void publishExcellentGoodsCard(PublishBO publishBO, UserRpcVO user) {
        GainExcellentGoodsCardRpcDTO goodsCardRpcDTO = new GainExcellentGoodsCardRpcDTO();
        goodsCardRpcDTO.setUserId(user.getId());
        goodsCardRpcDTO.setNew(false);
        Long excellentGoodsCardId = excellentGoodsCardRemoteService.gainExcellentGoodsCard(goodsCardRpcDTO);
        publishBO.setExcellentCardId(excellentGoodsCardId);

    }

    /**
     * 自动转优车货源
     *
     * @param publishBO
     */
    private void autoConvertExcellentGoods(PublishBO publishBO, UserRpcVO user, BaseParamDTO baseParam) {
        // 新版融合优车发货不自动转优车
        if (baseParam.getClientFusion() != null) {
            return;
        }
        // 拼车不自动转优车
        if (Objects.equals(UseCarTypeEnum.PART.getCode(), publishBO.getUseCarType())) {
            return;
        }
        // 是否是用户手动勾选优车发货
        publishBO.setUserManualExcellentGoods(Objects.equals(publishBO.getGoodCarPriceTransport(), 1));
        // 非专车货源如果满足条件，自动转成优车2.0货源
        if (Objects.equals(publishBO.getExcellentGoods(), ExcellentGoodsEnums.NORMAL.getCode())) {
            // 如果符合自动转成优车2.0条件，就打上标识，后续按照优车2.0来处理
            TransportCarryDTO transportCarryDTO = buildTransportCarryDTO(publishBO, user);
            if (goodCarPriceTransportService.checkTransportIsGoodCarPriceTransport(transportCarryDTO)) {
                log.info("自动转优车2.0货源成功，userId:{}", user.getId());
                publishBO.setGoodCarPriceTransport(1);
                publishBO.setAutomaticGoodCarPriceTransport(true);
                publishBO.setExcellentGoods(ExcellentGoodsEnums.EXCELLENT.getCode());
                publishBO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
            }
        }
    }

    private TransportCarryDTO buildTransportCarryDTO(PublishBO publishBO, UserRpcVO user) {
        TransportCarryDTO transportCarryDTO = new TransportCarryDTO();
        transportCarryDTO.setStartProvince(publishBO.getStartProvinc());
        transportCarryDTO.setStartCity(publishBO.getStartCity());
        transportCarryDTO.setStartArea(publishBO.getStartArea());
        transportCarryDTO.setDestProvince(publishBO.getDestProvinc());
        transportCarryDTO.setDestCity(publishBO.getDestCity());
        transportCarryDTO.setDestArea(publishBO.getDestArea());
        transportCarryDTO.setGoodsWeight(publishBO.getWeight());
        transportCarryDTO.setGoodsLength(publishBO.getLength());
        transportCarryDTO.setGoodsWide(publishBO.getWide());
        transportCarryDTO.setGoodsHigh(publishBO.getHigh());
        transportCarryDTO.setUserId(user.getId());
        transportCarryDTO.setDistance(publishBO.getDistance() != null ? publishBO.getDistance().toString() : "0");
        transportCarryDTO.setGoodTypeName(publishBO.getGoodTypeName());
        transportCarryDTO.setPrice(publishBO.getPrice());
        transportCarryDTO.setPublishType(publishBO.getPublishType());
        transportCarryDTO.setOldSrcMsgId(publishBO.getSrcMsgId());

        return transportCarryDTO;
    }

    /**
     * 返回自动重发剩余次数
     */
    @Override
    public int getAutoResendRemainTimes(Long userId) {
        return transportAutoResendService.getRemainTimes(userId);
    }

    /**
     * 我的货源页点击直接发布是否弹出出价弹窗接口
     *
     * @param srcMsgId 货源id
     */
    @Override
    public PopUpPriceBoxDTO isPopupPriceBox(Long srcMsgId) {
        log.info("直接发布是否弹出出价弹窗，开始，srcMsgId:{}", srcMsgId);
        PopUpPriceBoxDTO priceBoxDTO = new PopUpPriceBoxDTO();
        priceBoxDTO.setShowDialogType(0); // 默认旧弹窗
        priceBoxDTO.setPrompt("将生成一条相同内容的信息，是否发布？");

        TransportMainDO transport = transportMainService.getById(srcMsgId);
        // 货源在发布中且未过期 或 是有价货源，不显示出价弹窗
        boolean isHistoryTransport = transport.getCtime().before(DateUtil.beginOfDay(new Date()));
        if (transport.getStatus() == 1 && !isHistoryTransport || TransportUtil.hasPrice(transport.getPrice())) {
            log.error("直接发布是否弹出出价弹窗，货源不符合条件，srcMsgId:{}", srcMsgId);
            return priceBoxDTO;
        }

        Long userId = LoginHelper.getRequiredLoginUser().getUserId();

        // 获取推荐价格
        // 优先级1:取本人上一票相同货源成交价，相同标准：出发地、目的地、货物名称、长宽高重、调车数量均一致
        log.info("直接发布是否弹出出价弹窗，取本人上一票相同货源成交价，srcMsgId:{}", srcMsgId);
        TransportMainDO lastSameTransport = transportMainService.getLastSameDealTransport(transport);
        if (lastSameTransport != null) {
            priceBoxDTO.setShowDialogType(1);
            priceBoxDTO.setPrice(new BigDecimal(lastSameTransport.getPrice()).intValue());
            priceBoxDTO.setPrompt(promptProperties.getPriceBoxPrompt(1, priceBoxDTO.getPrice(), null));
        } else {
            // 优先级2:优先取本人上一票相似货源成交价（相似货源维度同已有功能）
            log.info("直接发布是否弹出出价弹窗，取相似货源成交价，srcMsgId:{}", srcMsgId);
            SameTransportAvgPriceResultDTO sameTransportAvgPrice = transportAfterOrderDataService.getSameTransportAvgPrice(srcMsgId, userId);
            if (sameTransportAvgPrice != null && sameTransportAvgPrice.getLastPrice() > 0) {
                priceBoxDTO.setShowDialogType(1);
                priceBoxDTO.setPrice(sameTransportAvgPrice.getLastPrice());
                priceBoxDTO.setPrompt(promptProperties.getPriceBoxPrompt(2, priceBoxDTO.getPrice(), null));
                // 优先级3:取大盘相似货源成交平均价（相似货源维度同已有功能）
            } else if (sameTransportAvgPrice != null) {
                priceBoxDTO.setShowDialogType(1);
                priceBoxDTO.setPrice(sameTransportAvgPrice.getAveragePrice().intValue());
                priceBoxDTO.setPrompt(promptProperties.getPriceBoxPrompt(3, priceBoxDTO.getPrice(), sameTransportAvgPrice.getSameCount()));
            }

            // 如果成交价低于优车2.0成本价，则不返回提示文案，金额设置为成本价
            if (priceBoxDTO.getPrice() != null) {
                CarryPriceVO carryPriceVo = thPriceService.getThPrice(transport);
                if (carryPriceVo != null && carryPriceVo.getThMinPrice() != null && priceBoxDTO.getPrice() < carryPriceVo.getThMinPrice()) {
                    log.info("本次获取相似货源平均成交价低于优车2.0成本价，不返回数据");
                    priceBoxDTO.setPrice(carryPriceVo.getThMinPrice());
                    priceBoxDTO.setPrompt("将生成一条相同内容的信息，是否发布？");
                }
            }
        }
        log.info("直接发布是否弹出出价弹窗，设置价格结束，srcMsgId:{}", srcMsgId);

        // 专票货源返回费率
        if (priceBoxDTO.getShowDialogType() == 1 && Objects.equals(transport.getInvoiceTransport(), 1)) {
            log.info("直接发布是否弹出出价弹窗，设置专票费率开始，srcMsgId:{}", srcMsgId);
            priceBoxDTO.setInvoiceTransport(1);
            priceBoxDTO.setEnterpriseTaxRate(DEFAULT_ENTERPRISE_TAX_RATE);
            TransportEnterpriseLogDO transportEnterpriseLogDO = transportEnterpriseLogService.getBySrcMsgId(srcMsgId);
            if (transportEnterpriseLogDO != null) {
                Long invoiceSubjectId = transportEnterpriseLogDO.getInvoiceSubjectId();
                try {
                    log.info("直接发布是否弹出出价弹窗，调用费率接口开始，userId:{},invoiceSubjectId:{}", userId, invoiceSubjectId);
                    WebResult<BigDecimal> webResult = thirdEnterpriseRemoteService.getTaxRate(invoiceSubjectId, userId);
                    if (webResult != null && webResult.ok()) {
                        priceBoxDTO.setEnterpriseTaxRate(webResult.getData());
                    }
                    log.info("直接发布是否弹出出价弹窗，调用费率接口结束，webResult:{}", JSON.toJSONString(webResult));
                } catch (Exception e) {
                    log.error("直接发布是否弹出出价弹窗，调用费率接口异常，srcMsgId:{}，userId:{}, invoiceSubjectId:{}", srcMsgId, userId, invoiceSubjectId);
                }
            }
        }

        return priceBoxDTO;
    }

    /**
     * 获取预计成交时长
     * 专车相关信息展示：
     * 1.是否展示司机驾驶此类货物
     * 2.是否隐藏装车联系电话、卸货联系电话、公里数、是否包含其他费用及对应校验
     *
     * @param dealMinutesDTO
     * @return
     */
    @Override
    public DealMinutesVO getDealMinutes(DealMinutesDTO dealMinutesDTO) {
        DealMinutesVO result = new DealMinutesVO();

        // 优车预计成交时长
        if (PublishGoodsTypeEnum.isExcellentGoods(dealMinutesDTO.getPublishGoodsType())) {
            if (!StringUtils.isAnyBlank(dealMinutesDTO.getStartCity(), dealMinutesDTO.getDestCity())) {
                TransportMainDO mainDO = new TransportMainDO();
                mainDO.setStartCity(dealMinutesDTO.getStartCity());
                mainDO.setDestCity(dealMinutesDTO.getDestCity());
                Integer excellentPricePopupPrompt = transportAfterOrderDataService.getExcellentPricePopupPrompt(mainDO);
                if (Objects.nonNull(excellentPricePopupPrompt)) {
                    if (Objects.equals(PublishGoodsTypeEnum.EXCELLENT_GOODS.getCode(), dealMinutesDTO.getPublishGoodsType())) {
                        excellentPricePopupPrompt = excellentPricePopupPrompt + 10;
                    } else if (Objects.equals(PublishGoodsTypeEnum.SUPER_QUICK_EXCELLENT_GOODS.getCode(), dealMinutesDTO.getPublishGoodsType())) {
                        excellentPricePopupPrompt = getRandomDealMinutes(excellentPricePopupPrompt);
                    }
                }
                result.setDealMinutes(excellentPricePopupPrompt);
            }
        }

        if (Objects.equals(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode(), dealMinutesDTO.getPublishGoodsType())) {
            // 专车预计成交时长
            Integer dealMinutes = tytConfigRemoteService.getIntValue(SPECIAL_CAR_DISPATCH_TIME_NORMAL, 10);
            if (!StringUtils.isAnyBlank(dealMinutesDTO.getStartCity(), dealMinutesDTO.getDestCity())) {
                int priceConfigCount = calcSpecialGoodsPriceService.countMatchPriceConfigCityAndRule(dealMinutesDTO.getStartCity(), dealMinutesDTO.getDestCity());
                if (priceConfigCount > 0) {
                    dealMinutes = tytConfigRemoteService.getIntValue(SPECIAL_CAR_DISPATCH_TIME_NOT_NORMAL, 20);
                }
            }
            result.setDealMinutes(dealMinutes);

            result.setShowDriverDriving(YesOrNoEnum.NO.getId());
            result.setHideSpecialCarRule(YesOrNoEnum.NO.getId());

            // 是否展示司机驾驶此类货物
            if (DrivingAbilityEnum.haveAbility(dealMinutesDTO.getGoodTypeName())) {
                SpecialCarPriceConfigDO priceConfig = calcSpecialGoodsPriceService.selectMatchPriceConfig(dealMinutesDTO.getUserId(), dealMinutesDTO.getCargoOwnerId(), dealMinutesDTO.getStartCity(), dealMinutesDTO.getDestCity(), dealMinutesDTO.getWeight());
                if (Objects.nonNull(priceConfig) && priceConfig.getDrivingFee().compareTo(new BigDecimal("0")) > 0) {
                    result.setShowDriverDriving(YesOrNoEnum.YES.getId());
                }
            }

            // 是否隐藏装车联系电话、卸货联系电话、公里数、是否包含其他费用及对应校验
            // 当用户是非代调账号，且货主身份（签约合作商）为“平台”、空、不在专车货主管理中时隐藏
            int count = dispatchCompanyService.countByUserId(dealMinutesDTO.getUserId());
            if (count == 0) {
                //非代调账号
                DispatchCargoOwnerDO dispatchCargoOwner = dispatchCargoOwnerService.selectByUserId(dealMinutesDTO.getUserId());
                if (dispatchCargoOwner == null) {
                    //不是专车货主
                    result.setHideSpecialCarRule(YesOrNoEnum.YES.getId());
                } else {
                    Integer ruleCount = dispatchCargoOwnerService.selectRuleCount(dealMinutesDTO.getUserId());
                    if (ruleCount > 0) {
                        //是专车货主并且签约合作商为平台或没有签约合作商
                        result.setHideSpecialCarRule(YesOrNoEnum.YES.getId());
                    }
                }
            }
        }
        return result;
    }

    /**
     * 极速优车：文案变更，预计X分钟内接单，X取值逻辑不变，快速*（0.7-0.8随机数）（如果快速算出来数据是60min以上，则极速=60min*0.8），取整
     *
     * @param excellentPricePopupPrompt
     * @return
     */
    public static Integer getRandomDealMinutes(Integer excellentPricePopupPrompt) {
        BigDecimal randomFactor = ThreadLocalRandom.current().nextInt(2) == 0 ? FACTOR_0_7 : FACTOR_0_8;
        excellentPricePopupPrompt = new BigDecimal(excellentPricePopupPrompt).multiply(randomFactor).setScale(0, RoundingMode.CEILING).intValue();
        return excellentPricePopupPrompt;
    }

    /**
     * 获取专车发货相关信息
     *
     * @return
     */
    @Override
    public SpecialCarInfoVO getSpecialCarInfo(SpecialCarInfoDTO dto) {
        SpecialCarInfoVO infoVO = new SpecialCarInfoVO();
        if (Objects.isNull(dto.getUserId())) {
            return infoVO;
        }

        infoVO.setUserType(UserTypeEnum.PLAT_USER.getCode());

        int count = dispatchCompanyService.countByUserId(dto.getUserId());
        if (count > 0) {
            infoVO.setUserType(UserTypeEnum.DISPATCH_USER.getCode());
            List<CooperativeVO> list = dispatchCooperativeService.selectListByName(dto.getCooperativeName());
            infoVO.setCooperativeList(list);
        } else {
            DispatchCargoOwnerDO owner = dispatchCargoOwnerService.selectSignedByUserId(dto.getUserId());
            Long platId = dispatchCooperativeService.selectPlatId();
            if (Objects.nonNull(owner) && !Objects.equals(owner.getCooperativeId(), platId)) {
                infoVO.setUserType(UserTypeEnum.SIGNED_USER.getCode());
            }
        }
        return infoVO;
    }

    /**
     * 直接发布时是否展示升级为优车发货弹窗
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public UpgradeExcellentDTO showExcellentPricePopup(Long srcMsgId) {
        UpgradeExcellentDTO upgradeExcellentDTO = new UpgradeExcellentDTO();
        TransportMainDO mainDO = transportMainService.getById(srcMsgId);

        // 有价普通货源判断弹窗是否展示
        if (Objects.isNull(mainDO) || !Objects.equals(mainDO.getExcellentGoods(), ExcellentGoodsEnums.NORMAL.getCode()) || StringUtils.isBlank(mainDO.getPrice()) || StringUtils.equals(mainDO.getPrice(), "0")) {
            return upgradeExcellentDTO;
        }
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        if (Objects.nonNull(baseParam)) {
            String clientVersion = baseParam.getClientVersion();
            if (StringUtils.isNotBlank(clientVersion) && NumberUtil.isNumber(clientVersion) && Integer.parseInt(clientVersion) >= 6710) {
                return upgradeExcellentDTO;
            }
        }

        TransportCarryDTO carryDTO = buildTransportCarryDTO(mainDO);
        GoodCarPriceTransportTabAndBIPriceDTO showGoodCarPriceTransportTab = goodCarPriceTransportService.isShowGoodCarPriceTransportTab(carryDTO);
        if (Objects.isNull(showGoodCarPriceTransportTab) || !showGoodCarPriceTransportTab.getShowTab()) {
            return upgradeExcellentDTO;
        }
        upgradeExcellentDTO.setShowPopup(true);
        upgradeExcellentDTO.setTitle("免费升级为优车发货");
        upgradeExcellentDTO.setPrompt("预计X分钟接单".replace("X", transportAfterOrderDataService.getExcellentPricePopupPrompt(mainDO).toString()));
        Integer publishType = PublishTypeEnum.FIXED.getCode();
        // 6680逻辑调整，只支持一口价
        /*if (Objects.equals(showGoodCarPriceTransportTab.getIsRouteSupportTeleNegotiation(), 1) &&
                Objects.equals(showGoodCarPriceTransportTab.getIsAllowTeleNegotiation(), 1)) {
            publishType = mainDO.getPublishType();
        }*/
        upgradeExcellentDTO.setPublishType(publishType);
        upgradeExcellentDTO.setThMinPrice(showGoodCarPriceTransportTab.getThMinPrice());
        upgradeExcellentDTO.setThMaxPrice(showGoodCarPriceTransportTab.getThMaxPrice());
        upgradeExcellentDTO.setFixPriceFast(showGoodCarPriceTransportTab.getFixPriceFast());
        upgradeExcellentDTO.setFixPriceMin(showGoodCarPriceTransportTab.getFixPriceMin());
        upgradeExcellentDTO.setFixPriceMax(showGoodCarPriceTransportTab.getFixPriceMax());

        return upgradeExcellentDTO;
    }

    private TransportCarryDTO buildTransportCarryDTO(TransportMainDO mainDO) {
        TransportCarryDTO transportCarryDTO = new TransportCarryDTO();
        transportCarryDTO.setStartProvince(mainDO.getStartProvinc());
        transportCarryDTO.setStartCity(mainDO.getStartCity());
        transportCarryDTO.setStartArea(mainDO.getStartArea());
        transportCarryDTO.setDestProvince(mainDO.getDestProvinc());
        transportCarryDTO.setDestCity(mainDO.getDestCity());
        transportCarryDTO.setDestArea(mainDO.getDestArea());
        transportCarryDTO.setGoodsWeight(mainDO.getWeight());
        transportCarryDTO.setGoodsLength(mainDO.getLength());
        transportCarryDTO.setGoodsWide(mainDO.getWide());
        transportCarryDTO.setGoodsHigh(mainDO.getHigh());
        transportCarryDTO.setUserId(mainDO.getUserId());
        transportCarryDTO.setDistance(mainDO.getDistance() != null ? mainDO.getDistance().toString() : "0");
        transportCarryDTO.setGoodTypeName(mainDO.getGoodTypeName());
        return transportCarryDTO;
    }

    /**
     * 我的货源页点击撤销是否弹出挽留弹窗接口
     */
    @Override
    public ShowRetentionDTO showRetention(Long srcMsgId) {
        ShowRetentionDTO showRetentionDTO = new ShowRetentionDTO();
        showRetentionDTO.setShowDialogType(1);
        showRetentionDTO.setSrcMsgId(srcMsgId);

        TransportMainDO transport = transportMainService.getTransportMainForId(srcMsgId);
        // 货源不在发布中，不显示
        if (transport == null || transport.getStatus() != 1) {
            log.warn("点击撤销是否弹出挽留弹窗，货源不是发布中，srcMsgId:{}", srcMsgId);
            return showRetentionDTO;
        }

        boolean hasPrice = TransportUtil.hasPrice(transport.getPrice());
        showRetentionDTO.setHasPrice(hasPrice ? 1 : 0);
        List<TransportAfterOrderDataDO> sameTransportList = transportAfterOrderDataService.getSameTransport(srcMsgId, null, false, 7);

        if (CollectionUtils.isEmpty(sameTransportList)) {
            showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, hasPrice, null));
        } else {
            boolean defaultRule = true;

            // 有价先判断近7天相似货源成交价高于货源价格
            if (hasPrice) {
                double avgPrice = sameTransportList.stream().map(TransportAfterOrderDataDO::getPrice).filter(StringUtils::isNotBlank).mapToInt(Integer::parseInt).average().orElse(0);
                BigDecimal dealPrice = new BigDecimal(avgPrice).divide(FIFTY, 0, RoundingMode.UP).multiply(FIFTY);
                if (dealPrice.intValue() > Integer.parseInt(transport.getPrice())) {
                    defaultRule = false;
                    TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(srcMsgId);
                    boolean isCarpool = Objects.nonNull(mainExtendDO) && Objects.equals(UseCarTypeEnum.PART.getCode(), mainExtendDO.getUseCarType());
                    if (!isCarpool) {
                        showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, true, dealPrice.intValue()));
                    }
                }
            }

            // 如果低于，或者无价货源，判断近7天相似货源成交时长中位数 - 等待时间
            if (defaultRule) {
                // 获取近7天所有相似货源成交时长中位数，单位毫秒
                long dealMedianDuration = getDealMedianDuration(sameTransportList);
                // 当前货源已发布时长
                long curTransportDuration = System.currentTimeMillis() - transport.getPubDate().getTime();
                // 等待时间超过90分钟
                long waitTime = (dealMedianDuration - curTransportDuration) / 1000 / 60;
                if (waitTime >= 90) {
                    showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, false, 90));
                } else if (waitTime >= 30) {
                    showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, false, (int) waitTime));
                } else {
                    showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, hasPrice, null));
                }
            }
        }

        // 设置任务区，无价展示填价，有价展示加价
        List<ShowRetentionDTO.ShowRetentionTaskDTO> taskList = new ArrayList<>();
        ShowRetentionDTO.ShowRetentionTaskDTO task = new ShowRetentionDTO.ShowRetentionTaskDTO();
        task.setType(!hasPrice ? 1 : 2);
        task.setPrompt(promptProperties.getRetentionPrompt(2, hasPrice, null));
        taskList.add(task);

        // 撤销并发布选项
        ShowRetentionDTO.ShowRetentionTaskDTO cancelTask = new ShowRetentionDTO.ShowRetentionTaskDTO();
        cancelTask.setType(4);
        cancelTask.setPrompt(promptProperties.getRetentionRepublish());
        taskList.add(cancelTask);
        showRetentionDTO.setTaskList(taskList);
        return showRetentionDTO;
    }

    @Override
    public ShowRetentionDTO showRetentionV2(Long srcMsgId) {
        ShowRetentionDTO showRetentionDTO = new ShowRetentionDTO();
        showRetentionDTO.setShowDialogType(1);
        showRetentionDTO.setSrcMsgId(srcMsgId);

        TransportMainDO transport = transportMainService.getTransportMainForId(srcMsgId);
        // 货源不在发布中，不显示
        if (transport == null || transport.getStatus() != 1) {
            log.warn("点击撤销是否弹出挽留弹窗，货源不是发布中，srcMsgId:{}", srcMsgId);
            return showRetentionDTO;
        }

        boolean hasPrice = TransportUtil.hasPrice(transport.getPrice());
        showRetentionDTO.setHasPrice(hasPrice ? 1 : 0);
        List<TransportAfterOrderDataDO> sameTransportList = transportAfterOrderDataService.getSameTransport(srcMsgId, null, false, 7);

        if (CollectionUtils.isEmpty(sameTransportList)) {
            showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, hasPrice, null));
        } else {
            boolean defaultRule = true;

            // 有价先判断近7天相似货源成交价高于货源价格
            if (hasPrice) {
                double avgPrice = sameTransportList.stream().map(TransportAfterOrderDataDO::getPrice).filter(StringUtils::isNotBlank).mapToInt(Integer::parseInt).average().orElse(0);
                BigDecimal dealPrice = new BigDecimal(avgPrice).divide(FIFTY, 0, RoundingMode.UP).multiply(FIFTY);
                if (dealPrice.intValue() > Integer.parseInt(transport.getPrice())) {
                    defaultRule = false;
                    TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(srcMsgId);
                    boolean isCarpool = Objects.nonNull(mainExtendDO) && Objects.equals(UseCarTypeEnum.PART.getCode(), mainExtendDO.getUseCarType());
                    if (!isCarpool) {
                        showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, true, dealPrice.intValue()));
                    }
                }
            }

            // 如果低于，或者无价货源，判断近7天相似货源成交时长中位数 - 等待时间
            if (defaultRule) {
                // 获取近7天所有相似货源成交时长中位数，单位毫秒
                long dealMedianDuration = getDealMedianDuration(sameTransportList);
                // 当前货源已发布时长
                long curTransportDuration = System.currentTimeMillis() - transport.getPubDate().getTime();
                // 等待时间超过90分钟
                long waitTime = (dealMedianDuration - curTransportDuration) / 1000 / 60;
                if (waitTime >= 90) {
                    showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, false, 90));
                } else if (waitTime >= 30) {
                    showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, false, (int) waitTime));
                } else {
                    showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, hasPrice, null));
                }
            }
        }

        // 设置任务区，无价展示填价，有价展示加价
        List<ShowRetentionDTO.ShowRetentionTaskDTO> taskList = new ArrayList<>();
        ShowRetentionDTO.ShowRetentionTaskDTO task = new ShowRetentionDTO.ShowRetentionTaskDTO();
        task.setType(!hasPrice ? 1 : 2);
        task.setPrompt(promptProperties.getRetentionPrompt(2, hasPrice, null));
        taskList.add(task);

        //货源是否有在线修改次数
        try {
            transportBaseChecker.checkOnlineEditTimes(srcMsgId);
            ShowRetentionDTO.ShowRetentionTaskDTO onlineEditTask = new ShowRetentionDTO.ShowRetentionTaskDTO();
            onlineEditTask.setType(5);
            onlineEditTask.setPrompt(promptProperties.getOnlineEdit());
            taskList.add(onlineEditTask);
        } catch (Exception e) {
            // 撤销并发布选项
            ShowRetentionDTO.ShowRetentionTaskDTO cancelTask = new ShowRetentionDTO.ShowRetentionTaskDTO();
            cancelTask.setType(4);
            cancelTask.setPrompt(promptProperties.getRetentionRepublish());
            taskList.add(cancelTask);
        }

        showRetentionDTO.setTaskList(taskList);
        return showRetentionDTO;
    }

    /**
     * 点击货源发布弹出出价弹窗接口
     *
     * @param TransportPublishDTO
     */
    @Override
    public PopUpPriceBoxDTO publishPopupPriceBox(TransportPublishDTO TransportPublishDTO) {
        log.info("点击货源发布弹出出价弹窗接口，入参，TransportPublishDTO:{}", JSON.toJSONString(TransportPublishDTO));
        PopUpPriceBoxDTO priceBoxDTO = new PopUpPriceBoxDTO();

        // 获取相似货源数量，返回不同文案
        TransportMainDO mainDO = TransportPublishConverter.INSTANCE.toMainDO(TransportPublishDTO);
        String similarityCode = transportMainService.genSimilarityCode(mainDO, false);
        int similarityCount = transportMainService.countSimilarityGoods(similarityCode, null);
        if (similarityCount == 0) {
            priceBoxDTO.setTitle(promptProperties.getPublishPriceBoxPrompt("title-no-similar", null, null));
        } else {
            priceBoxDTO.setTitle(promptProperties.getPublishPriceBoxPrompt("title-has-similar", String.valueOf(similarityCount), null));
        }
        priceBoxDTO.setContent(promptProperties.getPublishPriceBoxPrompt("content", null, null));

        // 有相似货源成交价，优先显示
        SameTransportAvgPriceQueryDTO priceQueryDTO = new SameTransportAvgPriceQueryDTO();
        priceQueryDTO.setStartCity(TransportPublishDTO.getStartCity());
        priceQueryDTO.setDestCity(TransportPublishDTO.getDestCity());
        priceQueryDTO.setDistance(TransportPublishDTO.getDistance() == null ? BigDecimal.ZERO : TransportPublishDTO.getDistance());
        priceQueryDTO.setGoodsWeight(TransportPublishDTO.getWeight());
        priceQueryDTO.setGoodTypeName(TransportPublishDTO.getGoodTypeName());
        priceQueryDTO.setHasPrice(true);
        SameTransportAvgPriceResultDTO sameTransportAvgPrice = transportAfterOrderDataService.getSameTransportAvgPrice(priceQueryDTO);
        if (sameTransportAvgPrice != null) {
            priceBoxDTO.setPrice(sameTransportAvgPrice.getAveragePrice().intValue());
            priceBoxDTO.setPrompt(promptProperties.getPublishPriceBoxPrompt("prompt", sameTransportAvgPrice.getDayNum(), sameTransportAvgPrice.getAveragePrice().toString()));
        } else {
            // 若未成功获取相似货源价格，则取优车2.0最低值展示，天数固定为7
            TransportCarryReq carryReq = TransportPublishConverter.INSTANCE.toCarryReq(TransportPublishDTO);
            carryReq.setUserId(LoginHelper.getLoginUser().getUserId());
            CarryPriceVO thPrice = thPriceService.getThPrice(carryReq);
            if (thPrice != null && thPrice.getFixPriceMin() != null) {
                priceBoxDTO.setPrice(thPrice.getFixPriceMin());
                priceBoxDTO.setPrompt(promptProperties.getPublishPriceBoxPrompt("prompt", "7", String.valueOf(thPrice.getFixPriceMin())));
            }
        }

        log.info("点击货源发布弹出出价弹窗接口，出参，priceBoxDTO:{}", JSON.toJSONString(priceBoxDTO));
        return priceBoxDTO;
    }

    /**
     * 获取近7天所有相似货源成交时长中位数，返回秒数
     */
    private long getDealMedianDuration(List<TransportAfterOrderDataDO> sameTransportList) {
        // 查询7天成交货源的id，发布时间
        List<Long> srcMsgIds = sameTransportList.stream().map(TransportAfterOrderDataDO::getSrcMsgId).toList();
        QueryWrapper<TransportMainDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "pub_date");
        queryWrapper.in("id", srcMsgIds);
        List<TransportMainDO> transportMainList = transportMainService.getBaseMapper().selectList(queryWrapper);

        // 获取成交时长
        List<Long> dealDuration = new ArrayList<>();
        Map<Long, Date> collect = transportMainList.stream().collect(Collectors.toMap(TransportMainDO::getId, TransportMainDO::getPubDate));
        for (TransportAfterOrderDataDO sameTransport : sameTransportList) {
            Date pubDate = collect.get(sameTransport.getSrcMsgId());
            if (pubDate != null) {
                dealDuration.add(sameTransport.getCreateTime().getTime() - pubDate.getTime());
            }
        }

        // 计算成交时长中位数
        dealDuration.sort(Comparator.comparingLong(t -> t));
        int length = dealDuration.size();
        long median;
        if (length % 2 == 0) {
            // 偶数个元素，取中间两个数的平均值
            median = (dealDuration.get(length / 2 - 1) + dealDuration.get(length / 2)) / 2;
        } else {
            // 奇数个元素，取中间的数
            median = dealDuration.get(length / 2);
        }
        return median;
    }

    /**
     * 货源发布、变更时弹窗埋点记录接口
     */
    @Override
    public void popupTracking(PopupTrackingLogDTO trackingLog) {
        transportPopupTrackingLogService.save(trackingLog);
    }

    @Override
    public PublishGoodsTypeResultVO getPublishTransportTypeData(PublishTransportTypeDataDTO dataDTO) {
        return transportPublishService.getPublishTransportTypeData(dataDTO);
    }

    @Override
    public CalcSpecialGoodsPriceResultDTO calcSpecialGoodsPrice(CalculatePriceDTO priceDTO) {
        return calcSpecialGoodsPriceService.calculatePriceV2(priceDTO);
    }

    /**
     * 获取发货帮助提示气泡是否需要展示以及多秒以后展示
     * 展示逻辑：当用户30天内发货量小于3单，且超过5s未离开页面，（单数、秒数通过公共资源配置，一个配置即可）
     */
    @Override
    public PopupConfigVo getHelpBtnPopupConfig() {
        PopupConfigVo popupConfigVo = new PopupConfigVo();
        popupConfigVo.setShowPopup(false);

        // 每个用户一天最多展示一次
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        String cacheValue = redisUtil.getString(RedisKeyConstant.CACHE_HELP_BTN_POPUP_KEY_PREFIX + userId);
        if (StringUtils.isNotEmpty(cacheValue)) {
            return popupConfigVo;
        }

        String configValue = tytConfigRemoteService.getStringValue(ConfigKeyConstant.HELP_BTN_POPUP_CONFIG, "3,5");
        String[] configArr = configValue.split(COMMA);
        // 配置单数 当前用户30天内发货量小于多少单
        int orderNum = Integer.parseInt(configArr[0]);

        int publishCount = transportMainService.getPublishCountByUserId(userId, DateUtils.addDays(new Date(), -30), new Date());
        if (publishCount < orderNum) {
            popupConfigVo.setShowPopup(true);
            // 配置秒数 超过多少秒未离开页面
            int seconds = Integer.parseInt(configArr[1]);
            popupConfigVo.setDelaySeconds(seconds);
        }

        return popupConfigVo;
    }

    /**
     * 发货帮助提示气泡展示以后上报
     */
    @Override
    public void reportPopup() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            // 每个用户一天内展示一次，从0点到24点
            Duration duration = Duration.between(new Date().toInstant(), DateUtil.endOfDay(new Date()).toInstant());
            redisUtil.set(RedisKeyConstant.CACHE_HELP_BTN_POPUP_KEY_PREFIX + loginUser.getUserId(), "1", duration);
        }
    }

    @Override
    public DriverAssignPopupVO getDriverAssignPopup(DriverAssignPopupDTO driverAssignPopupDTO) {
        DriverAssignPopupVO driverAssignPopupVO = new DriverAssignPopupVO();
        List<TransportOrdersListVO> invoiceInProcessOrderList = ordersRemoteService.getInvoiceInProcessOrderList(driverAssignPopupDTO.getDriverUserId());
        if (CollUtil.isEmpty(invoiceInProcessOrderList)) {
            return driverAssignPopupVO;
        }
        Set<Long> srcMsgIds = invoiceInProcessOrderList.stream().map(TransportOrdersListVO::getTsId).collect(Collectors.toSet());
        List<TransportMainDO> mainDOS = transportMainService.getBySrcMsgIds(new ArrayList<>(srcMsgIds));
        if (CollUtil.isEmpty(mainDOS)) {
            return driverAssignPopupVO;
        }
        // 如果装货截止日期在今天0点之前，需要剔除掉
        mainDOS = mainDOS.stream().filter(mainDO -> {
            if (mainDO.getBeginLoadingTime() == null && mainDO.getLoadingTime() == null) {
                Date ctime = mainDO.getCtime();
                return !ctime.before(DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -1));
            } else if (mainDO.getLoadingTime() != null) {
                return !mainDO.getLoadingTime().before(DateUtil.beginOfDay(new Date()));
            } else {
                return !mainDO.getBeginLoadingTime().before(DateUtil.beginOfDay(new Date()));
            }
        }).toList();

        if (CollUtil.isEmpty(mainDOS)) {
            return driverAssignPopupVO;
        }

        Set<Date> mainBeginLoadingSet = new TreeSet<>();
        Set<Date> mainLoadingSet = new TreeSet<>(Comparator.reverseOrder());
        mainDOS.forEach(mainDO -> {
            Date ctime = mainDO.getCtime();
            // 今明两日，随到随装
            if (mainDO.getBeginLoadingTime() == null && mainDO.getLoadingTime() == null) {
                mainBeginLoadingSet.add(DateUtil.beginOfDay(ctime));
                mainLoadingSet.add(DateUtil.offsetDay(DateUtil.beginOfDay(ctime), 2));
            } else if (mainDO.getBeginLoadingTime() == null) {
                mainBeginLoadingSet.add(mainDO.getLoadingTime());
                mainLoadingSet.add(mainDO.getLoadingTime());
            } else if (mainDO.getLoadingTime() == null) {
                mainBeginLoadingSet.add(mainDO.getBeginLoadingTime());
                mainLoadingSet.add(mainDO.getBeginLoadingTime());
            } else {
                mainBeginLoadingSet.add(mainDO.getBeginLoadingTime());
                mainLoadingSet.add(mainDO.getLoadingTime());
            }
        });

        Date beginLoadingTime = driverAssignPopupDTO.getBeginLoadingTime();
        beginLoadingTime = beginLoadingTime == null || beginLoadingTime.getTime() == 0 ? null : beginLoadingTime;
        Date loadingTime = driverAssignPopupDTO.getLoadingTime();
        loadingTime = loadingTime == null || loadingTime.getTime() == 0 ? null : loadingTime;
        if (beginLoadingTime == null && loadingTime == null) {
            beginLoadingTime = DateUtil.beginOfDay(new Date());
            loadingTime = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), 2);
        } else if (beginLoadingTime == null) {
            beginLoadingTime = loadingTime;
        } else if (loadingTime == null) {
            loadingTime = beginLoadingTime;
        }

        Date earliestLoadingTime = mainBeginLoadingSet.iterator().next();
        Date latestLoadingTime = mainLoadingSet.iterator().next();
        //如果有交集，就返回交集的区间
        if (!(loadingTime.before(earliestLoadingTime) || beginLoadingTime.after(latestLoadingTime))) {
            driverAssignPopupVO.setPopupType(1);
            driverAssignPopupVO.setBeginLoadingTime(earliestLoadingTime);
            driverAssignPopupVO.setEndLoadingTime(latestLoadingTime);
            return driverAssignPopupVO;
        }
        // 如果没有指派中的订单交集，就看看有没有重复货源
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        Set<Long> assignSrcMsgIdSet = invoiceInProcessOrderList.stream()
                .filter(order -> Objects.equals(order.getIsAssignOrder(), 1) && order.getUserId().equals(loginUser.getUserId()))
                .map(TransportOrdersListVO::getTsId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(assignSrcMsgIdSet)) {
            driverAssignPopupVO.setPopupType(3);
        } else {
            if (srcMsgIds.size() != assignSrcMsgIdSet.size()) {
                mainDOS = transportMainService.getBySrcMsgIds(new ArrayList<>(assignSrcMsgIdSet));
            }
            Optional<TransportMainDO> mainOptional = mainDOS.stream()
                    .sorted(Comparator.comparing(TransportMainDO::getId).reversed())
                    .toList().stream().findFirst();
            TransportMainDO transportMainDO = mainOptional.orElse(null);
            if (transportMainDO != null) {
                driverAssignPopupVO.setPopupType(2);
                TransportAssignVO transportAssignVO = new TransportAssignVO();
                BeanUtil.copyProperties(transportMainDO, transportAssignVO);
                Optional<TransportOrdersListVO> orderOptional = invoiceInProcessOrderList.stream().filter(order -> order.getTsId().equals(transportMainDO.getId())).findFirst();
                orderOptional.ifPresent(order -> transportAssignVO.setOrderCreateTime(order.getCreateTime()));
                driverAssignPopupVO.setTransportAssignVO(transportAssignVO);
            } else {
                driverAssignPopupVO.setPopupType(3);
            }
        }
        return driverAssignPopupVO;

    }

    @Override
    public PublishCheckVO publishCheck(PublishCheckDTO publishCheckDTO) {
        PublishCheckVO publishCheckVO = new PublishCheckVO();
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        // 调车数量大于1，专车货源、开票货源、拼车货源不授权
        if (!Objects.equals(publishCheckDTO.getShuntingQuantity(), 1)
                || Objects.equals(publishCheckDTO.getPublishGoodsType(), PublishGoodsTypeEnum.SPECIAL_GOODS.getCode())
                || Objects.equals(publishCheckDTO.getInvoiceTransport(), 1)
                || Objects.equals(publishCheckDTO.getUseCarType(), 2)) {
            log.info("调车数量大于1或者专车货源或者拼车货源或者开票货源，不检查授权,userId:{}", userId);
            return publishCheckVO;
        }
        Integer userType = abTestRemoteService.getUserType(DISPATCH_AUTH_USER, userId);
        if (Objects.equals(userType, 1)) {
            PublishProcessBO publishProcessBO = beforeCheckConvert(publishCheckDTO);
            // 组装发货实体
            TransportMainDO transportMain = transportPublishService.buildTransportMain(publishProcessBO.getPublishBO(), publishProcessBO.getUser(), publishProcessBO.getOldMain(), baseParam);
            publishProcessBO.setTransportMain(transportMain);
            TransportMainExtendDO mainExtend = transportPublishService.createTransportMainExtend(publishProcessBO);
            publishProcessBO.setMainExtend(mainExtend);

            DispatchAuthRuleDTO dispatchAuthRuleDTO = buildDispatchAuthRuleDTO(publishProcessBO);
            log.info("用户：{}查询代调授权表入参：{}", userId, JSONUtil.toJsonStr(dispatchAuthRuleDTO));
            DispatchAuthRulesDO dispatchAuthRulesDO = dispatchAuthRulesService.selectRules(dispatchAuthRuleDTO);
            if (dispatchAuthRulesDO != null) {
                publishCheckVO.setDispatchAuthRule(YesOrNoEnum.YES.getId());
            }
        }
        return publishCheckVO;
    }

    private DispatchAuthRuleDTO buildDispatchAuthRuleDTO(PublishProcessBO publishProcessBO) {
        DispatchAuthRuleDTO dispatchAuthRuleDTO = new DispatchAuthRuleDTO();
        TransportMainDO transportMain = publishProcessBO.getTransportMain();
        TransportMainExtendDO mainExtend = publishProcessBO.getMainExtend();
        UserRpcVO user = publishProcessBO.getUser();

        BeanUtils.copyProperties(transportMain, dispatchAuthRuleDTO);
        BeanUtils.copyProperties(mainExtend, dispatchAuthRuleDTO);
        dispatchAuthRuleDTO.setWeight(transportMain.getWeight() != null ? new BigDecimal(transportMain.getWeight()) : null);
        dispatchAuthRuleDTO.setPublishTime(new Time(System.currentTimeMillis()));

        DwsNewIdentiwoDataRpcVO userIdentity = userRemoteService.getDwsNewIdentiwoDataByUserId(user.getId());
        dispatchAuthRuleDTO.setUserIdentity(userIdentity != null ? userIdentity.getType() : null);
        dispatchAuthRuleDTO.setHavePrice(TransportUtil.hasPrice(transportMain.getPrice()) ? 1 : 0);
        return dispatchAuthRuleDTO;
    }
}
