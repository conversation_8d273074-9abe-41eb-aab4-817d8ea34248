package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportBackendDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportBackendService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportBackendUserService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 小程序货源后置处理
 *
 * <AUTHOR>
 * @since 2025/02/23 17:23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BackendTransportPostHandler {

    private final TransportBackendService transportBackendService;
    private final TransportBackendUserService transportBackendUserService;

    public void handler(DirectPublishProcessBO processBO) {
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        if (Objects.equals(directPublishBO.getIsBackendTransport(), 1)) {
            Long srcMsgId = directPublishBO.getSrcMsgId();
            TransportMainDO newMainDO = processBO.getTransportMain();
            TransportBackendDO transportBackend = transportBackendService.selectBySrcMsgId(srcMsgId);
            if (transportBackend != null && transportBackend.getStatus() != 2) {
                transportBackend.setStatus(3);
                transportBackend.setOrderStatus(31);
                transportBackend.setSrcMsgId(newMainDO.getSrcMsgId());
                transportBackend.setReceiverUserId(newMainDO.getUserId());
                transportBackend.setReceiverPhone(newMainDO.getUploadCellphone());
                transportBackend.setReceiverShowName(newMainDO.getUserShowName());
                transportBackend.setReceivingTime(new Date());
                transportBackend.setMtime(new Date());
                transportBackend.setFindCarType(1);
                transportBackendService.updateById(transportBackend);
                transportBackendUserService.updateByBackendIdAndBatch(transportBackend.getId(), transportBackend.getBatch());
            }
        }
    }
}
