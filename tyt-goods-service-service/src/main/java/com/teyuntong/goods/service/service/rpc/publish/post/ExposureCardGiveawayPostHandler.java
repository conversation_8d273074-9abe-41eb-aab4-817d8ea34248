package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.exposure.service.ExposureCardGiveawayService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 校验是否符合曝光卡发放规则
 *
 * <AUTHOR>
 * @since 2025/03/07 18:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExposureCardGiveawayPostHandler {

    private final ExposureCardGiveawayService exposureCardGiveawayService;

    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO processBO) {
        // 如果是直接发布，校验是否符合曝光卡发放规则
        if (PublishOptEnum.isPublish(processBO.getOptEnum())) {
            if (processBO.getTransportMain().getStatus() == 1) {
                exposureCardGiveawayService.checkAndSave(processBO.getTransportMain());
            }
        }
    }
}
