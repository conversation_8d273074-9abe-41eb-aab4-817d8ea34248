package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.custom.service.CsBusinessUserRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/03/06 17:05
 */
@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "csBusinessUserRemoteService",
        fallbackFactory = CsBusinessUserRemoteService.CsBusinessUserRemoteServiceFallbackFactory.class)
public interface CsBusinessUserRemoteService extends CsBusinessUserRpcService {
    @Component
    class CsBusinessUserRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<CsBusinessUserRemoteService> {
        protected CsBusinessUserRemoteServiceFallbackFactory() {
            super(true, CsBusinessUserRemoteService.class);
        }
    }
}
