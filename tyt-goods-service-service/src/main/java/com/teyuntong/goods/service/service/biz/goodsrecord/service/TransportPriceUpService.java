package com.teyuntong.goods.service.service.biz.goodsrecord.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.entity.TransportPriceUpDO;

/**
 * <p>
 * 货源加价表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
public interface TransportPriceUpService extends IService<TransportPriceUpDO> {

    /**
     * 返回最新一条货源的数据
     */
    TransportPriceUpDO getLastOne(Long srcMsgId);
}
