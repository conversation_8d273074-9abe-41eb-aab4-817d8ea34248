package com.teyuntong.goods.service.service.rpc.publish.converter;

import com.teyuntong.goods.service.client.publish.dto.DirectPublishDTO;
import com.teyuntong.goods.service.client.publish.dto.TransportPublishDTO;
import com.teyuntong.goods.service.client.publish.dto.UpdateGoodsInfoDTO;
import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.*;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/01/24 10:36
 */
@Mapper
public interface TransportPublishConverter {
    TransportPublishConverter INSTANCE = Mappers.getMapper(TransportPublishConverter.class);

    TransportMainDO toMainDO(TransportPublishDTO publishDTO);

    @Mapping(target = "startProvince", source = "startProvinc")
    @Mapping(target = "destProvince", source = "destProvinc")
    @Mapping(target = "goodsWeight", source = "weight")
    @Mapping(target = "goodsLength", source = "length")
    @Mapping(target = "goodsWide", source = "wide")
    @Mapping(target = "goodsHigh", source = "high")
    TransportCarryReq toCarryReq(TransportPublishDTO publishDTO);

    TransportMainDO copyMainDO(TransportMainDO mainDO);

    // 不同步main表id
    @Mapping(ignore = true, target = "id")
    TransportDO fromMainDO(TransportMainDO mainDO);

    TransportMainExtendDO copyMainExtendDO(TransportMainExtendDO mainExtendDO);

    // 不同步main extend表id
    @Mapping(ignore = true, target = "id")
    TransportExtendDO fromMainExtendDO(TransportMainExtendDO mainExtendDO);


    DirectPublishBO toDirectPublishBO(DirectPublishDTO directPublishDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tsId", source = "id")
    TransportHistoryDO toHistoryDO(TransportMainDO mainDO);

    PublishBO toPublishBO(PublishDTO publishDTO);
}
