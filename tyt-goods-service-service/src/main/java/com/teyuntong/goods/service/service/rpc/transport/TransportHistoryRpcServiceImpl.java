package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.service.TransportHistoryRpcService;
import com.teyuntong.goods.service.client.transport.vo.TransportHistoryVO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportHistoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/09/04 15:58
 */
@RestController
@RequiredArgsConstructor
public class TransportHistoryRpcServiceImpl implements TransportHistoryRpcService {

    @Autowired
    private TransportHistoryService transportHistoryService;

    @Override
    public TransportHistoryVO getLatestBySrcMsgId(Long srcMsgId) {
        return transportHistoryService.getLatestBySrcMsgId(srcMsgId);
    }
}
