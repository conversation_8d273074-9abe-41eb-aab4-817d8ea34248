package com.teyuntong.goods.service.service.rpc.publish.post;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.user.service.client.permission.vo.AuthPermissionRpcVO;
import com.teyuntong.user.service.client.user.dto.UpdateUserSubInfoRpcDTO;
import com.teyuntong.user.service.client.user.vo.UserSubRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;
import java.util.Objects;

import static com.teyuntong.goods.service.service.common.constant.RedisKeyConstant.MONTH_PUBLISH_NUM;
import static com.teyuntong.goods.service.service.common.enums.PublishStyleEnum.TODAY_PUBLISH;
import static org.dromara.easyes.common.constants.BaseEsConstants.ONE;

/**
 * 更新发货权益
 *
 * <AUTHOR>
 * @since 2025/02/23 17:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PublishPermissionPostHandler {

    private final UserPermissionRemoteService userPermissionRemoteService;
    private final UserRemoteService userRemoteService;
    private final RedisUtil redisUtil;

    /**
     * 更新发货权益使用记录
     *
     * @param basePublishProcessBO
     */
    @Async("threadPoolExecutor")
    public void updateAuthPermissionUsed(BasePublishProcessBO basePublishProcessBO) {
        AuthPermissionRpcVO authPermissionRpcVO = basePublishProcessBO.getAuthPermissionRpcVO();
        TransportMainDO transportMain = basePublishProcessBO.getTransportMain();
        if (authPermissionRpcVO != null && authPermissionRpcVO.getUsedRecordId() != null) {
            userPermissionRemoteService.updateSrcMsgIdToPermissionUsed(authPermissionRpcVO.getUsedRecordId(), transportMain.getSrcMsgId());
        }

    }

    /**
     * 更新当月发货次数和总发货次数
     *
     * @param publishBO
     * @param transportMain
     */
    @Async("threadPoolExecutor")
    public void updatePublishNum(PublishBO publishBO, TransportMainDO transportMain) {

        if (!Objects.equals(publishBO.getPublishStyle(), TODAY_PUBLISH.getCode())) {
            // 更新发货次数
            UserSubRpcVO userSub = userRemoteService.getUserSubById(transportMain.getUserId());
            if (userSub != null) {
                Integer publishNum = userSub.getPublishNum() == null ? 0 : userSub.getPublishNum();
                UpdateUserSubInfoRpcDTO userSubInfoRpcDTO = new UpdateUserSubInfoRpcDTO();
                userSubInfoRpcDTO.setPublishNum(publishNum + ONE);
                userSubInfoRpcDTO.setUserId(transportMain.getUserId());
                userRemoteService.updateUserSubInfo(userSubInfoRpcDTO);
            }
            // 更新当月发货次数
            Integer monthPublishNum = redisUtil.getInt(MONTH_PUBLISH_NUM + transportMain.getUserId());
            int monthNum = monthPublishNum == null ? 0 : monthPublishNum;
            //获取当月剩余秒数
            long cacheMillis = DateUtil.endOfMonth(new Date()).getTime() - DateUtil.date().getTime();
            redisUtil.set(MONTH_PUBLISH_NUM + transportMain.getUserId(), monthNum + 1, Duration.ofMillis(cacheMillis));
        }
    }


}
