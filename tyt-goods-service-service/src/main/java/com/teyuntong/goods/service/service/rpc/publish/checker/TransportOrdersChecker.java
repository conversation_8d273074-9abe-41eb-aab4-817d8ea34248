package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.client.publish.vo.PayUserInfoVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.DispatchTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.trade.service.client.orders.vo.TransportOrdersVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class TransportOrdersChecker {

    private final OrdersRemoteService ordersRemoteService;

    public void check(PublishBO publishBO, TransportMainDO oldTransportMain) {
        if (oldTransportMain == null) {
            return;
        }
        List<TransportOrdersVO> transportOrders = ordersRemoteService.getByTsId(oldTransportMain.getSrcMsgId());
        if (CollectionUtils.isEmpty(transportOrders)) {
            return;
        }
        transportOrders.sort(Comparator.comparing(TransportOrdersVO::getPayEndTime, Comparator.nullsLast(Comparator.reverseOrder())));
        for (TransportOrdersVO transportOrder : transportOrders) {
            if (transportOrder.getCostStatus() >= 15) {
                PayUserInfoVO payUserInfoVO = new PayUserInfoVO();
                payUserInfoVO.setOrderId(transportOrder.getId());
                payUserInfoVO.setTsOrderNo(transportOrder.getTsOrderNo());
                payUserInfoVO.setCarUserId(transportOrder.getPayUserId());
                payUserInfoVO.setCellPhone(transportOrder.getPayCellPhone());

                throw new BusinessException(GoodsErrorCode.GOODS_PAID, null, payUserInfoVO);
            }

            if (publishBO.getDispatchType().equals(DispatchTypeEnum.DISPATCH_ONLINE_EDIT.getCode()) && transportOrder.getOrderNewStatus() == 5) {
                if (publishBO.getInfoFee().compareTo(new BigDecimal(transportOrder.getPayAmount()).movePointLeft(2)) != 0
                        || publishBO.getTecServiceFee().compareTo(new BigDecimal(transportOrder.getTecServiceFee()).movePointLeft(2)) != 0) {
                    throw BusinessException.createException(GoodsErrorCode.GOODS_STATUS_ERROR.getCode(), "当前货源有待支付订单，不可修改订金、信息服务费");
                }
            }
        }

    }

}
