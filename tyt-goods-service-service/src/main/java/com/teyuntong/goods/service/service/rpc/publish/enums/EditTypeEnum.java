package com.teyuntong.goods.service.service.rpc.publish.enums;

import lombok.Getter;

@Getter
public enum EditTypeEnum {

    //1.撤销重新发货 2. 直接发布（加价，自动重发，更新货物信息等） 5在线编辑货源  6代调接单发布 7代调在线编辑

    PUBLISH(1),
    DIRECT_PUBLISH(2),
    ONLINE_EDIT(5),
    DISPATCH_TAKE_PUBLISH(6),
    DISPATCH_ONLINE_EDIT(7);
    private final Integer value;

    EditTypeEnum(Integer value) {
        this.value = value;
    }
}
