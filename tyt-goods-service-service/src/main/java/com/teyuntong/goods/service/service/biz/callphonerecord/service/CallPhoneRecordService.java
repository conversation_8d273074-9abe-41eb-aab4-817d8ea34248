package com.teyuntong.goods.service.service.biz.callphonerecord.service;


import com.teyuntong.goods.service.service.biz.callphonerecord.bean.GoodsContactTimeDTO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordDO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 拨打电话记录 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-04-07
 */
public interface CallPhoneRecordService {
    CallPhoneRecordDO getLatestCallRecord(List<Long> srcMsgIds);

    /**
     * 获取指定时间到今天的所有货源首次联系时长
     * 注意startTime的范围控制，避免慢查询
     *
     * @param startCity
     * @param destCity
     * @param startTime
     * @return
     */
    List<GoodsContactTimeDTO> getGoodsContactTimeByRoute(String startCity, String destCity, Date startTime);

}
