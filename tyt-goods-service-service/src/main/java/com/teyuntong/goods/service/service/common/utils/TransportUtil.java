package com.teyuntong.goods.service.service.common.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.PublishGoodsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 货源模块工具类
 *
 * <AUTHOR>
 * @since 2024/12/02 14:07
 */
@Slf4j
public class TransportUtil {

    /**
     * 判断是否有价，货源表price是字符串。
     * <pre>
     *  TransportUtil.hasPrice(null)  = false
     *  TransportUtil.hasPrice("")    = false
     *  TransportUtil.hasPrice(" ")   = false
     *  TransportUtil.hasPrice("0")   = false
     *  TransportUtil.hasPrice("0.0") = false
     * </pre>
     */
    public static boolean hasPrice(String price) {
        if (StringUtils.isBlank(price)) {
            return false;
        }
        return new BigDecimal(price).compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * 获取价格
     */
    public static Integer getPrice(String price) {
        if (StringUtils.isBlank(price)) {
            return null;
        }
        return Integer.parseInt(price);
    }

    /**
     * 获取价格
     */
    public static int getPriceDefaultZero(String price) {
        if (StringUtils.isBlank(price)) {
            return 0;
        }
        return Integer.parseInt(price);
    }

    /**
     * 判断是否有重量，不为空且>0
     */
    public static boolean hasWeight(String weight) {
        if (StringUtils.isBlank(weight)) {
            return false;
        }
        return new BigDecimal(weight).compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断是否有距离，不为空且>0
     */
    public static boolean hasDistance(BigDecimal distance) {
        return distance != null && distance.compareTo(BigDecimal.ZERO) > 0;
    }

    public static String formatUserName(String name, String userId) {
        String userName = " ";
        if (StringUtils.isNotBlank(name) && !"null".equals(name)) {
            userName = name;
        } else {
            userName = "用户" + userId;
        }
        return userName;
    }

    /**
     * 隐藏字符串中的手机号
     *
     * @param value
     * @return
     */
    public static String hidePhoneInStr(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        String reg = "\\d{11,}";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(value);
        while (matcher.find()) {
            value = value.replace(matcher.group(), "***");
        }
        return value;
    }

    /**
     * 无价货源判断
     */
    public static boolean nonPrice(String price) {
        return !TransportUtil.hasPrice(price);
    }

    /**
     * 处理参考参数
     */
    public static Integer getReferNumber(String numberStr) {
        if (StringUtils.isBlank(numberStr)) {
            return null;
        }
        return new BigDecimal(numberStr).movePointRight(2).intValue();
    }

    /**
     * 处理参考参数
     */
    public static Integer getReferNumber(BigDecimal reqNumber, String numberDecStr) {
        Integer referNumber = null;
        if (reqNumber != null) {
            referNumber = reqNumber.movePointRight(2).intValue();
        } else {
            if (StringUtils.isNotBlank(numberDecStr)) {
                BigDecimal numberDec = new BigDecimal(numberDecStr);
                referNumber = numberDec.movePointRight(2).intValue();
            }
        }
        return referNumber;
    }

    /**
     * 货源有货参返回true
     */
    public static boolean isValidSize(String size) {
        return !isInvalidSize(size);
    }

    /**
     * 货源长宽高如果为空或=0或=1返回true
     */
    public static boolean isInvalidSize(String size) {
        if (StringUtils.isBlank(size)) {
            return true;
        }
        BigDecimal num = new BigDecimal(size);
        return num.compareTo(BigDecimal.ZERO) == 0 || num.compareTo(BigDecimal.ONE) == 0;
    }

    /**
     * 货源长宽高如果为空或=0或=1返回true
     */
    public static boolean isInvalidSize(BigDecimal size) {
        if (null == size) {
            return true;
        }
        return size.compareTo(BigDecimal.ZERO) == 0 || size.compareTo(BigDecimal.ONE) == 0;
    }

    /**
     * 是否为有效的长宽高，长宽高不能为空，不能为0，不能为1，<=上限值
     *
     * @param size       长宽高字符串
     * @param upperLimit 上限
     */
    public static boolean isValidSize(String size, String upperLimit) {
        if (StringUtils.isNotBlank(size)) {
            BigDecimal num = new BigDecimal(size);
            return num.compareTo(BigDecimal.ZERO) != 0
                    && num.compareTo(BigDecimal.ONE) != 0
                    && num.compareTo(new BigDecimal(upperLimit)) <= 0;
        }
        return false;
    }

    /**
     * 计算货源使用曝光卡，预计增加XX名司机查看
     * 无价货源：
     * 1. 货源质量分数>= 21      当前查看司机人数*0.51
     * 2. 货源质量分数【18,21）  当前查看司机人数*0.58
     * 3. 货源质量分数【16,18）   当前查看司机人数*0.61
     * 4. 货源质量分数<16       当前查看司机人数*0.5
     * 有价货源：
     * 5. 货源质量分数>= 17     当前查看司机人数*0.2
     * 6. 货源质量分数【15,17）  当前查看司机人数*0.21
     * 7. 货源质量分数【1,15）   当前查看司机人数*0.29
     * 8. 货源质量分数< 1   当前查看司机人数*0.29
     */
    public static Integer expectViewCount(String price, BigDecimal goodsModelScore, Integer curViewCount) {
        if (curViewCount == null) {
            return 0;
        }
        int score = goodsModelScore == null ? 0 : goodsModelScore.intValue();
        if (hasPrice(price)) {
            // 有价货源的逻辑
            if (score >= 17) {
                return (int) (curViewCount * 0.2) + 1;
            } else if (score >= 15) {
                return (int) (curViewCount * 0.21) + 1;
            } else if (score >= 1) {
                return (int) (curViewCount * 0.29) + 1;
            } else {
                return (int) (curViewCount * 0.29) + 1;
            }
        } else {
            // 无价货源的逻辑
            if (score >= 21) {
                return (int) (curViewCount * 0.51) + 1;
            } else if (score >= 18) {
                return (int) (curViewCount * 0.58) + 1;
            } else if (score >= 16) {
                return (int) (curViewCount * 0.61) + 1;
            } else {
                return (int) (curViewCount * 0.5) + 1;
            }
        }
    }

    /**
     * 生成货源hashcode
     */
    public static String getNewHashCode(TransportMainDO transport) {

        String nowDay = DateUtil.today();
        Long userId = transport.getUserId();

        //判断唯一时，市县就可以确认。
        String startCity = transport.getStartCity();
        String startArea = transport.getStartArea();

        String destCity = transport.getDestCity();
        String destArea = transport.getDestArea();
        // 重量统一改成保留2位小数
        String weight = StringUtils.isBlank(transport.getWeight()) ? "" :
                new BigDecimal(transport.getWeight()).setScale(2, RoundingMode.HALF_UP).toString();


        List<String> textList = new ArrayList<>();

        textList.add(nowDay);
        textList.add(userId + "");
        textList.add(startCity);
        textList.add(startArea);
        textList.add(destCity);
        textList.add(destArea);
        textList.add(weight);

        String type = transport.getType();
        String brand = transport.getBrand();
        String goodTypeName = transport.getGoodTypeName();

        if (StringUtils.isBlank(type) && StringUtils.isBlank(brand) && StringUtils.isBlank(goodTypeName)) {
            //都为空，取前10
            String taskContent = transport.getTaskContent();
            if (taskContent.length() > 10) {
                taskContent = taskContent.substring(0, 10);
            }
            textList.add(taskContent);
        } else {
            textList.add(type);
            textList.add(brand);
            textList.add(goodTypeName);
        }

        String hashCodeText = StringUtils.join(textList, "|");
        String newHashCode = MD5Util.GetMD5Code(hashCodeText);
        log.info("生成货源hashcode，srcMsgId:{}，hashCodeText:{}，newHashCode:{}", transport.getSrcMsgId(), hashCodeText, newHashCode);
        return newHashCode;
    }

    /**
     * 获取货源标签json
     */
    public static TransportLabelJson getLabelJson(TransportMainDO transport) {
        TransportLabelJson labelJson = new TransportLabelJson();
        if (transport != null && transport.getLabelJson() != null) {
            labelJson = JSONUtil.toBean(transport.getLabelJson(), TransportLabelJson.class);
        }
        return labelJson;
    }

    /**
     * 计算吨公里价
     *
     * @param tunKmPrice 吨公里价
     * @param weight     吨重
     * @param distance   运距，单位公里
     */
    public static int calPrice(BigDecimal tunKmPrice, BigDecimal weight, BigDecimal distance) {
        if (tunKmPrice == null || weight == null || distance == null) {
            return 0;
        }
        return tunKmPrice.multiply(weight).multiply(distance).setScale(0, RoundingMode.HALF_UP).intValue();
    }

    /**
     * 根据价格判断新版优车档位: fixPriceMin < fixPriceFast < fixPriceMax
     *
     * @param price        运费
     * @param fixPriceMin  特惠优车价
     * @param fixPriceMax  极速优车价
     * @param fixPriceFast 快速优车价
     */
    public static PublishGoodsTypeEnum judgeExcellentGoodsLevel(String price, Integer fixPriceMin, Integer fixPriceMax, Integer fixPriceFast) {
        if (nonPrice(price) || fixPriceMin == null || fixPriceMax == null || fixPriceFast == null) {
            return null;
        }
        int intPrice = Integer.parseInt(price);
        if (intPrice >= fixPriceMax) {
            return PublishGoodsTypeEnum.SUPER_QUICK_EXCELLENT_GOODS;
        } else if (intPrice >= fixPriceFast) {
            return PublishGoodsTypeEnum.QUICK_EXCELLENT_GOODS;
        } else if (intPrice >= fixPriceMin) {
            return PublishGoodsTypeEnum.EXCELLENT_GOODS;
        } else {
            return null;
        }
    }

    /**
     * 长宽高重都有且都不为1或0
     */
    public static boolean isValidSize(TransportMainDO transport) {
        return isValidSize(transport.getWeight())
                && isValidSize(transport.getLength())
                && isValidSize(transport.getWide())
                && isValidSize(transport.getHigh());
    }

    /**
     * 判断价格是否超过优车最低价
     */
    public static boolean isExcellentPrice(String price, Integer fixPriceMin) {
        if (StringUtils.isBlank(price) || fixPriceMin == null) {
            return false;
        }
        return Integer.parseInt(price) >= fixPriceMin;
    }

    /**
     * 获取客户端版本号，目前APP是4位，PC端是6位。返回四位
     */
    public static int getClientVersion(String clientVersion) {
        if (StringUtils.isBlank(clientVersion)) {
            return Integer.MAX_VALUE;
        }
        int i = Integer.parseInt(clientVersion);
        return i % 10000;
    }
}
