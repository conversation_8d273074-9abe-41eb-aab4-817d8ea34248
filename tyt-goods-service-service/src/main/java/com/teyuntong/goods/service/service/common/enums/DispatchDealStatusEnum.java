package com.teyuntong.goods.service.service.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * 新代调处理状态
 *
 * <AUTHOR>
 * @since 2025/09/15 10:15
 */
@Getter
@RequiredArgsConstructor
public enum DispatchDealStatusEnum {

    CONFIRMING(1, "待确认"),
    DISPATCH_PUBLISH(2, "代调已发布"),
    DISPATCH_RELEASE(3, "代调取消，释放大厅"),
    TIMEOUT_RELEASE(4, "超时释放大厅"),
    CANCEL_AUTH(5, "用户取消授权"),
    ;

    /**
     * 货源是否是授权状态
     *
     * @param code
     * @return
     */
    public static boolean isAuthed(Integer code) {
        return Objects.equals(CONFIRMING.getCode(), code) || Objects.equals(DISPATCH_PUBLISH.getCode(), code);
    }

    private final Integer code;
    private final String name;

}
