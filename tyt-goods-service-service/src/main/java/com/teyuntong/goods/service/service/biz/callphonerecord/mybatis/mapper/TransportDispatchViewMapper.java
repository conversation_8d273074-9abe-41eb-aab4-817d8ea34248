package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 货源查看、联系统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@Mapper
public interface TransportDispatchViewMapper extends BaseMapper<TransportDispatchViewDO> {

    /**
     * 批量查询货源查看次数，联系次数
     *
     * @param srcMsgIdList
     * @return
     */
    List<TransportDispatchViewDO> selectViewAndContactCount(@Param("srcMsgIdList") List<Long> srcMsgIdList);

    // 统计某个货源的联系次数和查看次数，一个用户拨打多次算一次
    TransportDispatchViewDO countContactAndViewBySrcMsgId(Long msgId);

    Integer hasContactInSrcMsgIds(@Param("srcMsgIds") List<Long> srcMsgIds);

    TransportDispatchViewDO selectBySrcMsgIdAndCarUserId(@Param("srcMsgId") Long srcMsgId, @Param("userId") Long userId);
}
