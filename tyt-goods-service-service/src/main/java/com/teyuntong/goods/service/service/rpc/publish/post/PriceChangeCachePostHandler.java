package com.teyuntong.goods.service.service.rpc.publish.post;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.entity.TransportPriceUpDO;
import com.teyuntong.goods.service.service.biz.goodsrecord.service.TransportPriceUpService;
import com.teyuntong.goods.service.service.biz.transport.service.PriceChangeCacheService;
import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;
import java.util.Objects;

/**
 * 手动编辑发布或者加价缓存记录
 *
 * <AUTHOR>
 * @since 2025/07/17 18:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PriceChangeCachePostHandler {

    private final RedisUtil redisUtil;
    private final PriceChangeCacheService priceChangeCacheService;
    private final TransportPriceUpService transportPriceUpService;

    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO processBO) {
        //首次发货、手动编辑发布、填价、加价缓存记录
        if (processBO.getOptEnum() == PublishOptEnum.FILL_PRICE || processBO.getOptEnum() == PublishOptEnum.ADD_PRICE
                || processBO.getOptEnum() == PublishOptEnum.PUBLISH || processBO.getOptEnum() == PublishOptEnum.EDIT
                || processBO.getOptEnum() == PublishOptEnum.ONLINE_EDIT || processBO.getOptEnum() == PublishOptEnum.DISPATCH_TAKE_PUBLISH
                || processBO.getOptEnum() == PublishOptEnum.DISPATCH_ONLINE_EDIT) {

            Long srcMsgId = processBO.getTransportMain().getSrcMsgId();
            priceChangeCacheService.priceChangeCache(srcMsgId);

            // 记录货源变化
            saveGoodsPriceChange(processBO);
        }
    }

    /**
     * 记录货源变化
     */
    private void saveGoodsPriceChange(BasePublishProcessBO processBO) {
        Long srcMsgId = processBO.getTransportMain().getSrcMsgId();

        // 把原始价格，首次有价记录到缓存中
        String originalKey = RedisKeyConstant.GOODS_PRICE_ORIGINAL + srcMsgId;
        String earliestKey = RedisKeyConstant.GOODS_PRICE_EARLIEST + srcMsgId;

        int oldPrice = TransportUtil.getPriceDefaultZero(processBO.getOldMain() == null ? null : processBO.getOldMain().getPrice());
        int newPrice = TransportUtil.getPriceDefaultZero(processBO.getTransportMain().getPrice());

        long duration = DateUtil.between(new Date(), DateUtil.endOfDay(new Date()), DateUnit.SECOND);
        redisUtil.setIfAbsent(originalKey, newPrice, Duration.ofSeconds(duration));
        if (newPrice > 0) {
            redisUtil.setIfAbsent(earliestKey, newPrice, Duration.ofSeconds(duration));
        }

        // 如果货源存在价格变动，落库保存，货源首次发布也需要记录。
        Long oldSrcMsgId = processBO.getOldMain() == null ? null : processBO.getOldMain().getSrcMsgId();
        if (oldPrice != newPrice || !Objects.equals(srcMsgId, oldSrcMsgId)) {
            TransportPriceUpDO record = new TransportPriceUpDO();
            record.setSrcMsgId(srcMsgId);
            record.setUserId(processBO.getUser().getId());
            record.setPublishOpt(processBO.getOptEnum().name());
            record.setCreateTime(new Date());

            // 加价次数
            record.setUpNumber(redisUtil.getInt(ConfigKeyConstant.TRANSPORT_ADD_PRICE_TIMES + srcMsgId));
            // 新价、旧价、新价变动、原价、首次有价
            record.setBeforePrice(oldPrice);
            record.setAfterPrice(newPrice);
            record.setUpPrice(newPrice - oldPrice);
            record.setOriginalPrice(redisUtil.getInt(originalKey));
            record.setEarliestPrice(redisUtil.getInt(earliestKey));

            transportPriceUpService.save(record);

        }
    }
}
