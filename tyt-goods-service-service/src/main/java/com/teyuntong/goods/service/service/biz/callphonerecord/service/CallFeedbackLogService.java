package com.teyuntong.goods.service.service.biz.callphonerecord.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallFeedbackLogDO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 拨打反馈页面填写记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
public interface CallFeedbackLogService extends IService<CallFeedbackLogDO> {

    /**
     * 获取车主的货源反馈记录
     */
    CallFeedbackLogDO getLastFeedback(Long carUserId, Long srcMsgId);

    /**
     * 批量获取车主的货源反馈记录
     *
     * @return {srcMsgId:"最新一条反馈记录"}
     */
    Map<Long, String> getLastFeedback(Long carUserId, List<Long> srcMsgIds);

    /**
     * 是否填写过反馈页面
     */
    Integer hasFeedback(Long carUserId, Long srcMsgId);
}
