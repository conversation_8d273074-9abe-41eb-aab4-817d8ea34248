package com.teyuntong.goods.service.service.rpc.publish.bo;

import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.service.biz.commission.bo.TecServiceFeeConfigComputeResult;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.*;
import com.teyuntong.goods.service.service.remote.bi.BiGoodModelResult;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.user.service.client.permission.vo.AuthPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.Getter;
import lombok.Setter;

/**
 * 直接发布请求参数BO
 *
 * <AUTHOR>
 * @since 2025/02/11 10:05
 */
@Getter
@Setter
public class BasePublishProcessBO {

    /**
     * 用户信息
     */
    private UserRpcVO user;

    /**
     * 登录基础信息
     */
    private BaseParamDTO baseParam;

    /**
     * 历史货源信息
     */
    private TransportMainDO oldMain;
    /**
     * 历史货源扩展信息
     */
    private TransportMainExtendDO oldMainExtend;
    /**
     * 当前货源信息
     */
    private TransportMainDO transportMain;
    /**
     * 当前货源扩展信息
     */
    private TransportMainExtendDO mainExtend;

    /**
     * 当前货源信息
     */
    private TransportDO transport;
    /**
     * 当前货源扩展信息
     */
    private TransportExtendDO transportExtend;


    /**
     * 小程序货源信息
     */
    private TransportBackendDO transportBackend;


    /**
     * 是否是抽佣货源
     */
    private boolean commissionTransport = false;

    /**
     * 是否是秒抢货源
     */
    private Integer isSeckillGoods = 0;

    /**
     * 抽佣技术服务费计算结果
     */
    private TecServiceFeeConfigComputeResult configToComputeResult;

    /**
     * 权益鉴权信息
     */
    private AuthPermissionRpcVO authPermissionRpcVO;

    /**
     * 操作类型
     */
    private PublishOptEnum optEnum;

    /**
     * 是否满足抽佣条件 0：不满足 1：满足
     */
    private Integer meetCommissionRules = 0;
    /**
     * BI模型结果
     */
    private BiGoodModelResult biGoodModelResult;

    /**
     * 优车定价
     */
    private CarryPriceVO thPrice;

}
