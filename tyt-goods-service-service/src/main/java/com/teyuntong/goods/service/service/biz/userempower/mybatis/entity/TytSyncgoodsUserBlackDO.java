package com.teyuntong.goods.service.service.biz.userempower.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 同步YMM用户黑名单表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-05-15
 */
@Getter
@Setter
@TableName("tyt_syncgoods_user_black")
public class TytSyncgoodsUserBlackDO {

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人username
     */
    private String createUsername;

    /**
     * 创建人userid
     */
    private Long createUserId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人username
     */
    private String updateUsername;

    /**
     * 修改人userid
     */
    private Long updateUserId;
}
