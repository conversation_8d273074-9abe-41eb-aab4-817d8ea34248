package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportHistoryDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMappingDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportHistoryService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMappingService;
import com.teyuntong.goods.service.service.rpc.publish.bo.BasePublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.converter.TransportPublishConverter;
import com.teyuntong.goods.service.service.rpc.publish.enums.EditTypeEnum;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 保存历史货源handler
 *
 * <AUTHOR>
 * @since 2025/02/23 17:25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddTransportHistoryHandler {

    private final TransportHistoryService transportHistoryService;
    private final TransportMappingService transportMappingService;

    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO processBO) {
        TransportMainDO mainDO = processBO.getTransportMain();
        TransportMainExtendDO extendDO = processBO.getMainExtend();

        if (mainDO != null && mainDO.getId() != null) {
            TransportHistoryDO historyDO = TransportPublishConverter.INSTANCE.toHistoryDO(mainDO);
            BeanUtils.copyProperties(extendDO, historyDO, "id", "srcMsgId", "createTime", "modifyTime");
            if (processBO.getOptEnum() == PublishOptEnum.PUBLISH || processBO.getOptEnum() == PublishOptEnum.EDIT){
                historyDO.setEditType(EditTypeEnum.PUBLISH.getValue());
            } else if (processBO.getOptEnum() == PublishOptEnum.ONLINE_EDIT) {
                historyDO.setEditType(EditTypeEnum.ONLINE_EDIT.getValue());
            }else if (processBO.getOptEnum() == PublishOptEnum.DISPATCH_TAKE_PUBLISH) {
                historyDO.setEditType(EditTypeEnum.DISPATCH_TAKE_PUBLISH.getValue());
            } else if (processBO.getOptEnum() == PublishOptEnum.DISPATCH_ONLINE_EDIT) {
                historyDO.setEditType(EditTypeEnum.DISPATCH_ONLINE_EDIT.getValue());
            } else{
                historyDO.setEditType(EditTypeEnum.DIRECT_PUBLISH.getValue());
            }
            transportHistoryService.save(historyDO);
        }

        // 新增到货源发布新老映射表
        TransportMainDO oldMain = processBO.getOldMain();
        if (oldMain != null && mainDO != null && !Objects.equals(oldMain.getSrcMsgId(), mainDO.getSrcMsgId())) {
            TransportMappingDO mappingDO = new TransportMappingDO();
            mappingDO.setNewSrcMsgId(mainDO.getSrcMsgId());
            mappingDO.setOldSrcMsgId(oldMain.getSrcMsgId());
            mappingDO.setPublishOpt(processBO.getOptEnum().name());
            transportMappingService.save(mappingDO);
        }
    }

}
