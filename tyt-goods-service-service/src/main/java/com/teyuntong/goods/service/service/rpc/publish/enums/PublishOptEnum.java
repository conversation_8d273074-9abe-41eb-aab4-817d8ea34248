package com.teyuntong.goods.service.service.rpc.publish.enums;

/**
 * 发布操作类型
 *
 * <AUTHOR>
 * @since 2025/02/11 10:13
 */
public enum PublishOptEnum {

    PUBLISH, // 首次发布
    EDIT, // 编辑发布
    DIRECT, // 直接发布
    AUTO_RESEND, // 自动重发
    FILL_PRICE, // 填价
    ADD_PRICE, // 加价
    TRANSFER_FIXED, // 转一口价
    TRANSFER_TELE, // 转电议
    UPDATE_INFO, // 更新货源信息（长宽高重）
    REPUBLISH, // 曝光
    RERELEASE, // 重新上架
    ONLINE_EDIT, // 在线编辑;
    DISPATCH_TAKE_PUBLISH, // 新代调接单找车
    DISPATCH_ONLINE_EDIT, // 新代调在线编辑
    CANCEL_AUTH_PUBLISH, // 新代调取消授权，释放到大厅
    ;
    /**
     * 是否发布操作
     */
    public static boolean isPublish(PublishOptEnum publishOptEnum) {
        return publishOptEnum == PUBLISH || publishOptEnum == EDIT
                || publishOptEnum == DIRECT || publishOptEnum == AUTO_RESEND
                || publishOptEnum == RERELEASE || publishOptEnum == ONLINE_EDIT
                || publishOptEnum == DISPATCH_TAKE_PUBLISH || publishOptEnum == DISPATCH_ONLINE_EDIT;
    }

    /**
     * 发布中货源允许的操作
     */
    public static boolean isLiveOpt(PublishOptEnum publishOptEnum) {
        return publishOptEnum == FILL_PRICE || publishOptEnum == ADD_PRICE
                || publishOptEnum == TRANSFER_FIXED || publishOptEnum == TRANSFER_TELE
                || publishOptEnum == UPDATE_INFO || publishOptEnum == REPUBLISH || publishOptEnum == ONLINE_EDIT;
    }

    /**
     * 是否编辑操作
     */
    public static boolean isEdit(PublishOptEnum publishOptEnum) {
        return publishOptEnum == FILL_PRICE || publishOptEnum == ADD_PRICE
                || publishOptEnum == TRANSFER_FIXED || publishOptEnum == TRANSFER_TELE
                || publishOptEnum == UPDATE_INFO || publishOptEnum == ONLINE_EDIT;
    }
}
