package com.teyuntong.goods.service.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.car.service.TytSigningCarRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-user-service", path = "user", contextId = "signingCarRpcService", fallbackFactory = SigningCarRemoteService.SigningCarRemoteFallbackFactory.class)
public interface SigningCarRemoteService extends TytSigningCarRpcService {

    @Component
    class SigningCarRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<SigningCarRemoteService>{
        protected SigningCarRemoteFallbackFactory() {
            super(true, SigningCarRemoteService.class);
        }
    }
}