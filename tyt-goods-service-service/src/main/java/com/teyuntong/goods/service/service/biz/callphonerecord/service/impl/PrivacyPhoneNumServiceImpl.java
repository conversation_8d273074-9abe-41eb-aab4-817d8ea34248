package com.teyuntong.goods.service.service.biz.callphonerecord.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.aliyun.openservices.ons.shaded.commons.lang3.StringUtils;
import com.teyuntong.goods.service.client.callphonerecord.vo.PrivacyPhoneNumGoodIdReq;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.AxbUserFieldParam;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.PrivacyPhoneNumService;
import com.teyuntong.goods.service.service.biz.commission.mybatis.entity.TransportTecServiceFeeDO;
import com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.TransportTecServiceFeeMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.outer.AXBCommonRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.outer.AXBCommonRemoteService;
import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.ABTestVo;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.AxbBindReq;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.AxbBindVO;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.AxbInfoVO;
import com.teyuntong.outer.export.service.client.cticloud.axb.vo.AxbUpdateReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class PrivacyPhoneNumServiceImpl implements PrivacyPhoneNumService {

    private final TytConfigRemoteService tytConfigRemoteService;

    private final TransportMainService transportMainService;

    private final RedisUtil redisUtil;

    private final AXBCommonRemoteService axbCommonRemoteService;

    private final TransportTecServiceFeeMapper transportTecServiceFeeMapper;

    private final ABTestRemoteService abTestRemoteService;

    private final OrdersRemoteService ordersRemoteService;

    private final String ACCOUNT_TWO_TRANSPORT_USER_PRIVACY_PHONE_NUM_COUNT_CACHE_KEY = "accountTwoTransportUserPrivacyPhoneNumCountCacheKey:";

    @Override
    public String getPrivacyPhoneNum(PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq) {
        if (privacyPhoneNumGoodIdReq == null
                || privacyPhoneNumGoodIdReq.getGoodId() == null || privacyPhoneNumGoodIdReq.getOperateType() == null || privacyPhoneNumGoodIdReq.getDriverUserId() == null
                || StringUtils.isBlank(privacyPhoneNumGoodIdReq.getGoodUserPhone()) || StringUtils.isBlank(privacyPhoneNumGoodIdReq.getDriverUserPhone())) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }

        boolean operateTypeIsLegal = checkBizType(privacyPhoneNumGoodIdReq.getOperateType());
        if (!operateTypeIsLegal) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }

        String PrivacyPhoneNum = null;
        try {
            int operateType = privacyPhoneNumGoodIdReq.getOperateType();
            LocalDateTime expireTime;
            int expireTimeSec = 0;
            if (operateType == 1) {
                //找货
                Integer expireTimeConfig = tytConfigRemoteService.getIntValue("T_PRIVACY_PHONE_NUM_EXPIRE_TIME", 3);
                expireTimeSec = expireTimeConfig * 60 * 60;
            } else if (operateType == 2) {
                //订单
                Integer expireTimeConfig = tytConfigRemoteService.getIntValue("O_PRIVACY_PHONE_NUM_EXPIRE_TIME", 10);
                expireTimeSec = expireTimeConfig * 24 * 60 * 60;
            }

            List<AxbInfoVO> axbInfo = axbCommonRemoteService.getAxbInfo(1, privacyPhoneNumGoodIdReq.getGoodId()
                    , privacyPhoneNumGoodIdReq.getDriverUserPhone(), privacyPhoneNumGoodIdReq.getGoodUserPhone(), String.valueOf(privacyPhoneNumGoodIdReq.getDriverUserId()));
            boolean isHavePrivacyPhoneNum = false;

            log.info("获取虚拟号历史绑定结果：{}", JSONObject.toJSONString(axbInfo));
            if (axbInfo != null && !axbInfo.isEmpty()) {
                for (AxbInfoVO axbInfoVO : axbInfo) {
                    if (StringUtils.isNotBlank(axbInfoVO.getExtraField())
                            && axbInfoVO.getExpirationDate() != null
                            && privacyPhoneNumGoodIdReq.getDriverUserId().toString().equals(axbInfoVO.getExtraField())
                            && new Date().before(axbInfoVO.getExpirationDate())) {
                        isHavePrivacyPhoneNum = true;
                        PrivacyPhoneNum = axbInfoVO.getTelX();
                        break;
                    }
                }
            }
            if (!isHavePrivacyPhoneNum) {
                TransportMainDO transportMainDO = transportMainService.getById(privacyPhoneNumGoodIdReq.getGoodId());

                String axbUserFieldParamJsonString = makeAXBUserFieldParam(privacyPhoneNumGoodIdReq.getDriverUserId(), privacyPhoneNumGoodIdReq.getGoodId());
                log.info("构造虚拟号额外参数，内容：{}", axbUserFieldParamJsonString);

                int tianrunAccount = makeTianRunAccountChoose(privacyPhoneNumGoodIdReq);

                String cacheKey = ACCOUNT_TWO_TRANSPORT_USER_PRIVACY_PHONE_NUM_COUNT_CACHE_KEY
                        + transportMainDO.getUserId() + ":";

                Date today = new Date();
                if (tianrunAccount != 0) {
                    if (tianrunAccount == 2) {
                        //每个货主每Y（3）天绑定X（4）个后
                        int count = 0;
                        Integer num1 = redisUtil.getInt(cacheKey + DateUtil.beginOfDay(today).toDateStr());
                        if (num1 != null) {
                            count += num1;
                        }
                        Integer num2 = redisUtil.getInt(cacheKey + DateUtil.beginOfDay(DateUtil.offsetDay(today, -1)).toDateStr());
                        if (num2 != null) {
                            count += num2;
                        }
                        Integer num3 = redisUtil.getInt(cacheKey + DateUtil.beginOfDay(DateUtil.offsetDay(today, -2)).toDateStr());
                        if (num3 != null) {
                            count += num3;
                        }
                        if (count > 3) {
                            log.info("使用天润账户2绑定，超过次数限制，不再进行绑定");
                            return null;
                        }
                    }

                    AxbBindVO axbBindVO = axbCommonRemoteService.axbBind(new AxbBindReq(privacyPhoneNumGoodIdReq.getDriverUserPhone()
                            , privacyPhoneNumGoodIdReq.getGoodUserPhone(), 1, privacyPhoneNumGoodIdReq.getGoodId(), expireTimeSec
                            , privacyPhoneNumGoodIdReq.getDriverUserId().toString(), axbUserFieldParamJsonString, tianrunAccount));
                    if (axbBindVO != null && StringUtils.isNotBlank(axbBindVO.getTelX())) {
                        PrivacyPhoneNum = axbBindVO.getTelX();
                        if (tianrunAccount == 2) {
                            Integer num = redisUtil.getInt(cacheKey + DateUtil.beginOfDay(today).toDateStr());
                            if (num == null) {
                                redisUtil.set(cacheKey + DateUtil.beginOfDay(today).toDateStr(), 1, Duration.ofDays(4));
                            } else {
                                redisUtil.set(cacheKey + DateUtil.beginOfDay(today).toDateStr(), num + 1, Duration.ofDays(4));
                            }
                        }
                    }
                } else {
                    log.info("不绑定虚拟号");
                }
            }
        } catch (Exception e) {
            log.error("getPrivacyPhoneNum 获取虚拟号流程执行失败 原因：" + e);
        }
        return PrivacyPhoneNum;
    }

    private boolean checkBizType(Integer bizType) {
        return bizType == 1 || bizType == 2;
    }

    @Override
    public String makeAXBUserFieldParam(Long driverUserId, Long goodsId) {
        try {
            TransportMainDO transportMainDO = transportMainService.getById(goodsId);
            AxbUserFieldParam axbUserFieldParam = new AxbUserFieldParam();
            if (transportMainDO != null) {
                axbUserFieldParam.setSrcMsgId(transportMainDO.getSrcMsgId());
                axbUserFieldParam.setTransportUserId(transportMainDO.getUserId());
                axbUserFieldParam.setCarUserId(driverUserId);
                axbUserFieldParam.setPrice(transportMainDO.getPrice());
                axbUserFieldParam.setInfoFee(transportMainDO.getInfoFee() != null ? transportMainDO.getInfoFee().toString() : null);
                axbUserFieldParam.setTecServiceFee(transportMainDO.getTecServiceFee() != null ? transportMainDO.getTecServiceFee().toString() : null);
                axbUserFieldParam.setRefundFlag(transportMainDO.getRefundFlag() != null ? transportMainDO.getRefundFlag() == 0 ? "不退还" : "退还" : null);
                axbUserFieldParam.setDistance(String.valueOf(transportMainDO.getDistance()));
                axbUserFieldParam.setStartPoint(transportMainDO.getStartPoint());
                axbUserFieldParam.setDestPoint(transportMainDO.getDestPoint());
                List<String> transportTypeList = makeAXBUserFieldParamTransportTypeParam(transportMainDO);
                if (CollectionUtils.isNotEmpty(transportTypeList)) {
                    axbUserFieldParam.setTransportType(transportTypeList);
                }
                return JSON.toJSONString(axbUserFieldParam);
            }
        } catch (Exception e) {
            log.info("构造虚拟号额外参数异常，原因：", e);
            return null;
        }
        return null;
    }

    private List<String> makeAXBUserFieldParamTransportTypeParam(TransportMainDO transportMain) {
        List<String> result = new ArrayList<>();
        if (transportMain.getSourceType() != null) {
            if (transportMain.getSourceType() == 4) {
                result.add("YMM同步货源");
            }
        }
        if (transportMain.getExcellentGoods() != null) {
            if (transportMain.getExcellentGoods() == 1) {
                result.add("优车1.0");
            } else if (transportMain.getExcellentGoods() == 2) {
                result.add("专车");
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(transportMain.getLabelJson())) {
            TransportLabelJson transportLabelJson = JSON.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
            if (transportLabelJson != null && transportLabelJson.getGoodCarPriceTransport() != null && transportLabelJson.getGoodCarPriceTransport() == 1) {
                result.add("优车2.0");
            }
        }
        if (CollectionUtils.isEmpty(result)) {
            result.add("普通");
        }
        return result;
    }

    @Override
    public void updateAXBExpirationDate(Long driverUserId, Long goodsId) {
        if (driverUserId == null || goodsId == null) {
            return;
        }
        try {
            //如果虚拟号开关为关闭状态，则不进行续期
            Integer isUsePrivacyPhoneNum = tytConfigRemoteService.getIntValue("USE_PRIVACY_PHONE_NUM", 1);
            if (isUsePrivacyPhoneNum != 1) {
                return;
            }

            Integer expireTimeConfig = tytConfigRemoteService.getIntValue("O_PRIVACY_PHONE_NUM_EXPIRE_TIME", 10);
            int expireTimeSec = expireTimeConfig * 24 * 60 * 60;
            //货源成交延长虚拟号过期时间
            List<AxbInfoVO> axbInfo = axbCommonRemoteService.getAxbInfo(1, goodsId, null, null, String.valueOf(driverUserId));
            List<AxbInfoVO> needSaveNewExpirationDateAxbInfos = new ArrayList<>();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(axbInfo)) {
                for (AxbInfoVO axbInfoVO : axbInfo) {
                    if (StringUtils.isNotBlank(axbInfoVO.getExtraField())
                            && axbInfoVO.getExpirationDate() != null
                            && String.valueOf(driverUserId).equals(axbInfoVO.getExtraField())
                            && new Date().before(axbInfoVO.getExpirationDate())) {
                        needSaveNewExpirationDateAxbInfos.add(axbInfoVO);
                    }
                }
            }
            if (!needSaveNewExpirationDateAxbInfos.isEmpty()) {
                StringBuffer AxbUpdateIds = new StringBuffer();
                for (AxbInfoVO needSaveNewExpirationDateAxbInfo : needSaveNewExpirationDateAxbInfos) {
                    String axbUserFieldParamJsonString = makeAXBUserFieldParam(driverUserId, goodsId);
                    log.info("构造虚拟号额外参数，内容：{}", axbUserFieldParamJsonString);
                    AxbUpdateReq axbUpdateReq = new AxbUpdateReq();
                    axbUpdateReq.setId(needSaveNewExpirationDateAxbInfo.getId());
                    axbUpdateReq.setExpiration(expireTimeSec);
                    axbUpdateReq.setUserField(axbUserFieldParamJsonString);
                    axbCommonRemoteService.axbUpdate(axbUpdateReq);
                    AxbUpdateIds.append(axbUpdateReq.getId()).append(" ");
                }
                log.info("支付定金续期虚拟号 driverUserId：{} goodsId：{} 续期的虚拟号IDs：{}", driverUserId, goodsId, AxbUpdateIds);
            }
        } catch (Exception e) {
            log.error("货源成交延长车主与货源曾经绑定过的虚拟号过期时间失败 :driverUserId={}, goodsId={} 原因：", driverUserId, goodsId, e);
        }
    }

    private int makeTianRunAccountChoose(PrivacyPhoneNumGoodIdReq privacyPhoneNumGoodIdReq) {
        //抽佣货源
        boolean isCommissionTransport = false;
        TransportMainDO transportMain = transportMainService.getTransportMainForId(privacyPhoneNumGoodIdReq.getGoodId());
        if (StringUtils.isNotBlank(transportMain.getLabelJson())) {
            TransportLabelJson transportLabelJson = JSONObject.parseObject(transportMain.getLabelJson(), TransportLabelJson.class);
            if (transportLabelJson != null && transportLabelJson.getCommissionTransport() != null && transportLabelJson.getCommissionTransport() == 1) {
                TransportTecServiceFeeDO transportTecServiceFee = transportTecServiceFeeMapper.getBySrcMsgId(transportMain.getSrcMsgId());
                if (transportTecServiceFee != null) {
                    isCommissionTransport = (transportTecServiceFee.getMemberShowPrivacyPhoneTab() == null || transportTecServiceFee.getMemberShowPrivacyPhoneTab() == 1);
                } else {
                    isCommissionTransport = true;
                }
            }
        }

        if (isCommissionTransport) {
            log.info("绑定虚拟号 符合抽佣货源条件");
        }

        //首履用户
        boolean firstHonourAnAgreementboolean = ordersRemoteService.checkTransportUserIsFirst(transportMain.getUserId());
        if (firstHonourAnAgreementboolean) {
            log.info("绑定虚拟号弹窗 符合首履货主条件");
        }

        //首履货主或者抽佣货源
        if (firstHonourAnAgreementboolean || isCommissionTransport) {
            return 1;
        }

        //在货主ab测试中，使用账号2
        List<String> abTestCodeList = new ArrayList<>();
        abTestCodeList.add("privacy_phone_tab_show_transport_abtest");
        ABTestDto abTestDto = new ABTestDto();
        abTestDto.setCodeList(abTestCodeList);
        abTestDto.setUserId(transportMain.getUserId());
        List<ABTestVo> userTypeList = abTestRemoteService.getUserTypeList(abTestDto);
        if (CollectionUtils.isNotEmpty(userTypeList)) {
            if (userTypeList.get(0).getType() == 1) {
                log.info("绑定虚拟号 符合特殊货主AB测试条件");
                return 2;
            }
        }
        return 0;
    }

}
