package com.teyuntong.goods.service.service.biz.specialcar.service;


import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;

/**
 * <p>
 * 专车派单表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-20
 */
public interface SpecialCarDispatchService {
    /**
     * 专车发货自动派单
     *
     * @param main
     * @param user
     * @param distanceLimit
     */
    void doDispatch(TransportMainDO main, UserRpcVO user, Integer distanceLimit);
}
