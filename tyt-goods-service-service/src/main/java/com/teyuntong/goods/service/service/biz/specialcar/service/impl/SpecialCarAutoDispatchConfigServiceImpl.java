package com.teyuntong.goods.service.service.biz.specialcar.service.impl;

import com.teyuntong.goods.service.service.biz.specialcar.mybatis.entity.SpecialCarAutoDispatchConfigDO;
import com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.SpecialCarAutoDispatchConfigMapper;
import com.teyuntong.goods.service.service.biz.specialcar.service.SpecialCarAutoDispatchConfigService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 专车自动指派配置表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SpecialCarAutoDispatchConfigServiceImpl implements SpecialCarAutoDispatchConfigService {
    private final SpecialCarAutoDispatchConfigMapper specialCarAutoDispatchConfigMapper;

    @Override
    public SpecialCarAutoDispatchConfigDO selectDispatchConfigByRoute(String startCity, String destCity) {
        return specialCarAutoDispatchConfigMapper.selectDispatchConfigByRoute(startCity, destCity);
    }
}
