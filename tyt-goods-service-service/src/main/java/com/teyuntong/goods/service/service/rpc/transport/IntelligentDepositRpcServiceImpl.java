package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.dto.IntelligentDepositDTO;
import com.teyuntong.goods.service.client.transport.service.IntelligentDepositRpcService;
import com.teyuntong.goods.service.client.transport.vo.DepositAndRefundTypeVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.IntelligentDepositConfigDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.IntelligentDepositUserRecordDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.IntelligentDepositConfigService;
import com.teyuntong.goods.service.service.biz.transport.service.IntelligentDepositUserRecordService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.enums.DepositTypeEnum;
import com.teyuntong.goods.service.service.common.enums.InvoiceTransportEnum;
import com.teyuntong.goods.service.service.common.enums.RefundFlagEnum;
import com.teyuntong.goods.service.service.common.enums.UserGoodsTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserIdentityRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.infra.basic.resource.client.tytabtest.enums.AbTestRuleTypeEnum;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.AbTestConfigVO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.outer.export.service.client.common.old.bean.ResultMsgBean;
import com.teyuntong.outer.export.service.client.common.old.enums.ResponseEnum;
import com.teyuntong.user.service.client.user.enums.NewIdentityEnum;
import com.teyuntong.user.service.client.user.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 智能订金
 *
 * <AUTHOR>
 * @since 2025-05-22 17:43
 */
@RestController
@Slf4j
public class IntelligentDepositRpcServiceImpl implements IntelligentDepositRpcService {

    @Resource
    private ABTestRemoteService abTestRemoteService;
    @Resource
    private TytConfigRemoteService tytConfigRemoteService;
    @Resource
    private UserRemoteService userRemoteService;
    @Resource
    private UserIdentityRemoteService userIdentityRemoteService;

    @Resource
    private TransportMainService transportMainService;
    @Resource
    private IntelligentDepositConfigService intelligentDepositConfigService;
    @Resource
    private IntelligentDepositUserRecordService intelligentDepositUserRecordService;


    /**
     * 发货二页，订金默认填充金额
     */
    public static final String DEPOSIT_DEFAULT_VALUE_KEY = "deposit_default_value";
    public static final String DEPOSIT_DEFAULT_VALUE = "100";

    @Override
    public DepositAndRefundTypeVO intelligentDeposit(IntelligentDepositDTO dto) {
        if (Objects.isNull(dto.getUserId())) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        DepositTypeEnum depositType = DepositTypeEnum.NORMAL;
        if (!Objects.equals(dto.getInvoiceTransport(), InvoiceTransportEnum.YES.getCode())) {
            // 专票货源不走智能订金
            AbTestConfigVO depositTypeConfig = abTestRemoteService.getAbTestConfig("deposit_type_config");
            if (Objects.nonNull(depositTypeConfig)) {
                if (Objects.equals(depositTypeConfig.getRuleType(), AbTestRuleTypeEnum.USER.getCode())) {
                    // ab测试类型为用户，判断当前用户是否在ab测内
                    depositType = getDepositType(dto.getUserId(), depositTypeConfig);
                } else {
                    // ab测试类型为全部，直接取ab测配置
                    depositType = DepositTypeEnum.getDepositTypeByCode(depositTypeConfig.getDefaultType());
                }
            }
        }
        Integer refundFlag = RefundFlagEnum.RETURN.getCode();
        BigDecimal deposit = new BigDecimal("100");
        if (DepositTypeEnum.INTELLIGENT == depositType) {
            // 订金退还方式 0-关闭，订金退还方式取上一票；1-开启，订金退还
            Integer refundConfig = tytConfigRemoteService.getIntValue("intelligent_deposit_default_return", 0);
            if (Objects.nonNull(refundConfig) && refundConfig == 0) {
                // 用户被加入到智能订金模式时，第一次发货在智能订金的模式下都默认订金可退。 工单：SSRS-1180
                Long userRecordId = intelligentDepositUserRecordService.selectByUserId(dto.getUserId());
                if (Objects.nonNull(userRecordId)) {
                    TransportMainDO lastTransport = transportMainService.getLastTransport(dto.getUserId());
                    if (Objects.nonNull(lastTransport) && Objects.nonNull(lastTransport.getRefundFlag())) {
                        refundFlag = lastTransport.getRefundFlag();
                    }
                }
            }
            // 订金金额
            if (Objects.nonNull(dto.getDistance()) && Objects.nonNull(dto.getWeight())) {
                IntelligentDepositConfigDO depositConfig = intelligentDepositConfigService
                        .getConfigByWeightAndDistance(dto.getWeight(), dto.getDistance());
                if (Objects.nonNull(depositConfig) && Objects.nonNull(depositConfig.getDeposit())) {
                    deposit = new BigDecimal(depositConfig.getDeposit());
                }
            }
        } else {
            // 手动订金模式
            TransportMainDO lastTransport = transportMainService.getLastTransport(dto.getUserId());
            if (Objects.nonNull(lastTransport)) {
                // 用户发过货，默认填充上一票的订金和退还方式
                refundFlag = lastTransport.getRefundFlag();
                deposit = lastTransport.getInfoFee();
                if (Objects.isNull(deposit) || deposit.compareTo(new BigDecimal(0)) == 0) {
                    deposit = new BigDecimal(50);
                }
                if (Objects.equals(InvoiceTransportEnum.YES.getCode(), dto.getInvoiceTransport())) {
                    refundFlag = RefundFlagEnum.RETURN.getCode();
                }
            } else {
                String defaultDeposit = tytConfigRemoteService.getStringValue(DEPOSIT_DEFAULT_VALUE_KEY, DEPOSIT_DEFAULT_VALUE);
                deposit = new BigDecimal(defaultDeposit);
                if (Objects.equals(InvoiceTransportEnum.YES.getCode(), dto.getInvoiceTransport())) {
                    refundFlag = RefundFlagEnum.RETURN.getCode();
                } else {
                    RefundFlagEnum returnEnum = RefundFlagEnum.RETURN;
                    // 1、先获取经分同步表中的用户身份
                    RefundFlagEnum biRefundEnum = getRefundFlagEnumFromBiSyncTable(dto.getUserId());
                    // 2、经分身份未获取成功，获取用户注册身份
                    if (Objects.isNull(biRefundEnum)) {
                        returnEnum = getRefundFlagEnumFromUserRegister(dto.getUserId(), returnEnum);
                    } else {
                        returnEnum = biRefundEnum;
                    }
                    refundFlag = returnEnum.getCode();
                }
            }
            // 订金默认填充ab测，圈选用户不走默认填充逻辑
            Integer depositDefaultFill = abTestRemoteService.getUserType("deposit_default_fill", dto.getUserId());
            if (Objects.nonNull(depositDefaultFill) && depositDefaultFill == 1) {
                deposit = null;
            }
        }

        DepositAndRefundTypeVO vo = new DepositAndRefundTypeVO();
        vo.setDeposit(deposit);
        vo.setRefundFlag(refundFlag);
        vo.setDepositType(depositType.getCode());
        return vo;
    }

    /**
     * 从用户注册身份获取退款类型
     *
     * @param userId
     * @param returnEnum
     * @return
     */
    private RefundFlagEnum getRefundFlagEnumFromUserRegister(Long userId, RefundFlagEnum returnEnum) {
        UserIdentityLabelVO label = userIdentityRemoteService.getUserIdentityLabel(userId);
        if (Objects.nonNull(label)) {
            UserGoodsTypeEnum goodsTypeEnum = UserGoodsTypeEnum.getByGoodsType(label.getGoodsTypeFirst(), label.getGoodsTypeSecond());
            if (Objects.nonNull(goodsTypeEnum)) {
                switch (goodsTypeEnum) {
                    case SELF_CARGO_TERMINAL:
                    case ENTERPRISE_CARGO_TERMINAL:
                    case ENTERPRISE_LOGISTICS_COMPANY:
                        returnEnum = RefundFlagEnum.NO_RETURN;
                        break;
                    default:
                        break;
                }
            }
        }
        return returnEnum;
    }

    /**
     * 根据经分同步身份表，确认用户身份
     *
     * @param userId
     * @return
     */
    private RefundFlagEnum getRefundFlagEnumFromBiSyncTable(Long userId) {
        RefundFlagEnum returnEnum = null;
        DwsNewIdentiwoDataRpcVO newIdentity = userRemoteService.getDwsNewIdentiwoDataByUserId(userId);
        if (Objects.nonNull(newIdentity)) {
            NewIdentityEnum identityEnum = NewIdentityEnum.getByCode(newIdentity.getType());
            if (Objects.nonNull(identityEnum)) {
                switch (identityEnum) {
                    case CARGO_TERMINAL:
                    case LOGISTICS_COMPANY:
                        returnEnum = RefundFlagEnum.NO_RETURN;
                        break;
                    case SELF_CARGO_OWNER:
                    case ENTERPRISE_CARGO_OWNER:
                        returnEnum = RefundFlagEnum.RETURN;
                        break;
                    default:
                        break;
                }
            }
        }
        return returnEnum;
    }

    /**
     * 获取定金类型
     *
     * @param userId
     * @param depositTypeConfig
     * @return
     * @throws Exception
     */
    private DepositTypeEnum getDepositType(Long userId, AbTestConfigVO depositTypeConfig) {
        // ab测试类型为用户，判断当前用户是否在ab测内
        Integer userType = abTestRemoteService.getUserType(depositTypeConfig.getCode(), userId);
        if (Objects.nonNull(userType)) {
            return DepositTypeEnum.getDepositTypeByCode(userType);
        }
        // 当前用户不在ab测内，判断注册时间是否在配置的时间段内，并且用户类型是个人货主或企业货主
        String dateRange = tytConfigRemoteService.getStringValue("intelligent_deposit_user_match");
        if (StringUtils.isBlank(dateRange)) {
            return DepositTypeEnum.NORMAL;
        }
        String[] dateRangeArr = dateRange.split("-");
        if (dateRangeArr.length != 2) {
            return DepositTypeEnum.NORMAL;
        }
        String startDate = dateRangeArr[0];
        String endDate = dateRangeArr[1];
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return DepositTypeEnum.NORMAL;
        }
        UserRpcVO user = userRemoteService.getUser(userId);
        boolean dateInRange = TimeUtil.dateInRange(startDate, endDate, user.getCtime());
        if (!dateInRange) {
            // 注册时间不在配置的时间范围内
            return DepositTypeEnum.NORMAL;
        }
        // 判断用户注册类型是否是：个人货主，企业货主
        UserIdentityLabelVO label = userIdentityRemoteService.getUserIdentityLabel(userId);
        if (Objects.nonNull(label)) {
            UserGoodsTypeEnum goodsTypeEnum = UserGoodsTypeEnum.getByGoodsType(label.getGoodsTypeFirst(), label.getGoodsTypeSecond());
            if (UserGoodsTypeEnum.SELF_CARGO_OWNER == goodsTypeEnum || UserGoodsTypeEnum.ENTERPRISE_CARGO_OWNER == goodsTypeEnum) {
                return DepositTypeEnum.INTELLIGENT;
            }
        }
        return DepositTypeEnum.NORMAL;
    }

    /**
     * 用户智能订金提交记录
     *
     * @param dto
     */
    @Override
    public void depositRecord(IntelligentDepositDTO dto) {
        if(Objects.isNull(dto.getUserId()) || Objects.isNull(dto.getDefaultDeposit()) ||
                Objects.isNull(dto.getDefaultRefundFlag()) || Objects.isNull(dto.getUserDeposit())
                || Objects.isNull(dto.getUserRefundFlag())){
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        IntelligentDepositUserRecordDO userRecordDO = new IntelligentDepositUserRecordDO();
        BeanUtils.copyProperties(dto, userRecordDO);
        intelligentDepositUserRecordService.saveDepositRecord(userRecordDO);
    }
}
