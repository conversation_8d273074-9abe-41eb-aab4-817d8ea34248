package com.teyuntong.goods.service.service.biz.userempower.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 货源授权弹窗数据表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-05-15
 */
@Getter
@Setter
@TableName("tyt_userempower_syncgoods")
public class TytUserempowerSyncgoodsDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 货方姓名
     */
    private String goodsUserName;

    /**
     * 状态(0已授权；1 未授权)
     */
    private Integer status;

    /**
     * 修改人id
     */
    private Long updateUserId;

    /**
     * 操作人
     */
    private String updateUserName;

    /**
     * 授权时间
     */
    private Date syncTime;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 是否删除(0否；1 是)
     */
    private Integer isDelete;
}
