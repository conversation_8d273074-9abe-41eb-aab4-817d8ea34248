package com.teyuntong.goods.service.service.common.constant;

/**
 * <AUTHOR>
 * @since 2025/1/27 16:41
 */
public class ConfigKeyConstant {
    /**
     * 货源备注前面添加的固定文案
     */
    public static final String GOODS_DETAIL_REMARK_PROMPT = "goods_detail_remark_prompt";
    /**
     * 未实名认证用户发布货源最大数量
     */
    public static final String USER_MAX_PUBLISH_NUM = "user_max_publish_num";

    // 专车匹配零担运费规则吨位阈值
    public static final String SPECIAL_CAR_PRICE_CONFIG_TONNAGE = "special_car_price_config_tonnage";

    /**
     * 公司调度身份货主用户
     */
    public static final String COMPANY_GOODS_USERS = "company_goods_users";
    /**
     * 默认的开票货源主体
     */
    public static final String INVOICE_SUBJECT_DATA = "invoice_subject_data";

    /**
     * 限制个人相似货源（0关闭；1开启）
     */
    public static final String CHECK_PERSONAL_SIMILARITY = "tyt:plat:config:checkPersonalSimilarity";

    /**
     * 优车货源下发布时间
     */
    public static final String EXCELLENT_GOODS_PUBLISH_TIME = "tyt:transport:excellent_goods_publish_time:";

    /**
     * 货源报价次数
     */
    public static final String TRANSPORT_QUOTED_PRICE_TOTAL_TIMES_KEY = "transport_quoted_price_total_times";

    /**
     * 回价助手标签
     */
    public static final String PRICE_ASSISTANT_LABEL_KEY = "price_assistant_label";

    /**
     * 回价助手描述
     */
    public static final String PRICE_ASSISTANT_DESCRIPTION_KEY = "price_assistant_description";

    /**
     * 回价助手弹窗描述
     */
    public static final String PRICE_ASSISTANT_POPUP_DESCRIPTION_KEY = "price_assistant_popup_description";

    /**
     * 回价助手弹窗运费加价比例
     */
    public static final String PRICE_ASSISTANT_ADD_PRICE_RATE_KEY = "price_assistant_add_price_rate";


    /**
     * 加价间隔时间
     */
    public static final String TRANSPORT_ADD_PRICE_INTERVAL = "tyt:transport:addPrice:interval:";
    /**
     * 加价次数
     */
    public static final String TRANSPORT_ADD_PRICE_TIMES = "tyt:transport:addPrice:times:";

    /**
     * 货报价挽留弹窗开关
     */
    public static final String TRANSPORT_QUOTED_PRICE_LEAVE_TAB_SHOW_ON_OFF = "transport_quoted_price_leave_tab_show_on_off";

    /**
     * 默认技术服务费
     */
    public static final String DEFAULT_TEC_SERVICE_FEE = "default_tec_service_fee";


    /**
     * 优货置顶配置，逗号拼接的3个整数（开关状态，刷新间隔，刷新次数）
     */
    public static final String RESEND_EXCELLENT_KEY = "tyt:config:task:tsTopExcellentConfig";

    /**
     * 曝光卡使用限制永久封禁文案
     */
    public static final String EXPOSURE_CARD_USE_LIMIT_PERPETUAL = "republish_astrict_content_perpetual";
    /**
     * 曝光卡使用限制阶段封禁文案
     */
    public static final String EXPOSURE_CARD_USE_LIMIT_TEMPORARY = "republish_astrict_content_temporary";
    /**
     * 曝光卡领取链接
     */
    public static final String EXPOSURE_CARD_GIVEAWAY_LINK = "exposure_card_giveaway_link";

    /**
     * 抽佣金额计算抹零方式 0：对10向下取整；1:向下取整
     */
    public static final String TEC_SERVICE_FEE_ROUNDING_TYPE = "tec_service_fee_rounding_type";

    /**
     * 优车2.0剩余次数弹窗提示文案
     */
    public static final String GOOD_CAR_PRICE_LEFT_TIME_PROMPT = "good_car_price_left_time_prompt";

    /**
     * 优车2.0成交赠送电议次数
     */
    public static final String GOOD_CAR_PRICE_DEAL_GIVE_NUM = "good_car_price_deal_give_num";

    /**
     * 专车平台预计接单时间key
     */
    public static final String SPECIAL_CAR_DISPATCH_TIME_NORMAL = "special_car_dispatch_time_normal";

    /**
     * 专车非平台预计接单时间key
     */
    public static final String SPECIAL_CAR_DISPATCH_TIME_NOT_NORMAL = "special_car_dispatch_time_not_normal";

    //==================公共资源配置==================
    /**
     * 分段支付比例
     */
    public static final String SEGMENTED_PAYMENTS_PRICE = "segmented_payments_price";

    /**
     * 货方我的等级页面（新）链接公共参数
     */
    public static final String GOODS_MY_LEVEL_PAGE = "goods_my_level_page";

    /**
     * 优车2.0价格模型最大值系数
     */
    public static final String EXCELLENT_PRICE_THPRICE_MILEAGE = "excellent_price_thPrice_mileage";

    /**
     * 优车2.0价格模型最小值系数
     */
    public static final String EXCELLENT_PRICE_THPRICE_MIN = "excellent_price_thPrice_min";

    /**
     * 不可退订金价格限制系数
     */
    public static final String INFE_FEE_PRICE_LIMIT_RATE = "infe_fee_price_limit_rate";

    /**
     * 优车价格模型新老切换开关：1新版；0旧版
     */
    public static final String EXCELLENT_PRICE_MODEL_SWITCH = "excellent_price_model_switch";

    /**
     * 发货帮助提示气泡展示逻辑配置key，【单数,秒数】
     */
    public static final String HELP_BTN_POPUP_CONFIG = "help_btn_popup_config";

    /**
     * 不进价值货源的用户id，多个用逗号隔开
     */
    public static final String VALUABLE_TRANSPORT_FILTER_USER_CONFIG = "valuable_transport_filter_user_config";

    /**
     * YMM同步TYT货源货名及备注校验，1开启0关闭
     */
    public static final String YMM_SYNC_GOODS_NAME_CHECK = "ymm_sync_goods_name_check";

    /**
     * 加价push次数上限
     */
    public static final String MARKUP_PUSH_COUNT = "MARKUP_PUSH_COUNT";

    /**
     * 公网域名
     */
    public static final String TYT_PUBLIC_HOST = "tyt:host:public:plat";

    /**
     * 货源在线编辑次数限制
     */
    public static final String GOODS_ONLINE_EDIT_CONFIG_KEY = "goods_online_edit_times";

    /**
     * 新代调货源自动进入找货大厅时间 单位：分钟
     */
    public static final String DISPATCH_AUTO_ENTER_HALL_TIME_CONFIG_KEY = "dispatch_auto_enter_hall_time";


}
