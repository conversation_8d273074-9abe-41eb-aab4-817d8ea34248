package com.teyuntong.goods.service.service.rpc.publish.post;

import com.teyuntong.goods.service.service.biz.goodsname.enums.MachineTypeEnum;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.MachineTypeAuditDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.MachineTypeAuditService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 添加待审核货名
 *
 * <AUTHOR>
 * @since 2025/02/23 17:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AddMachineTypePostHandler {

    private final MachineTypeAuditService machineTypeAuditService;

    /**
     * 添加待审核货名，进入后台的待审核列表
     *
     * @param publishProcessBO
     */
    @Async("threadPoolExecutor")
    public void saveMachineType(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        TransportMainDO transportMain = publishProcessBO.getTransportMain();
        UserRpcVO user = publishProcessBO.getUser();
        if (publishBO.getMachineType() != null && publishBO.getMachineType() == MachineTypeEnum.AUDIT.code) {
            MachineTypeAuditDO machineTypeAudit = machineTypeAuditService.selectByShowName(transportMain.getTaskContent());
            if (machineTypeAudit == null) {
                machineTypeAudit = MachineTypeAuditDO.builder()
                        .showName(transportMain.getTaskContent())
                        .status(MachineTypeEnum.AUDIT.code)
                        .conformanceCounts(0)
                        .illegalCounts(0)
                        .createName(user.getUserName())
                        .createTime(new Date())
                        .modifyName(user.getUserName())
                        .modifyTime(new Date())
                        .build();
                machineTypeAuditService.save(machineTypeAudit);
            } else {
                machineTypeAudit.setStatus(MachineTypeEnum.AUDIT.code);
                machineTypeAudit.setModifyTime(new Date());
                machineTypeAudit.setModifyName(user.getUserName());
                machineTypeAuditService.updateById(machineTypeAudit);
            }
        }
    }

}
