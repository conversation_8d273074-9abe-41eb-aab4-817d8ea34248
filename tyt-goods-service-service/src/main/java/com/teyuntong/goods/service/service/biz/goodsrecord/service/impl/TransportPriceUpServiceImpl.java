package com.teyuntong.goods.service.service.biz.goodsrecord.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.entity.TransportPriceUpDO;
import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.mapper.TransportPriceUpMapper;
import com.teyuntong.goods.service.service.biz.goodsrecord.service.TransportPriceUpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 货源加价表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportPriceUpServiceImpl extends ServiceImpl<TransportPriceUpMapper, TransportPriceUpDO> implements TransportPriceUpService {

    /**
     * 返回最新一条货源的数据
     */
    @Override
    public TransportPriceUpDO getLastOne(Long srcMsgId) {
        LambdaQueryWrapper<TransportPriceUpDO> wrapper = new LambdaQueryWrapper<TransportPriceUpDO>()
                .eq(TransportPriceUpDO::getSrcMsgId, srcMsgId)
                .orderByDesc(TransportPriceUpDO::getId)
                .last("limit 1");
        return this.getOne(wrapper);
    }
}
