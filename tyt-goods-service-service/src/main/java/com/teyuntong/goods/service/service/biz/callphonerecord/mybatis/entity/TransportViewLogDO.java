package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货物详情浏览日志表，每人每货仅存储一条
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Getter
@Setter
@TableName("tyt_transport_view_log")
public class TransportViewLogDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 货物ID
     */
    private Long tsId;

    /**
     * 客户端版本号
     */
    private String clientVersion;

    /**
     * 客户端标识 1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
     */
    private Integer clientSign;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;
}
