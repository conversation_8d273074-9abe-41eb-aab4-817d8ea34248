package com.teyuntong.goods.service.service.rpc.publish.post;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.transport.dto.UserDispatchTransportDTO;
import com.teyuntong.goods.service.client.transport.enums.CancelAuthOperateTypeEnum;
import com.teyuntong.goods.service.client.transport.service.ThPriceRpcService;
import com.teyuntong.goods.service.service.biz.log.mybatis.entity.TransportOptLogDO;
import com.teyuntong.goods.service.service.biz.log.service.TransportOptLogService;
import com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportPublishLogDO;
import com.teyuntong.goods.service.service.biz.publish.service.TransportPublishLogService;
import com.teyuntong.goods.service.service.biz.refresh.mybatis.entity.GoodsRefreshManualLogDO;
import com.teyuntong.goods.service.service.biz.refresh.service.GoodsRefreshManualLogService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.PublishTransportDataSnapDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.PublishTransportDataSnapService;
import com.teyuntong.goods.service.service.common.enums.DispatchTypeEnum;
import com.teyuntong.goods.service.service.rpc.publish.bo.*;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 货源发布记录
 *
 * <AUTHOR>
 * @since 2025/03/24 14:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportPublishLogPostHandler {

    private final TransportPublishLogService transportPublishLogService;
    private final ThPriceRpcService thPriceRpcService;
    private final PublishTransportDataSnapService publishTransportDataSnapService;
    private final GoodsRefreshManualLogService goodsRefreshManualLogService;
    private final TransportOptLogService transportOptLogService;

    @Async("threadPoolExecutor")
    public void handler(BasePublishProcessBO processBO) {
        TransportMainDO transportMain = processBO.getTransportMain();
        TransportMainDO oldMain = processBO.getOldMain();
        PublishOptEnum optEnum = processBO.getOptEnum();

        TransportPublishLogDO publishLog = new TransportPublishLogDO();
        publishLog.setUserId(transportMain.getUserId());
        publishLog.setSrcMsgId(transportMain.getSrcMsgId());
        publishLog.setPubType(PubTypeEnum.getByCode(optEnum));
        publishLog.setPubTime(new Date());
        publishLog.setRequestSource(null);
        transportPublishLogService.save(publishLog);

        if (optEnum == PublishOptEnum.PUBLISH || optEnum == PublishOptEnum.EDIT || optEnum == PublishOptEnum.ONLINE_EDIT || optEnum == PublishOptEnum.DISPATCH_TAKE_PUBLISH
                 || optEnum == PublishOptEnum.DISPATCH_ONLINE_EDIT) {
            PublishTransportDataSnapDO publishTransportDataSnapDO = new PublishTransportDataSnapDO();
            makePublishTransportDataSnapDO(transportMain, publishTransportDataSnapDO);
            publishTransportDataSnapService.save(publishTransportDataSnapDO);
        }

//        // 记录价格变更日志
//        ChangePriceLogReq changePriceLogReq = new ChangePriceLogReq();
//        changePriceLogReq.setSrcMsgId(transportMain.getSrcMsgId());
//        changePriceLogReq.setPrice(TransportUtil.hasPrice(transportMain.getPrice()) ?
//                new BigDecimal(transportMain.getPrice()) : BigDecimal.ZERO);
//        changePriceLogReq.setPublishType(transportMain.getPublishType());
//        changePriceLogReq.setOperationType(OperationTypeEnum.getByCode(optEnum));
//        thPriceRpcService.changePirceLog(changePriceLogReq);

        // 记录货源操作日志
        saveTransportOptLog(processBO);

        if (optEnum == PublishOptEnum.ONLINE_EDIT){
            return;
        }
        // 记录操作日志
        GoodsRefreshManualLogDO logDO = new GoodsRefreshManualLogDO();
        logDO.setSrcMsgId(transportMain.getSrcMsgId());
        logDO.setUserId(transportMain.getUserId());
        logDO.setItemValue(0);
        logDO.setPriceBefore(oldMain == null ? "" : oldMain.getPrice());
        logDO.setPriceAfter(transportMain.getPrice());
        logDO.setPublishTypeBefore(oldMain == null ? null : oldMain.getPublishType());
        logDO.setPublishTypeAfter(transportMain.getPublishType());
        goodsRefreshManualLogService.save(logDO);
    }

    /**
     * 记录本次货源操作日志
     */
    private void saveTransportOptLog(BasePublishProcessBO processBO) {
        TransportOptLogDO optLog = new TransportOptLogDO();
        optLog.setOpt(processBO.getOptEnum().name());
        UserRpcVO user = processBO.getUser();
        String optUerName = StringUtils.defaultIfBlank(StringUtils.defaultIfBlank(user.getTrueName(), user.getUserName()), "用户" + user.getId());
        optLog.setOptUserName(optUerName);
        optLog.setCreateTime(new Date());
        optLog.setOptUserType(1);

        // 新货源信息
        TransportMainDO main = processBO.getTransportMain();
        optLog.setSrcMsgId(main.getSrcMsgId());
        optLog.setTsId(processBO.getTransport().getId());
        optLog.setStatus(main.getStatus());
        JSONObject mainMap = JSON.parseObject(JSON.toJSONString(main));
        JSONObject mainExtendMap = JSON.parseObject(JSON.toJSONString(processBO.getMainExtend()));
        mainExtendMap.forEach(mainMap::putIfAbsent);
        optLog.setGoodsInfo(JSON.toJSONString(mainMap));

        // 旧货源信息
        TransportMainDO oldMain = processBO.getOldMain();
        if (oldMain != null) {
            optLog.setOldSrcMsgId(oldMain.getSrcMsgId());
            optLog.setOldStatus(oldMain.getStatus());
            JSONObject oldMainMap = JSON.parseObject(JSON.toJSONString(oldMain));
            JSONObject oldMainExtendMap = JSON.parseObject(JSON.toJSONString(processBO.getOldMainExtend()));
            oldMainExtendMap.forEach(oldMainMap::putIfAbsent);
            optLog.setOldGoodsInfo(JSON.toJSONString(oldMainMap));
        }

        // 新代调操作记录
        if (processBO instanceof DirectPublishProcessBO directPublishProcessBO) {
            UserDispatchTransportDTO dispatchTransportDTO = directPublishProcessBO.getDirectPublishBO().getUserDispatchTransportDTO();
            if (dispatchTransportDTO != null) {
                optLog.setRemark(dispatchTransportDTO.getCancelReason());
                // 代调操作类型设置
                if (StringUtils.isNotBlank(dispatchTransportDTO.getOperateType())) {
                    optLog.setOpt(dispatchTransportDTO.getOperateType());
                    optLog.setOptUserType(CancelAuthOperateTypeEnum.getOperateSource(dispatchTransportDTO.getOperateType()));
                }
                optLog.setOptUserName(dispatchTransportDTO.getOperatorName());
            }
        } else if (processBO instanceof PublishProcessBO publishProcessBO) {
            PublishBO publishBO = publishProcessBO.getPublishBO();
            if (Objects.equals(1, publishBO.getDispatchTransport()) && StringUtils.isNotBlank(publishBO.getOperator())) {
                if (DispatchTypeEnum.DISPATCH_TAKE_PUBLISH.getCode().equals(publishBO.getDispatchType())) {
                    optLog.setOpt(PublishOptEnum.DISPATCH_TAKE_PUBLISH.name());
                } else {
                    optLog.setOpt(PublishOptEnum.DISPATCH_ONLINE_EDIT.name());
                }
                optLog.setOptUserName(publishBO.getOperator());
                optLog.setOptUserType(2); // 代调
            }
        }
        transportOptLogService.save(optLog);
    }

    private void makePublishTransportDataSnapDO(TransportMainDO transportMain, PublishTransportDataSnapDO publishTransportDataSnapDO) {
        publishTransportDataSnapDO.setSrcMsgId(transportMain.getSrcMsgId());
        publishTransportDataSnapDO.setUserId(transportMain.getUserId());
        publishTransportDataSnapDO.setStartProvinc(transportMain.getStartProvinc());
        publishTransportDataSnapDO.setStartCity(transportMain.getStartCity());
        publishTransportDataSnapDO.setStartArea(transportMain.getStartArea());
        publishTransportDataSnapDO.setStartDetailAdd(transportMain.getStartDetailAdd());
        publishTransportDataSnapDO.setStartLongitude(transportMain.getStartLongitudeValue() == null ? null : transportMain.getStartLongitudeValue());
        publishTransportDataSnapDO.setStartLatitude(transportMain.getStartLatitudeValue() == null ? null : transportMain.getStartLatitudeValue());
        publishTransportDataSnapDO.setStartCoordX(transportMain.getStartCoordXValue() == null ? null : transportMain.getStartCoordXValue());
        publishTransportDataSnapDO.setStartCoordY(transportMain.getStartCoordYValue() == null ? null : transportMain.getStartCoordYValue());
        publishTransportDataSnapDO.setStartPoint(transportMain.getStartPoint());
        publishTransportDataSnapDO.setDestProvinc(transportMain.getDestProvinc());
        publishTransportDataSnapDO.setDestCity(transportMain.getDestCity());
        publishTransportDataSnapDO.setDestArea(transportMain.getDestArea());
        publishTransportDataSnapDO.setDestDetailAdd(transportMain.getDestDetailAdd());
        publishTransportDataSnapDO.setDestLongitude(transportMain.getDestLongitudeValue() == null ? null : transportMain.getDestLongitudeValue());
        publishTransportDataSnapDO.setDestLatitude(transportMain.getDestLatitudeValue() == null ? null : transportMain.getDestLatitudeValue());
        publishTransportDataSnapDO.setDestCoordX(transportMain.getDestCoordXValue() == null ? null : transportMain.getDestCoordXValue());
        publishTransportDataSnapDO.setDestCoordY(transportMain.getDestCoordYValue() == null ? null : transportMain.getDestCoordYValue());
        publishTransportDataSnapDO.setDestPoint(transportMain.getDestPoint());
        publishTransportDataSnapDO.setTaskContent(transportMain.getTaskContent());
        publishTransportDataSnapDO.setMatchItemId(transportMain.getMatchItemId() == null ? null : String.valueOf(transportMain.getMatchItemId()));
        publishTransportDataSnapDO.setType(transportMain.getType());
        publishTransportDataSnapDO.setBrand(transportMain.getBrand());
        publishTransportDataSnapDO.setGoodTypeName(transportMain.getGoodTypeName());
        publishTransportDataSnapDO.setWeight(transportMain.getWeight());
        publishTransportDataSnapDO.setLength(transportMain.getLength());
        publishTransportDataSnapDO.setWide(transportMain.getWide());
        publishTransportDataSnapDO.setHigh(transportMain.getHigh());
        publishTransportDataSnapDO.setCreateTime(transportMain.getCtime());
        publishTransportDataSnapDO.setUpdateTime(transportMain.getCtime());
    }
}

@Getter
@AllArgsConstructor
enum PubTypeEnum {
    CONFIRM_PUBLISH(1, "确认发布"),
    DIRECT_PUBLISH(2, "直接发布"),
    ADD_PRICE(3, "加价"),
    TRANSFER_FIX_PRICE(4, "转一口价"),
    FILL_PRICE(5, "填价"),
    REPUBLISH(6, "曝光"),
    UPDATE_INFO(7, "更新货源信息"),
    ;
    private final Integer code;
    private final String name;

    public static Integer getByCode(PublishOptEnum optEnum) {
        if (optEnum == PublishOptEnum.DIRECT) {
            return DIRECT_PUBLISH.code;
        } else if (optEnum == PublishOptEnum.FILL_PRICE) {
            return FILL_PRICE.code;
        } else if (optEnum == PublishOptEnum.ADD_PRICE) {
            return ADD_PRICE.code;
        } else if (optEnum == PublishOptEnum.TRANSFER_FIXED || optEnum == PublishOptEnum.TRANSFER_TELE) {
            return TRANSFER_FIX_PRICE.code;
        } else if (optEnum == PublishOptEnum.REPUBLISH) {
            return REPUBLISH.code;
        } else if (optEnum == PublishOptEnum.UPDATE_INFO) {
            return UPDATE_INFO.code;
        } else if (optEnum == PublishOptEnum.PUBLISH || optEnum == PublishOptEnum.EDIT || optEnum == PublishOptEnum.ONLINE_EDIT
                || optEnum == PublishOptEnum.DISPATCH_TAKE_PUBLISH || optEnum == PublishOptEnum.DISPATCH_ONLINE_EDIT) {
            return CONFIRM_PUBLISH.code;
        } else {
            return 0;
        }
    }


}

@Getter
@AllArgsConstructor
enum OperationTypeEnum {
    // 操作入口 1:编辑发布；2:直接发布；3:填价、加价；4:转一口价、转电议；5:拒绝报价修改运费；6:货源诊断
    CONFIRM_PUBLISH(1, "编辑发布"),
    DIRECT_PUBLISH(2, "直接发布"),
    ADD_PRICE(3, "填价、加价"),
    TRANSFER_FIX_PRICE(4, "转一口价、转电议"),
    REFUSE_QUOTE(5, "拒绝报价修改运费"),
    DIAGNOSIS(6, "货源诊断"),
    ;
    private final Integer code;
    private final String name;

    public static Integer getByCode(PublishOptEnum optEnum) {
        if (optEnum == PublishOptEnum.DIRECT) {
            return DIRECT_PUBLISH.code;
        } else if (optEnum == PublishOptEnum.FILL_PRICE) {
            return ADD_PRICE.code;
        } else if (optEnum == PublishOptEnum.ADD_PRICE) {
            return ADD_PRICE.code;
        } else if (optEnum == PublishOptEnum.TRANSFER_FIXED || optEnum == PublishOptEnum.TRANSFER_TELE) {
            return TRANSFER_FIX_PRICE.code;
        } else if (optEnum == PublishOptEnum.PUBLISH || optEnum == PublishOptEnum.EDIT) {
            return CONFIRM_PUBLISH.code;
        } else {
            return 0;
        }
    }
}