package com.teyuntong.goods.service.service.common.constant;

/**
 * <AUTHOR>
 * @since 2024/5/9 17:26
 */
public class RedisKeyConstant {

    // 货名白名单缓存key
    public static final String WHITELIST_CACHE_KEY = "tyt:goods_service:machine:whitelist";

    // 无效关键字缓存key
    public static final String NULLIFY_KEYWORD_CACHE_KEY = "tyt:goods_service:nullify:keywordList";

    /**
     * 缓存车辆当前位置信息
     */
    public static final String CACHE_EXPIRE_LOCATION_APPLY = "expire:location:apply:";

    /**
     * 货源刷新次数缓存前缀
     */
    public static final String CANCEL_GIVE_REFRESH_CACHE_PREFIX = "tyt:cancel:give:refresh:count:";

    /**
     * 货源详情价格弹窗前缀
     */
    public static final String DETAIL_POPUP_PRICE = "tyt:goods:service:detail:popUpPrice:";

    /**
     * 货源详情一口价优车2.0二次查看货源详情挽留弹窗前缀
     */
    public static final String TRANSPORT_QUOTED_PRICE_DETAINMENT_HASH_KEY = "goods:service:transportQuotedPriceCountDetainment:";

    /**
     * 运费加价次数
     */
    public static final String FREIGHT_ADD_MONEY_NUM = "freight_add_money_num";

    // 非标提示词缓存key
    public static final String NON_STANDARD_PROMPT_CACHE_KEY = "tyt:goods_service:non-standard:prompt";

    /**
     * 货源报价气泡
     */
    public static final String TRANSPORT_QUOTED_PRICE_BUBBLE_CLICK = "transportQuotedPriceBubble";

    /**
     * 货主货源报价次数
     */
    public static final String TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY = "transportQuotedPriceCount";

    /**
     * 车主出价推送消息key
     */
    public static final String TRANSPORT_CAR_QUOTED_PRICE_MESSAGE_KEY = "transportCarQuotedPriceMessage";

    /**
     * 车主出价给货主推送消息key
     */
    public static final String TRANSPORT_CAR_QUOTED_PRICE_TRANSPORT_MESSAGE_KEY = "transportCarQuotedPriceTransportMessage";

    public static final String TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY = "transportQuotedPriceCarLeave";

    /**
     * 货主同意报价key
     */
    public static final String TRANSPORT_QUOTED_PRICE_LOCK = "transportQuotedPriceRedisKey";

    /**
     * 回价助手红点key
     */
    public static final String PRICE_ASSISTANT_RED_POINT_KEY = "price:assistant:red:point:";

    /**
     * 货主出价车未查看
     */
    public static final String TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY = "transportQuotedPriceCarNoLook";

    /**
     * 货源详情加价自动弹窗
     */
    public static final String GOODS_DETAIL_AUTO_ADD_PRICE_POPUP_KEY = "tyt:goods_service:auto:add:price:popup:";

    /**
     * 货报价挽留弹窗key
     */
    public static final String TRANSPORT_QUOTED_PRICE_TRANSPORT_LEAVE_KEY = "transportQuotedPriceTransportLeave";

    /**
     * 货源详情线上成交送曝光卡
     */
    public static final String GOODS_DETAIL_EXPOSURE_TOAST_KEY = "tyt:goods_service:detail:exposure:toast:";

    /**
     * 第一个货源诊断任务完成时间戳
     */
    public static final String DIAGNOSIS_TASK_COMPLETE_TIME_KEY = "tyt:goods:diagnosis:task:complete:timestamp:";

    /**
     * 用户当月发布次数
     */
    public static final String MONTH_PUBLISH_NUM = "tyt:goods_service:month:user:publish:num:";

    /**
     * 相似货源code是否变更过
     */
    public static final String GOODS_SIMILARITY_CHANGE = "tyt:goods:similarity:change:";

    /**
     * 记录曝光卡使用时效，
     */
    public static final String EXPOSURE_CARD_VALID = "tyt:goods:exposure:valid:";

    /**
     * 货源授权弹窗当天已展示记录
     */
    public static final String SYNCGOODS_AUTH_POPUP_SHOWED = "tyt:goods:syncgoods:auth:popup:showed:";
    /**
     * 撤销订单时发送推荐货源
     */
    public static final String ORDER_CANCEL_RECOMMEND = "tyt:goods:order:cancel:recommend:";

    /**
     * 某个货源的曝光卡使用次数
     */
    public static final String EXPOSURE_CARD_USE_NUM_CACHE = "goods:service:exposure_card_use_num_cache:";

    /**
     * 加价三十分钟
     */
    public static final String ADD_PRICE_30_MINUTE = "goods:service:last_add_price_30_minute:";

    /**
     * 货源加价push次数，key+srcMsgId+userId
     */
    public static final String GOODS_ADD_PRICE_PUSH = "goods:add:price:push:";

    /**
     * 用户当天是否展示提示气泡缓存key前缀
     */
    public static final String CACHE_HELP_BTN_POPUP_KEY_PREFIX = "help_btn_popup_user_id_";

    /**
     * 货源原始价格，key+srcMsgId，无价默认为0
     */
    public static final String GOODS_PRICE_ORIGINAL = "tyt:goods:price:original:";

    /**
     * 货源首次有价，key+srcMsgId，无价默认为0
     */
    public static final String GOODS_PRICE_EARLIEST = "tyt:goods:price:earliest:";
}
