package com.teyuntong.goods.service.service.biz.specialcar.service.impl;

import com.teyuntong.goods.service.service.biz.specialcar.mybatis.mapper.TransportDispatchCarTypeMapper;
import com.teyuntong.goods.service.service.biz.specialcar.service.TransportDispatchCarTypeService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 自动派单车型匹配因素表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportDispatchCarTypeServiceImpl implements TransportDispatchCarTypeService {
    private final TransportDispatchCarTypeMapper transportDispatchCarTypeMapper;

    @Override
    public List<String> selectCarTypeListByWeight(BigDecimal weightDecimal) {
        return transportDispatchCarTypeMapper.selectCarTypeListByWeight(weightDecimal);
    }
}
