package com.teyuntong.goods.service.service.rpc.publish;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.service.client.navigation.dto.NavigationQueryDTO;
import com.teyuntong.goods.service.client.navigation.service.NavigationRpcService;
import com.teyuntong.goods.service.client.navigation.vo.NavigationResultVO;
import com.teyuntong.goods.service.client.publish.service.TransportOnlineEditRpcService;
import com.teyuntong.goods.service.client.transport.dto.PublishDTO;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;
import com.teyuntong.goods.service.client.transport.vo.TransportPublishVO;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.commission.bo.TecServiceFeeConfigComputeResult;
import com.teyuntong.goods.service.service.biz.publish.service.TransportPublishService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.*;
import com.teyuntong.goods.service.service.biz.transport.service.*;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.TytCityRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.builder.CommissionTecFeeBuilder;
import com.teyuntong.goods.service.service.rpc.publish.builder.SpecialGoodsBuilder;
import com.teyuntong.goods.service.service.rpc.publish.builder.TransportLabelJsonBuilder;
import com.teyuntong.goods.service.service.rpc.publish.checker.*;
import com.teyuntong.goods.service.service.rpc.publish.commission.CalcCommissionTecFeeService;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.goods.service.service.rpc.publish.post.*;
import com.teyuntong.infra.basic.resource.client.tytcity.vo.TytCityVo;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.user.service.client.user.vo.ApiDataUserCreditInfoRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import com.teyuntong.user.service.client.user.vo.UserSubRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 在线编辑货源服务实现
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class TransportOnlineEditRpcServiceImpl implements TransportOnlineEditRpcService {

    private final UserRemoteService userRemoteService;
    private final TransportMainService transportMainService;
    private final TransportMainExtendService transportMainExtendService;
    private final NavigationRpcService navigationRpcService;
    private final ThPriceService thPriceService;
    private final SpecialGoodsBuilder specialGoodsBuilder;
    private final DispatchCompanyService dispatchCompanyService;
    private final DispatchCooperativeService dispatchCooperativeService;
    private final UserPublishLimitChecker userPublishLimitChecker;
    private final FixedTransportChecker fixedTransportChecker;
    private final SensitiveWordsChecker sensitiveWordsChecker;
    private final SpecialTransportChecker specialTransportChecker;
    private final PublishPhoneChecker publishPhoneChecker;
    private final PublishPermissionChecker publishPermissionChecker;
    private final AllowPriceChecker allowPriceChecker;
    private final InvoiceTransportChecker invoiceTransportChecker;
    private final CarpoolTransportChecker carpoolTransportChecker;
    private final ActivityChecker activityChecker;
    private final YmmTransportChecker ymmTransportChecker;
    private final ExcellentTransportChecker excellentTransportChecker;
    private final TransportPublishService transportPublishService;
    private final TransportLabelJsonBuilder transportLabelJsonBuilder;
    private final CommissionTecFeeBuilder commissionTecFeeBuilder;
    private final CalcCommissionTecFeeService calcCommissionTecFeeService;
    private final TransportBaseChecker transportBaseChecker;
    private final PersonalDuplicateChecker personalDuplicateChecker;
    private final CommissionTransportPostHandler commissionTransportPostHandler;
    private final AddDispatchOwnerPostHandler addDispatchOwnerPostHandler;
    private final InvoiceTransportSaveLogPostHandler invoiceTransportSaveLogPostHandler;
    private final InvoiceTransportAssignPostHandler invoiceTransportAssignPostHandler;
    private final AddTransportHistoryHandler addTransportHistoryHandler;
    private final AddMachineTypePostHandler addMachineTypePostHandler;
    private final AutoResendTransportPostHandler autoResendTransportPostHandler;
    private final BiTrickingPostHandler biTrickingPostHandler;
    private final TransportPublishLogPostHandler transportPublishLogPostHandler;
    private final SpecialtAutoAssignPostHandler specialtAutoAssignPostHandler;
    private final SendPublishMQPostHandler sendPublishMQPostHandler;
    private final SameTransportPushPostHandler sameTransportPushPostHandler;
    private final PriceChangeCachePostHandler priceChangeCachePostHandler;
    private final SeckillGoodsTransportService seckillGoodsTransportService;
    private final TransportOrdersChecker transportOrdersChecker;
    private final TransportSyncYmmService transportSyncYmmService;
    private final TytCityRemoteService tytCityRemoteService;
    private final AssignCarPostHandler assignCarPostHandler;
    private final DispatchTransportPoster dispatchTransportPoster;
    private final AssignCarChecker assignCarChecker;




    @Override
    public TransportPublishVO transportOnlineEdit(PublishDTO publishDTO) {
        PublishBO publishBO = new PublishBO();
        BeanUtils.copyProperties(publishDTO, publishBO);

        PublishProcessBO publishProcessBO = new PublishProcessBO();
        publishProcessBO.setPublishBO(publishBO);
        // 获取当前登录用户信息
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        UserRpcVO user = userRemoteService.getUser(loginUser.getUserId());
        publishProcessBO.setUser(user);
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        publishProcessBO.setBaseParam(baseParam);
        TransportMainDO oldMain = getOldTransportMain(publishProcessBO);
        TransportMainExtendDO oldMainExtend = null;
        if (oldMain != null){
            oldMainExtend = transportMainExtendService.getBySrcMsgId(oldMain.getSrcMsgId());
        }
        publishProcessBO.setOldMain(oldMain);
        publishProcessBO.setOldMainExtend(oldMainExtend);

        //参数处理
        buildConvert(publishProcessBO, user);

        // 拦截校验及添加部分货源参数
        checkAssert(publishProcessBO);

        // 运满满货源下架
        transportSyncYmmService.noticeYmmBackoutTransport(publishBO.getSrcMsgId());

        // 组装发货实体
        TransportMainDO transportMain = createTransportMain(publishBO, user, oldMain, baseParam);
        publishProcessBO.setTransportMain(transportMain);
        TransportMainExtendDO mainExtend = transportPublishService.createTransportMainExtend(publishProcessBO);
        publishProcessBO.setMainExtend(mainExtend);

        // 组装完发货实体后的后置处理
        dealOfCreated(publishProcessBO);
        log.info("组装完发货实体后的后置处理，publishProcessBO：{}", JSONUtil.toJsonStr(publishProcessBO));
        // 处理完成后再同步一下数据
        TransportDO transport = new TransportDO();
        TransportExtendDO transportExtend = new TransportExtendDO();
        BeanUtils.copyProperties(transportMain, transport);
        BeanUtils.copyProperties(mainExtend, transportExtend);
        publishProcessBO.setTransport(transport);
        publishProcessBO.setTransportExtend(transportExtend);

        transportPublishService.onlineEditGoods(publishProcessBO);

        //发货完成后的后置处理
        afterPublish(publishProcessBO);
        // 组装返回值
        return buildReturnVO(publishProcessBO);
    }

    /**
     * 获取历史货源信息
     *
     * @param processBO
     * @return
     */
    private TransportMainDO getOldTransportMain(PublishProcessBO processBO) {
        PublishBO publishBO = processBO.getPublishBO();
        Long oldSrcMsgId = publishBO.getSrcMsgId();
        if (Objects.equals(YesOrNoEnum.YES.getId(), publishBO.getDispatchTransport())){
            if (publishBO.getDispatchType().equals(DispatchTypeEnum.DISPATCH_TAKE_PUBLISH.getCode())){
                processBO.setOptEnum(PublishOptEnum.DISPATCH_TAKE_PUBLISH);
            }else {
                processBO.setOptEnum(PublishOptEnum.DISPATCH_ONLINE_EDIT);
            }
        }else {
            processBO.setOptEnum(PublishOptEnum.ONLINE_EDIT);
        }
        if(oldSrcMsgId == null){
            throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
        }

        return transportMainService.getTransportMainForId(oldSrcMsgId);
    }

    private void buildConvert(PublishProcessBO publishProcessBO, UserRpcVO user) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        BaseParamDTO baseParam = publishProcessBO.getBaseParam();

        // 如果是开票货源，要将订金修改为退还状态（开票货源订金都为退还）
        if (Objects.equals(publishBO.getInvoiceTransport(), YesOrNoEnum.YES.getId())) {
            // 如果是专票，则将订金改为退还状态
            publishBO.setRefundFlag(YesOrNoEnum.YES.getId());
            // 开票货源如果指派车方的话定金强制修改为0并且不在找货大厅显示
            if (StringUtils.isNotBlank(publishBO.getAssignCarTel())) {
                publishBO.setInfoFee(BigDecimal.ZERO);
                publishBO.setIsShow(YesOrNoEnum.NO.getId());
            }
            // 如果是分段支付，要计算总价
            if (Objects.equals(publishBO.getPaymentsType(), YesOrNoEnum.YES.getId())) {
                BigDecimal prepaidPrice = publishBO.getPrepaidPrice() == null ? BigDecimal.ZERO : publishBO.getPrepaidPrice();
                BigDecimal collectedPrice = publishBO.getCollectedPrice() == null ? BigDecimal.ZERO : publishBO.getCollectedPrice();
                BigDecimal receiptPrice = publishBO.getReceiptPrice() == null ? BigDecimal.ZERO : publishBO.getReceiptPrice();
                BigDecimal allPrice = collectedPrice.add(prepaidPrice).add(receiptPrice);
                publishBO.setPrice(allPrice.toPlainString());

            }
        }

        // 如果没有距离，调用地图补充距离
        buildDistance(publishProcessBO);

        // 发货类型补充
        setExcellentGoodsType(publishBO, user, baseParam);

        // 校验装卸货时间
        convertLoadingTime(publishBO);

        // 确定来源
        confirmSourceType(publishBO, user);

        // 标准货源
        if (publishBO.getMatchItemId() != null && publishBO.getMatchItemId() > 0 && publishBO.getMatchItemId() != 9999999) {
            publishBO.setIsStandard(YesOrNoEnum.NO.getId());
        } else {
            // 非标准货源
            publishBO.setIsStandard(YesOrNoEnum.YES.getId());
            if (publishBO.getMatchItemId() == null) {
                publishBO.setMatchItemId(-1);
            }
        }

        // 如是专车货源,计算专车运费及运费类型
        specialGoodsBuilder.build(publishProcessBO, user);
    }


    private void checkAssert(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        UserRpcVO user = publishProcessBO.getUser();
        TransportMainDO oldTransportMain = publishProcessBO.getOldMain();
        // 校验订单
        transportOrdersChecker.check(publishBO,oldTransportMain);
        //校验货源状态
        transportBaseChecker.checkGoodsStatusForOnlineEdit(oldTransportMain, publishBO.getDispatchTransport(), publishBO.getDispatchType());
        if (Objects.equals(YesOrNoEnum.NO.getId(), publishBO.getDispatchTransport())){
            //在线编辑次数校验
            transportBaseChecker.checkOnlineEditTimes(oldTransportMain.getSrcMsgId());
            //验证电话是否为本人联系电话，人工派单不进行验证；
            publishPhoneChecker.checkPhone(publishBO, user);
        }
        // 发货限制校验
        userPublishLimitChecker.check(user);
        // 校验秒抢货源
        checkSeckillGoodsIsLock(oldTransportMain.getSrcMsgId());
        // 一口价货源限制校验
        fixedTransportChecker.checkFixedTransport(publishBO);
        // 校验货物装卸货时间、订金
        transportBaseChecker.checkBaseParams(publishBO);
        // 校验货物内容(备注)是否处罚敏感词
        sensitiveWordsChecker.checkSensitiveWords(publishBO, user);
        // 运满满货源校验
        ymmTransportChecker.checkPublish(publishBO, user);
        // 专车发货的校验（合作商、专车运价）
        specialTransportChecker.specialCheck(publishProcessBO, user);
        // 校验用户发货权益
        publishPermissionChecker.checkPublishPermission(publishProcessBO, false);
        // 校验优车权益限制
        excellentTransportChecker.checkExcellentGoodsBlock(publishBO, user);
        // 校验运费
        allowPriceChecker.checkPublishPrice(publishBO, user.getId());
        // 校验开票货源
        invoiceTransportChecker.checkInvoiceTransport(publishBO, user);
        // 拼车校验
        carpoolTransportChecker.check(publishBO, user);
        // 指派司机校验
        assignCarChecker.check(publishBO);
        // 6510拉新裂变活动
        activityChecker.checkActivity(user);
    }

    private void dealOfCreated(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        // 校验货源是否是重货
        personalDuplicateChecker.check(publishProcessBO, publishBO.getAssignCarTel());

        // 组装货源标签+
        transportLabelJsonBuilder.build(publishProcessBO);

        // 计算货源的抽佣信息
        commissionTecFeeBuilder.build(publishProcessBO);

    }

    private void afterPublish(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        TransportMainDO transportMain = publishProcessBO.getTransportMain();
        UserRpcVO user = publishProcessBO.getUser();
        // 记录抽佣数据
        commissionTransportPostHandler.handler(publishProcessBO, true);
        // 添加到个人货主
        addDispatchOwnerPostHandler.handler(publishProcessBO);
        //开票货源记录发布时货主的企业信息
        invoiceTransportSaveLogPostHandler.saveForPublish(transportMain, publishBO);
        // 如果是指派货源，调用履约的派单接口
        invoiceTransportAssignPostHandler.invoiceAssignCar(publishProcessBO.getTransportMain(), publishProcessBO.getPublishBO());
        // 扣减发货次数
        publishPermissionChecker.checkPublishPermission(publishProcessBO, true);
        // 保存到历史表，货源发布新老映射表
        addTransportHistoryHandler.handler(publishProcessBO);
        // 如果是待审核货名，进入  后台的待审核列表
        addMachineTypePostHandler.saveMachineType(publishProcessBO);
        // 如果勾选自动重发，添加到自动重发记录
        autoResendTransportPostHandler.saveAutoResendRecord(publishBO.getIsAutoResend(), user.getId(), transportMain.getSrcMsgId());
        // 记录埋点数据
        biTrickingPostHandler.savePublishTricking(publishProcessBO, publishBO);
        // 记录发货记录
        transportPublishLogPostHandler.handler(publishProcessBO);
        // 专车自动派单,发送mq
        specialtAutoAssignPostHandler.specialAssign(transportMain);
        // 货源发布后发送mq
        sendPublishMQPostHandler.handler(publishProcessBO);
        //首次由非相似货源变为相似货源push
        sameTransportPushPostHandler.handler(transportMain);
        //首次发货、手动编辑发布缓存记录
        priceChangeCachePostHandler.handler(publishProcessBO);
        //指派司机
        assignCarPostHandler.assignCar(publishProcessBO);
        // 代调货源发货后置处理
        dispatchTransportPoster.dealDispatchTransportInfo(publishBO);
    }

    /**
     * 组装返回值
     *
     * @param publishProcessBO
     */
    private TransportPublishVO buildReturnVO(PublishProcessBO publishProcessBO) {
        TransportDO transport = publishProcessBO.getTransport();
        TransportPublishVO transportPublishVO = new TransportPublishVO();
        BeanUtils.copyProperties(transport, transportPublishVO);
        transportPublishVO.setTsId(transport.getId());
        transportPublishVO.setHasDestDetail((StringUtils.isNotBlank(transport.getDestDetailAdd()) && !Objects.equals(transport.getDestDetailAdd(), transport.getDestArea())) ? 1 : 0);
        return transportPublishVO;

    }

    private void buildDistance(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        TransportMainDO oldMain = publishProcessBO.getOldMain();
        if (oldMain != null && Objects.equals(YesOrNoEnum.NO.getId(), publishBO.getDispatchTransport())){
            buildAddress(publishBO, oldMain);
        }else {
            buildAddress(publishBO);
        }
        try {
            if (publishBO.getDistance() == null || publishBO.getDistance().compareTo(BigDecimal.ZERO) <= 0) {
                NavigationQueryDTO navigationQueryDTO = new NavigationQueryDTO();
                navigationQueryDTO.setStartLongitude(publishBO.getStartLongitude());
                navigationQueryDTO.setStartLatitude(publishBO.getStartLatitude());
                navigationQueryDTO.setDestLongitude(publishBO.getDestLongitude());
                navigationQueryDTO.setDestLatitude(publishBO.getDestLatitude());
                navigationQueryDTO.setWeight(new BigDecimal(publishBO.getWeight()));
                NavigationResultVO navigationResultVO = navigationRpcService.navigationDistance(navigationQueryDTO);
                if (navigationResultVO != null && navigationResultVO.getDistance() != null) {
                    publishBO.setDistance(navigationResultVO.getDistance());
                }
            }
        } catch (Exception e) {
            log.error("补充距离异常", e);
        }
    }

    private void setExcellentGoodsType(PublishBO publishBO, UserRpcVO user, BaseParamDTO baseParam) {
        // 优车三挡价格，如果客户端没传，重新获取
        if (publishBO.getFixPriceMin() == null || publishBO.getFixPriceMax() == null || publishBO.getFixPriceFast() == null) {
            TransportCarryReq TransportCarryReq = buildCarryReq(publishBO, user);
            CarryPriceVO thPrice = thPriceService.getThPrice(TransportCarryReq);
            if (thPrice != null) {
                publishBO.setFixPriceMin(thPrice.getFixPriceMin());
                publishBO.setFixPriceMax(thPrice.getFixPriceMax());
                publishBO.setFixPriceFast(thPrice.getFixPriceFast());
                publishBO.setThMinPrice(thPrice.getThMinPrice());
                publishBO.setThMaxPrice(thPrice.getThMaxPrice());
            }
        }

        if (publishBO.getPublishGoodsType() != null) { // 新版本发货（该字段为新增字段）
            if (PublishGoodsTypeEnum.isExcellentGoods(publishBO.getPublishGoodsType())) {
                publishBO.setExcellentGoods(ExcellentGoodsEnums.EXCELLENT.getCode());
                publishBO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
            } else if (Objects.equals(publishBO.getPublishGoodsType(), PublishGoodsTypeEnum.SPECIAL_GOODS.getCode())) {
                publishBO.setExcellentGoods(ExcellentGoodsEnums.SPECIAL.getCode());
            } else {
                publishBO.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
            }
        } else { // 老版本发货
            if (ExcellentGoodsEnums.isNormal(publishBO.getExcellentGoods())) {
                // 普通发货映射有价映射为用户出价，无价映射为普通找车
                if (TransportUtil.hasPrice(publishBO.getPrice())) {
                    publishBO.setPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode());
                } else {
                    publishBO.setPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS.getCode());
                }
            } else if (ExcellentGoodsEnums.isSpecial(publishBO.getExcellentGoods())) {
                // 专车货源仍映射为专车货源
                publishBO.setPublishGoodsType(PublishGoodsTypeEnum.SPECIAL_GOODS.getCode());
            } else {
                // 根据优车定价金额映射极速优车、快速优车、特惠优车
                if (publishBO.getFixPriceMin() != null && publishBO.getFixPriceMax() != null && publishBO.getFixPriceFast() != null) {
                    PublishGoodsTypeEnum goodsTypeEnum = TransportUtil.judgeExcellentGoodsLevel(publishBO.getPrice(), publishBO.getFixPriceMin(), publishBO.getFixPriceMax(), publishBO.getFixPriceFast());
                    // 原价格低于特惠优车或无价，映射为特惠优车
                    goodsTypeEnum = goodsTypeEnum == null ? PublishGoodsTypeEnum.EXCELLENT_GOODS : goodsTypeEnum;
                    publishBO.setPublishGoodsType(goodsTypeEnum.getCode());
                } else {
                    // 若在新版未成功获取优车定价，有价=>用户出价，无价=>普通找车
                    if (TransportUtil.hasPrice(publishBO.getPrice())) {
                        publishBO.setPublishGoodsType(PublishGoodsTypeEnum.USER_PRICE_GOODS.getCode());
                    } else {
                        publishBO.setPublishGoodsType(PublishGoodsTypeEnum.NORMAL_GOODS.getCode());
                    }
                    publishBO.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
                    publishBO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
                }
            }
        }
    }

    private TransportCarryReq buildCarryReq(PublishBO publishBO, UserRpcVO user) {
        TransportCarryReq transportCarryBean = new TransportCarryReq();
        transportCarryBean.setStartProvince(publishBO.getStartProvinc());
        transportCarryBean.setStartCity(publishBO.getStartCity());
        transportCarryBean.setStartArea(publishBO.getStartArea());
        transportCarryBean.setDestProvince(publishBO.getDestProvinc());
        transportCarryBean.setDestCity(publishBO.getDestCity());
        transportCarryBean.setDestArea(publishBO.getDestArea());
        transportCarryBean.setGoodsName(publishBO.getTaskContent());
        transportCarryBean.setGoodsWeight(new BigDecimal(publishBO.getWeight()));
        transportCarryBean.setGoodsLength(publishBO.getLength());
        transportCarryBean.setGoodsWide(publishBO.getWide());
        transportCarryBean.setGoodsHigh(publishBO.getHigh());
        transportCarryBean.setExcellentGoods(publishBO.getExcellentGoods());
        transportCarryBean.setUserId(user.getId());
        transportCarryBean.setDistance(publishBO.getDistance() != null ? publishBO.getDistance() : BigDecimal.ZERO);
        transportCarryBean.setGoodTypeName(publishBO.getGoodTypeName());
        return transportCarryBean;
    }

    /**
     * 装卸货时间转换
     *
     * @param publishBO
     */
    private void convertLoadingTime(PublishBO publishBO) {
        if (publishBO.getLoadingTime() != null && publishBO.getLoadingTime().getTime() == 0) {
            publishBO.setLoadingTime(null);
        }
        if (publishBO.getBeginLoadingTime() != null && publishBO.getBeginLoadingTime().getTime() == 0) {
            publishBO.setBeginLoadingTime(null);
        }
        if (publishBO.getUnloadTime() != null && publishBO.getUnloadTime().getTime() == 0) {
            publishBO.setUnloadTime(null);
        }
        if (publishBO.getBeginUnloadTime() != null && publishBO.getBeginUnloadTime().getTime() == 0) {
            publishBO.setBeginUnloadTime(null);
        }

    }
    /**
     * 确定来源
     *
     * @param publishBO
     * @param user
     */
    private void confirmSourceType(PublishBO publishBO, UserRpcVO user) {

        // 新代调货源
        if (Objects.equals(publishBO.getSourceType(), SourceTypeEnum.NEW_DISPATCH.getCode())) {
            publishBO.setSourceType(SourceTypeEnum.NEW_DISPATCH.getCode());
            return;
        }
        // 运满满货源
        if (Objects.equals(publishBO.getSourceType(), SourceTypeEnum.YMM.getCode())) {
            publishBO.setSourceType(SourceTypeEnum.YMM.getCode());
            return;
        }

        // 如果在内部名单中，统一都为代调或宏信
        List<DispatchCompanyDO> companyDOList = dispatchCompanyService.selectByUserId(user.getId());
        if (CollUtil.isNotEmpty(companyDOList)) {
            SourceTypeEnum sourceTypeEnum = SourceTypeEnum.DISPATCH;
            DispatchCooperativeDO cooperative = dispatchCooperativeService.selectByName("宏信建发");
            if (Objects.nonNull(cooperative) && cooperative.getId().equals(publishBO.getCargoOwnerId())) {
                sourceTypeEnum = SourceTypeEnum.HONGXIN;
            }
            publishBO.setSourceType(sourceTypeEnum.getCode());
            return;
        }

        publishBO.setSourceType(SourceTypeEnum.NORMAL.getCode());

    }

    /**
     * 发货组装处理
     *
     * @param publishBO
     * @param user
     */
    private TransportMainDO createTransportMain(PublishBO publishBO, UserRpcVO user, TransportMainDO oldTransportMain, BaseParamDTO baseParam) {
        // 组装实体
        TransportMainDO transportMain = transportPublishService.buildTransportMain(publishBO, user, oldTransportMain, baseParam);
        // 时间数据
        transportMain.setTsOrderNo(oldTransportMain.getTsOrderNo());
        transportMain.setCtime(oldTransportMain.getCtime());
        transportMain.setReleaseTime(oldTransportMain.getReleaseTime());
        transportMain.setFirstPublishType(oldTransportMain.getPublishType());
        transportMain.setResend(oldTransportMain.getResend());
        transportMain.setResendCounts(oldTransportMain.getResend());
        transportMain.setStatus(1);
        //设置捂货时间
        if (Objects.equals(publishBO.getPriorityRecommend(), YesOrNoEnum.YES.getId())) {
            transportMain.setPriorityRecommendExpireTime(oldTransportMain.getPriorityRecommendExpireTime());
        }
        // 设置用户信用积分及等级
        ApiDataUserCreditInfoRpcVO userCreditInfo = userRemoteService.getUserCreditInfo(user.getId());
        transportMain.setTotalScore(Optional.ofNullable(userCreditInfo).map(ApiDataUserCreditInfoRpcVO::getTotalScore).orElse(BigDecimal.ZERO));
        transportMain.setRankLevel(Optional.ofNullable(userCreditInfo).map(ApiDataUserCreditInfoRpcVO::getRankLevel).orElse(1));

        // 用户成交单数
        UserSubRpcVO userSub = userRemoteService.getUserSubById(user.getId());
        transportMain.setTradeNum(Optional.ofNullable(userSub).map(UserSubRpcVO::getDealNum).orElse(0));

        return transportMain;
    }

    private void checkSeckillGoodsIsLock(Long srcMsgId){
        if(seckillGoodsTransportService.checkIsSeckillGoodsTransportAndIsLock(srcMsgId)){
            throw BusinessException.createException(GoodsErrorCode.SECKILL_TRANSPORT_IS_LOCK.getCode(), "司机正在抢单中，不可修改");
        }
    }

    private void buildAddress(PublishBO publishBO, TransportMainDO oldMain) {
        publishBO.setStartProvinc(oldMain.getStartProvinc());
        publishBO.setStartCity(oldMain.getStartCity());
        publishBO.setStartArea(oldMain.getStartArea());
        publishBO.setStartPoint(oldMain.getStartPoint());
        publishBO.setStartDetailAdd(oldMain.getStartDetailAdd());
        publishBO.setDestProvinc(oldMain.getDestProvinc());
        publishBO.setDestCity(oldMain.getDestCity());
        publishBO.setDestArea(oldMain.getDestArea());
        publishBO.setDestPoint(oldMain.getDestPoint());
        publishBO.setDestDetailAdd(oldMain.getDestDetailAdd());

        publishBO.setStartCoordX(oldMain.getStartCoordX());
        publishBO.setStartCoordY(oldMain.getStartCoordY());
        publishBO.setDestCoordX(oldMain.getDestCoordX());
        publishBO.setDestCoordY(oldMain.getDestCoordY());

        publishBO.setStartLatitude(oldMain.getStartLatitude());
        publishBO.setStartLongitude(oldMain.getStartLongitude());
        publishBO.setDestLatitude(oldMain.getDestLatitude());
        publishBO.setDestLongitude(oldMain.getDestLongitude());

    }

    private void buildAddress(PublishBO publishBO) {

        String startCity = publishBO.getStartCity();
        String startArea = publishBO.getStartArea();
        String destCity = publishBO.getDestCity();
        String destArea = publishBO.getDestArea();

        if (StringUtils.isBlank(startCity)) {
            startCity = startArea;
        }
        if (StringUtils.isBlank(destCity)) {
            destCity = destArea;
        }
        TytCityVo startCityVo = null;
        if (StringUtils.isNotBlank(startCity) && StringUtils.isNotBlank(startArea)) {
            startCityVo = tytCityRemoteService.getRegxByName(startCity, startArea, "3");
            if (startCityVo == null) {
                // 提级匹配，比如澳门等
                startArea = startCity;
                startCityVo = tytCityRemoteService.getRegxByName(startArea, startCity, "3");
            }
        }
        if (startCityVo == null && publishBO.getStartCoordX() != null && publishBO.getStartCoordY() != null) {
            String startPx = publishBO.getStartCoordX().stripTrailingZeros().toPlainString();
            String startPy = publishBO.getStartCoordY().stripTrailingZeros().toPlainString();
            startCityVo = tytCityRemoteService.getCityByXY(startPx, startPy, "3");
        }
        if (startCityVo != null) {
            publishBO.setStartProvinc(startCityVo.getProvinceName());
            publishBO.setStartCity(startCityVo.getCityName());
            publishBO.setStartArea(startCityVo.getAreaName());
            publishBO.setStartCoordX(new BigDecimal(startCityVo.getPx()));
            publishBO.setStartCoordY(new BigDecimal(startCityVo.getPy()));
        }
        TytCityVo destCityVo = null;
        if (StringUtils.isNotBlank(destCity) && StringUtils.isNotBlank(destArea)) {
            destCityVo = tytCityRemoteService.getRegxByName(destCity, destArea, "3");
            if (destCityVo == null) {
                // 提级匹配，比如澳门等
                destArea = destCity;
                destCityVo = tytCityRemoteService.getRegxByName(destArea, destCity, "3");
            }
        }
        if (destCityVo == null && publishBO.getDestCoordX() != null && publishBO.getDestCoordY() != null) {
            String destPx = publishBO.getDestCoordX().stripTrailingZeros().toPlainString();
            String destPy = publishBO.getDestCoordY().stripTrailingZeros().toPlainString();
            destCityVo = tytCityRemoteService.getCityByXY(destPx, destPy, "3");
        }
        if (destCityVo != null) {
            publishBO.setDestProvinc(destCityVo.getProvinceName());
            publishBO.setDestCity(destCityVo.getCityName());
            publishBO.setDestArea(destCityVo.getAreaName());
            publishBO.setDestCoordX(new BigDecimal(destCityVo.getPx()));
            publishBO.setDestCoordY(new BigDecimal(destCityVo.getPy()));
        }

        String startPoint = updateAddressPoint(publishBO.getStartProvinc(), publishBO.getStartCity(), publishBO.getStartArea());
        String destPoint = updateAddressPoint(publishBO.getDestProvinc(), publishBO.getDestCity(), publishBO.getDestArea());
        publishBO.setStartPoint(startPoint);
        publishBO.setDestPoint(destPoint);
    }

    private String updateAddressPoint(String province, String city, String area) {
        if (StringUtils.isNotBlank(city) && StringUtils.isNotBlank(area)) {
            if (city.contains(province)) {
                if (area.contains(city)) {
                    return area;
                } else {
                    return city + area;
                }
            } else {
                if (area.contains(city)) {
                    return province + area;
                } else {
                    return province + city + area;
                }
            }
        } else {
            if (StringUtils.isNotBlank(city)) {
                if (city.contains(province)) {
                    return city;
                } else {
                    return province + city;
                }
            } else if (StringUtils.isNotBlank(area)) {
                if (area.contains(province)) {
                    return area;
                } else {
                    return province + area;
                }
            } else {
                return province;
            }
        }
    }





}
