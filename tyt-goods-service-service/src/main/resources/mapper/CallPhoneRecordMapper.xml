<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.CallPhoneRecordMapper">

    <select id="getLatestCallRecord"
            resultType="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordDO">
        select *
        from call_phone_record
        where src_msg_id in
        <foreach collection="srcMsgIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc limit 1
    </select>

    <select id="getRecordBySrcMsgIdList" resultType="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordTransportCountDO">
        SELECT t.src_msg_id AS srcMsgId, COUNT(DISTINCT t.car_user_id) AS count FROM call_phone_record t
        WHERE t.source_type != 6 and t.src_msg_id IN
        <foreach collection="srcMsgIdList" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
        GROUP BY t.src_msg_id ORDER BY MAX(t.create_time) DESC;
    </select>

    <select id="getCallPhoneRecordBySrcMsgIdList" resultType="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordDO">
        SELECT *
        FROM call_phone_record
        WHERE src_msg_id IN
        <foreach collection="srcMsgIdList" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
    </select>

    <select id="getGoodsContactTimeByRoute"
            resultType="com.teyuntong.goods.service.service.biz.callphonerecord.bean.GoodsContactTimeDTO">
        select  tm.id as srcMsgId, TIMESTAMPDIFF(MINUTE ,tm.ctime, pr.create_time) + 1 as contactMinutes
        from tyt_transport_main tm
        left join call_phone_record pr on tm.id = pr.src_msg_id
        where tm.start_city = #{startCity}  and tm.dest_city = #{destCity} and tm.ctime > #{startTime}
              and pr.create_time is not null
        group by tm.id
    </select>

    <select id="getCallPhoneRecords" resultType="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordDO">
        SELECT * FROM (SELECT * FROM `call_phone_record` WHERE src_msg_id = #{srcMsgId} and source_type != 6 ORDER BY id DESC) AS a GROUP BY car_user_id
    </select>

    <select id="getCallPhoneRecordsRemark" resultType="java.lang.String">
        select remark from call_phone_record_remark where src_msg_id = #{srcMsgId} and car_user_id = #{carUserId} limit 1
    </select>

</mapper>