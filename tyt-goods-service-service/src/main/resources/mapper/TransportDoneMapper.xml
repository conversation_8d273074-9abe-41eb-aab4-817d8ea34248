<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportDoneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDoneDO">
        <id column="id" property="id" />
        <result column="ts_id" property="tsId" />
        <result column="user_id" property="userId" />
        <result column="nick_name" property="nickName" />
        <result column="cell_phone" property="cellPhone" />
        <result column="start_point" property="startPoint" />
        <result column="dest_point" property="destPoint" />
        <result column="task_content" property="taskContent" />
        <result column="ts_order_no" property="tsOrderNo" />
        <result column="publish_time" property="publishTime" />
        <result column="deal_price" property="dealPrice" />
        <result column="car_id" property="carId" />
        <result column="head_city" property="headCity" />
        <result column="head_no" property="headNo" />
        <result column="tail_city" property="tailCity" />
        <result column="tail_no" property="tailNo" />
        <result column="carry_user_id" property="carryUserId" />
        <result column="carry_cell_phone" property="carryCellPhone" />
        <result column="carry_name" property="carryName" />
        <result column="is_change_car" property="isChangeCar" />
        <result column="is_allow_location" property="isAllowLocation" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="excellent_goods" property="excellentGoods" />
        <result column="excellent_goods_two" property="excellentGoodsTwo" />
        <result column="machine_remark" property="machineRemark" />
        <result column="excellent_card_id" property="excellentCardId" />
        <result column="driver_driving" property="driverDriving" />
        <result column="load_cell_phone" property="loadCellPhone" />
        <result column="unload_cell_phone" property="unloadCellPhone" />
        <result column="cargo_owner_id" property="cargoOwnerId" />
        <result column="dispatch_done_status" property="dispatchDoneStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ts_id, user_id, nick_name, cell_phone, start_point, dest_point, task_content, ts_order_no, publish_time, deal_price, car_id, head_city, head_no, tail_city, tail_no, carry_user_id, carry_cell_phone, carry_name, is_change_car, is_allow_location, ctime, mtime, excellent_goods, excellent_goods_two, machine_remark, excellent_card_id, driver_driving, load_cell_phone, unload_cell_phone, cargo_owner_id
    </sql>

    <select id="getDoneList"
            resultType="com.teyuntong.goods.service.service.biz.transport.dto.TransportDoneListDTO">
        SELECT
            d.id,
            d.ts_id AS tsId,
            d.user_id AS userId,
            d.nick_name AS nickName,
            d.cell_phone AS cellPhone,
            d.start_point AS startPoint,
            d.dest_point AS destPoint,
            d.task_content AS taskContent,
            d.ts_order_no AS tsOrderNo,
            d.publish_time AS publishTime,
            d.car_id AS carId,
            d.head_city AS headCity,
            d.head_no AS headNo,
            d.tail_city AS tailCity,
            d.tail_no AS tailNo,
            d.carry_user_id AS carryUserId,
            d.carry_cell_phone AS carryCellPhone,
            m.refund_flag AS refundFlag,
            d.carry_name AS carryName,
            d.is_change_car AS isChangeCar,
            d.is_allow_location AS isAllowLocation,
            d.ctime AS ctime,
            d.mtime AS mtime,
            m.id AS srcMsgId,
            m.loading_time AS loadingTime,
            m.unload_time AS unloadTime,
            m.begin_loading_time AS beginLoadingTime,
            m.begin_unload_time AS beginUnloadTime,
            m.remark AS remark,
            m.price AS price,
            d.deal_price AS dealPrice,
            m.shunting_quantity AS shuntingQuantity,
            m.publish_type AS publishType,
            m.info_fee AS infoFee,
            m.label_json AS labelJson,
            m.excellent_goods AS excellentGoods,
            m.machine_remark AS machineRemark,
            m.excellent_card_id AS excellentCardId,
            m.start_latitude AS startLatitude,
            m.start_longitude AS startLongitude,
            m.dest_latitude AS destLatitude,
            m.dest_longitude AS destLongitude,
            m.start_detail_add as startDetailAdd,
            m.dest_detail_add as destDetailAdd,
            m.invoice_transport AS invoiceTransport,
            m.source_type AS sourceType,
            m.status AS goodStatus,
            d.dispatch_done_status AS dispatchDoneStatus
        FROM
            tyt_transport_done d
        LEFT JOIN
            tyt_transport_main m ON d.ts_id = m.id
        WHERE
            d.user_id = #{req.userId} AND d.ctime > #{startDate} AND m.is_delete = 0
            <if test="req.clientSign == 1">
                and m.excellent_goods != 1
                and source_type != 6
            </if>
            <if test="req.queryActionType == 1 and req.queryID > 0">
                and d.id > #{req.queryID}
            </if>
            <if test="req.queryActionType == 2">
                and d.id &lt; #{req.queryID}
            </if>
        order by d.ctime desc, d.id desc
        limit #{pageSize}
    </select>

    <select id="getByTsId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDoneDO">
        select * from tyt_transport_done where ts_id = #{tsId} limit 1
    </select>

</mapper>
