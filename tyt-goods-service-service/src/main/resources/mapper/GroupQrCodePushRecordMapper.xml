<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.qiwei.mybatis.mapper.GroupQrCodePushRecordMapper">

	<select id="countByGroupId" resultType="java.lang.Integer">
		select count(*)
		from tyt_group_qr_code_push_record
		where group_id = #{groupId} and create_time > current_date and source_type = 6
	</select>

	<select id="selectBySimilarityCodeList" resultType="java.lang.String">
		select similarity_code
		from tyt_group_qr_code_push_record
		where similarity_code in
		<foreach collection="similarityCodeList" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectBySrcMsgId"
	        resultType="com.teyuntong.goods.service.service.biz.qiwei.mybatis.entity.GroupQrCodePushRecordDO">
		select *
		from tyt_group_qr_code_push_record
		where src_msg_id = #{srcMsgId} and group_id = #{groupId}
		limit 1
	</select>
</mapper>
