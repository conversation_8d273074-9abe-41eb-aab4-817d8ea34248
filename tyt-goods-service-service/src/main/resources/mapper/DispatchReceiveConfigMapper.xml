<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.DispatchReceiveConfigMapper">

    <!-- 查询可接单的调度 -->
    <select id="getDispatchList"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchReceiveConfigDO">
        select * from tyt_dispatch_receive_config
        where dispatch_id in ( <foreach collection="dispatchIds" separator="," item="i">#{i}</foreach> )
        <if test="status != null">
            and status = #{status}
        </if>
    </select>

    <!-- 查询可接单的调度 -->
    <select id="getDispatch"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchReceiveConfigDO">
        select * from tyt_dispatch_receive_config
        where dispatch_id = #{dispatchId}
    </select>
</mapper>
