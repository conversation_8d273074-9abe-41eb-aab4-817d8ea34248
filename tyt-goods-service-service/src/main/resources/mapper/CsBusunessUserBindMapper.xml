<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.CsBusinessUserBindMapper" >

    <select id="getUserBindList" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.CsBusinessUserBindDO">
        SELECT `id` id,`name` name,`login_phone_no` loginPhoneNo,`real_name` realName FROM `cs_business_user_bind` WHERE role_id=#{roleId} AND is_valid=1
    </select>

    <select id="getTransportDepartmentValuableAllCsUser" resultType="com.teyuntong.goods.service.client.transport.vo.CsBusinessUserBindValuableVO">
        select cbub.id, cbub.login_phone_no as loginPhoneNo, cbub.real_name as realName, tcuvs.status
        from cs_business_user_bind cbub inner join tyt_cs_user_valuable_status tcuvs on cbub.id = tcuvs.cs_business_user_id
    where
            tcuvs.status = 1 and
            cbub.is_valid = 1 and FIND_IN_SET('3' ,cbub.cs_department_id) > 0
    </select>

    <select id="getCsUserOrderNum" resultType="com.teyuntong.goods.service.client.transport.vo.CsBusinessUserBindValuableVO">
        select count(1) as orderNum, market_follow_up_user_id as id
        from tyt_transport_valuable where market_follow_up_user_id in
        <foreach collection="csUserIdList" open="(" close=")" item="csUserId" separator=",">
            #{csUserId}
        </foreach>
        and create_time &lt;= #{endDate} and create_time &gt;= #{startDate}
        and market_follow_up_user_id is not null group by market_follow_up_user_id
    </select>

</mapper>