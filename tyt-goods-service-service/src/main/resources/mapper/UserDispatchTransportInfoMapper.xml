<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.UserDispatchTransportInfoMapper">

    <select id="getBySrcMsgId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.UserDispatchTransportInfoDO">
        select
        id,
        src_msg_id srcMsgId,
        user_info_fee userInfoFee,
        dispatch_id dispatchId,
        dispatch_concat_phone dispatchConcatPhone,
        dispatch_name dispatchName,
        dispatch_time dispatchTime,
        cancel_auth_time cancelAuthTime,
        owner_freight ownerFreight,
        give_goods_phone giveGoodsPhone,
        give_goods_name giveGoodsName,
        dispatch_deal_status dispatchDealStatus,
        dispatch_pub_time dispatchPubTime,
        create_time createTime,
        modify_time modifyTime,
        create_name createName,
        modify_name modifyName
        from tyt_user_dispatch_transport_info where src_msg_id = #{srcMsgId} order by id desc limit 1
    </select>

    <select id="selectBySrcMsgIdList"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.UserDispatchTransportInfoDO">
        select *
        from tyt_user_dispatch_transport_info
        where src_msg_id in
        <foreach collection="srcMsgIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="countTodayDispatchNum" resultType="com.teyuntong.goods.service.service.biz.transport.dto.DispatchTransportCountDTO">
        select
            dispatch_id,
            count(*) num
        from tyt_user_dispatch_transport_info
        where create_time > CURDATE()
        group by dispatch_id
    </select>

</mapper>
