<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.CallFeedbackLogMapper">

    <select id="getLastFeedback" resultType="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallFeedbackLogDO">
        select *
        from tyt_call_feedback_log
        where car_user_id = #{carUserId}
        and src_msg_id in ( <foreach collection="srcMsgIds" item="i" separator=","> #{i} </foreach> )
        and status = 1
        order by id desc
    </select>
</mapper>
