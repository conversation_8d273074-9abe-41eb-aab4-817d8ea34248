<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportMapper">

    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sort" property="sort" jdbcType="BIGINT"/>
        <result column="start_point" property="startPoint" jdbcType="VARCHAR"/>
        <result column="dest_point" property="destPoint" jdbcType="VARCHAR"/>
        <result column="task_content" property="taskContent" jdbcType="VARCHAR"/>
        <result column="tel" property="tel" jdbcType="VARCHAR"/>
        <result column="pub_time" property="pubTime" jdbcType="VARCHAR"/>
        <result column="pub_qq" property="pubQq" jdbcType="BIGINT"/>
        <result column="nick_name" property="nickName" jdbcType="VARCHAR"/>
        <result column="user_show_name" property="userShowName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="mtime" property="mtime" jdbcType="TIMESTAMP"/>
        <result column="upload_cellphone" property="uploadCellphone" jdbcType="VARCHAR"/>
        <result column="resend" property="resend" jdbcType="INTEGER"/>
        <result column="start_coord" property="startCoord" jdbcType="VARCHAR"/>
        <result column="dest_coord" property="destCoord" jdbcType="VARCHAR"/>
        <result column="plat_id" property="platId" jdbcType="INTEGER"/>
        <result column="verify_flag" property="verifyFlag" jdbcType="INTEGER"/>
        <result column="price" property="price" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="price_code" property="priceCode" jdbcType="VARCHAR"/>
        <result column="start_coord_x" property="startCoordXValue" jdbcType="INTEGER"/>
        <result column="start_coord_y" property="startCoordYValue" jdbcType="INTEGER"/>
        <result column="dest_coord_x" property="destCoordXValue" jdbcType="INTEGER"/>
        <result column="dest_coord_y" property="destCoordYValue" jdbcType="INTEGER"/>
        <result column="start_detail_add" property="startDetailAdd" jdbcType="VARCHAR"/>
        <result column="start_longitude" property="startLongitudeValue" jdbcType="INTEGER"/>
        <result column="start_latitude" property="startLatitudeValue" jdbcType="INTEGER"/>
        <result column="dest_detail_add" property="destDetailAdd" jdbcType="VARCHAR"/>
        <result column="dest_longitude" property="destLongitudeValue" jdbcType="INTEGER"/>
        <result column="dest_latitude" property="destLatitudeValue" jdbcType="INTEGER"/>
        <result column="pub_date" property="pubDate" jdbcType="TIMESTAMP"/>
        <result column="goods_code" property="goodsCode" jdbcType="VARCHAR"/>
        <result column="weight_code" property="weightCode" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="length" property="length" jdbcType="VARCHAR"/>
        <result column="wide" property="wide" jdbcType="VARCHAR"/>
        <result column="high" property="high" jdbcType="VARCHAR"/>
        <result column="is_superelevation" property="isSuperelevation" jdbcType="VARCHAR"/>
        <result column="linkman" property="linkman" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="distance" property="distanceValue" jdbcType="INTEGER"/>
        <result column="pub_goods_time" property="pubGoodsTime" jdbcType="TIMESTAMP"/>
        <result column="tel3" property="tel3" jdbcType="VARCHAR"/>
        <result column="tel4" property="tel4" jdbcType="VARCHAR"/>
        <result column="display_type" property="displayType" jdbcType="VARCHAR"/>
        <result column="hash_code" property="hashCode" jdbcType="VARCHAR"/>
        <result column="is_car" property="isCar" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="INTEGER"/>
        <result column="pc_old_content" property="pcOldContent" jdbcType="VARCHAR"/>
        <result column="resend_counts" property="resendCounts" jdbcType="INTEGER"/>
        <result column="verify_photo_sign" property="verifyPhotoSign" jdbcType="INTEGER"/>
        <result column="user_part" property="userPart" jdbcType="INTEGER"/>
        <result column="start_city" property="startCity" jdbcType="VARCHAR"/>
        <result column="src_msg_id" property="srcMsgId" jdbcType="BIGINT"/>
        <result column="start_provinc" property="startProvinc" jdbcType="VARCHAR"/>
        <result column="start_area" property="startArea" jdbcType="VARCHAR"/>
        <result column="dest_provinc" property="destProvinc" jdbcType="VARCHAR"/>
        <result column="dest_city" property="destCity" jdbcType="VARCHAR"/>
        <result column="dest_area" property="destArea" jdbcType="VARCHAR"/>
        <result column="client_version" property="clientVersion" jdbcType="VARCHAR"/>
        <result column="is_info_fee" property="isInfoFee" jdbcType="VARCHAR"/>
        <result column="info_status" property="infoStatus" jdbcType="VARCHAR"/>
        <result column="ts_order_no" property="tsOrderNo" jdbcType="VARCHAR"/>
        <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP"/>
        <result column="reg_time" property="regTime" jdbcType="TIMESTAMP"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="good_type_name" property="goodTypeName" jdbcType="VARCHAR"/>
        <result column="good_number" property="goodNumber" jdbcType="INTEGER"/>
        <result column="is_standard" property="isStandard" jdbcType="INTEGER"/>
        <result column="match_item_id" property="matchItemId" jdbcType="INTEGER"/>
        <result column="android_distance" property="androidDistanceValue" jdbcType="INTEGER"/>
        <result column="ios_distance" property="iosDistanceValue" jdbcType="INTEGER"/>
        <result column="is_display" property="isDisplay" jdbcType="INTEGER"/>
        <result column="refer_length" property="referLength" jdbcType="INTEGER"/>
        <result column="refer_width" property="referWidth" jdbcType="INTEGER"/>
        <result column="refer_height" property="referHeight" jdbcType="INTEGER"/>
        <result column="refer_weight" property="referWeight" jdbcType="INTEGER"/>
        <result column="change_time" property="changeTime" jdbcType="TIMESTAMP"/>
        <result column="car_length" property="carLength" jdbcType="VARCHAR"/>
        <result column="loading_time" property="loadingTime" jdbcType="TIMESTAMP"/>
        <result column="begin_unload_time" property="beginUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP"/>
        <result column="car_min_length" property="carMinLength" jdbcType="DECIMAL"/>
        <result column="car_max_length" property="carMaxLength" jdbcType="DECIMAL"/>
        <result column="car_type" property="carType" jdbcType="VARCHAR"/>
        <result column="begin_loading_time" property="beginLoadingTime" jdbcType="TIMESTAMP"/>
        <result column="car_style" property="carStyle" jdbcType="VARCHAR"/>
        <result column="work_plane_min_high" property="workPlaneMinHigh" jdbcType="DECIMAL"/>
        <result column="work_plane_max_high" property="workPlaneMaxHigh" jdbcType="DECIMAL"/>
        <result column="work_plane_min_length" property="workPlaneMinLength" jdbcType="DECIMAL"/>
        <result column="work_plane_max_length" property="workPlaneMaxLength" jdbcType="DECIMAL"/>
        <result column="climb" property="climb" jdbcType="VARCHAR"/>
        <result column="order_number" property="orderNumber" jdbcType="INTEGER"/>
        <result column="evaluate" property="evaluate" jdbcType="INTEGER"/>
        <result column="special_required" property="specialRequired" jdbcType="VARCHAR"/>
        <result column="similarity_code" property="similarityCode" jdbcType="VARCHAR"/>
        <result column="similarity_first_id" property="similarityFirstId" jdbcType="BIGINT"/>
        <result column="similarity_first_info" property="similarityFirstInfo" jdbcType="VARCHAR"/>
        <result column="tyre_exposed_flag" property="tyreExposedFlag" jdbcType="VARCHAR"/>
        <result column="car_length_labels" property="carLengthLabels" jdbcType="VARCHAR"/>
        <result column="shunting_quantity" property="shuntingQuantity" jdbcType="INTEGER"/>
        <result column="first_publish_type" property="firstPublishType" jdbcType="INTEGER"/>
        <result column="publish_type" property="publishType" jdbcType="INTEGER"/>
        <result column="info_fee" property="infoFee" jdbcType="DECIMAL"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
        <result column="exclusive_type" property="exclusiveType" jdbcType="INTEGER"/>
        <result column="total_score" property="totalScore" jdbcType="DECIMAL"/>
        <result column="rank_level" property="rankLevel" jdbcType="INTEGER"/>
        <result column="is_show" property="isShow" jdbcType="INTEGER"/>
        <result column="refund_flag" property="refundFlag" jdbcType="INTEGER"/>
        <result column="source_type" property="sourceType" jdbcType="INTEGER"/>
        <result column="trade_num" property="tradeNum" jdbcType="INTEGER"/>
        <result column="auth_name" property="authName" jdbcType="VARCHAR"/>
        <result column="label_json" property="labelJson" jdbcType="VARCHAR"/>
        <result column="guarantee_goods" property="guaranteeGoods" jdbcType="INTEGER"/>
        <result column="credit_retop" property="creditRetop" jdbcType="INTEGER"/>
        <result column="sort_type" property="sortType" jdbcType="INTEGER"/>
        <result column="priority_recommend_expire_time" property="priorityRecommendExpireTime" jdbcType="TIMESTAMP"/>
        <result column="excellent_goods" property="excellentGoods" jdbcType="INTEGER"/>
        <result column="excellent_goods_two" property="excellentGoodsTwo" jdbcType="INTEGER"/>
        <result column="driver_driving" property="driverDriving" jdbcType="INTEGER"/>
        <result column="load_cell_phone" property="loadCellPhone" jdbcType="VARCHAR"/>
        <result column="unload_cell_phone" property="unloadCellPhone" jdbcType="VARCHAR"/>
        <result column="cargo_owner_id" property="cargoOwnerId" jdbcType="BIGINT"/>
        <result column="tec_service_fee" property="tecServiceFee" jdbcType="DECIMAL"/>
        <result column="machine_remark" property="machineRemark" jdbcType="VARCHAR"/>
        <result column="excellent_card_id" property="excellentCardId" jdbcType="BIGINT"/>
        <result column="invoice_transport" property="invoiceTransport" jdbcType="INTEGER"/>
        <result column="additional_price" property="additionalPrice" jdbcType="VARCHAR"/>
        <result column="enterprise_tax_rate" property="enterpriseTaxRate" jdbcType="DECIMAL"/>
    </resultMap>


    <update id="updateCarAgreementAboutTransportSomeFieldBySrcMsgId"
            parameterType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO">
        update tyt_transport set
        <if test="startPoint !=null">
            start_point = #{startPoint},
        </if>
        <if test="destPoint !=null">
            dest_point = #{destPoint},
        </if>
        <if test="startProvinc !=null">
            start_provinc = #{startProvinc},
        </if>
        <if test="startCity !=null">
            start_city = #{startCity},
        </if>
        <if test="startArea !=null">
            start_area = #{startArea},
        </if>
        <if test="destProvinc !=null">
            dest_provinc = #{destProvinc},
        </if>
        <if test="destCity !=null">
            dest_city = #{destCity},
        </if>
        <if test="destArea !=null">
            dest_area = #{destArea},
        </if>
        <if test="startCoordXValue !=null">
            start_coord_x = #{startCoordXValue},
        </if>
        <if test="startCoordYValue !=null">
            start_coord_y = #{startCoordYValue},
        </if>
        <if test="destCoordXValue !=null">
            dest_coord_x = #{destCoordXValue},
        </if>
        <if test="destCoordYValue !=null">
            dest_coord_y = #{destCoordYValue},
        </if>
        <if test="startDetailAdd !=null">
            start_detail_add = #{startDetailAdd},
        </if>
        <if test="startLongitudeValue !=null">
            start_longitude = #{startLongitudeValue},
        </if>
        <if test="startLatitudeValue !=null">
            start_latitude = #{startLatitudeValue},
        </if>
        <if test="destDetailAdd !=null">
            dest_detail_add = #{destDetailAdd},
        </if>
        <if test="destLongitudeValue !=null">
            dest_longitude = #{destLongitudeValue},
        </if>
        <if test="destLatitudeValue !=null">
            dest_latitude = #{destLatitudeValue},
        </if>
        <if test="beginLoadingTime !=null">
            begin_loading_time = #{beginLoadingTime},
        </if>
        <if test="loadingTime !=null">
            loading_time = #{loadingTime},
        </if>
        <if test="beginUnloadTime !=null">
            begin_unload_time = #{beginUnloadTime},
        </if>
        <if test="unloadTime !=null">
            unload_time = #{unloadTime},
        </if>
        <if test="weight !=null">
            weight = #{weight},
        </if>
        <if test="length !=null">
            length = #{length},
        </if>
        <if test="wide !=null">
            wide = #{wide},
        </if>
        <if test="high !=null">
            high = #{high},
        </if>
        <if test="carLength !=null">
            car_length = #{carLength},
        </if>
        <if test="carType !=null">
            car_type = #{carType},
        </if>

        <if test="price !=null">
            price = #{price},
        </if>
        <if test="distance !=null">
            distance = #{distanceValue},
        </if>
        <if test="additionalPrice !=null">
            additional_price = #{additionalPrice},
        </if>
        <if test="enterpriseTaxRate !=null">
            enterprise_tax_rate = #{enterpriseTaxRate},
        </if>
        mtime = now()
        where src_msg_id = #{srcMsgId};
    </update>
    <update id="disableTransport">
        update tyt_transport
        set display_type = '0',
            status= 0,
            mtime=now()
        where src_msg_id = #{srcMsgId}
          and display_type = '1'
          and status = 1
    </update>
    <update id="noDisplay">
        update tyt_transport
        set display_type = '0',
            mtime=now()
        where src_msg_id = #{srcMsgId}
          and display_type = '1'
          and status = 1
    </update>

    <update id="saveTransportCreditExposure">
        UPDATE tyt_transport
        SET credit_retop = #{creditRetop}
        WHERE src_msg_id = #{srcMsgId}
          AND ctime > current_date()
          AND STATUS = 1
          AND is_delete = 0
    </update>
    <update id="invalidTransport">
        UPDATE tyt_transport
        SET status = 0,
            mtime  = now()
        WHERE id = #{id}
          and status = 1
    </update>

    <select id="getPublishingList" resultMap="BaseResultMap">
        select
        *
        from
        tyt_transport
        where
        display_type = 1 and source = 0 and is_delete = 0
        and status in(1,6) and ctime >= current_date()
        <if test="req.userId != null">
            and user_id = #{req.userId}
        </if>
        <if test="req.clientSign == 1">
            and excellent_goods != 1
            and source_type != 6
        </if>
        <if test="req.queryActionType == 1 and req.queryID > 0">
            and id > #{req.queryID}
        </if>
        <if test="req.queryActionType == 2">
            and id &lt; #{req.queryID}
        </if>
        order by ctime desc, id desc
        limit #{pageSize}
    </select>

    <select id="getPublishingNum" resultType="java.lang.Integer">
        SELECT count(*)
        from tyt_transport
        where user_id = #{userId}
          and status in (1,6)
          and ctime >= current_date()
    </select>

    <select id="getLastBySrcMygId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO">
        select *
        from tyt_transport
        where src_msg_id = #{srcMsgId}
          and status = 1
        order by id desc
        limit 1
    </select>

    <select id="countSimilarityTransport" resultType="integer">
        select count(*)
        from tyt_transport
        where similarity_code = #{similarityCode}
          and status = 1
          and src_msg_id != #{srcMsgId}
    </select>

    <select id="getSimilarityTransportHavePriceCount" resultType="int">
        select count(*)
        from tyt_transport
        where similarity_code = #{similarityCode}
          and status = 1
          and price is not null
          and price != ''
          and ctime &gt;= #{startDate}
          and ctime &lt; #{endDate}
    </select>

    <select id="getSimilarityTransportCountFilterTransport" resultType="int">
        select count(*)
        from tyt_transport
        where similarity_code = #{similarityCode}
          and status = 1
          and ctime &gt;= #{startDate}
          and ctime &lt; #{endDate}
          and src_msg_id != #{srcMsgId}
    </select>

    <select id="getMaxResendCount" resultType="integer">
        select max(resend_counts)
        from tyt_transport
        where src_msg_id = #{srcMsgId}
    </select>

    <select id="getLastBySrcMsgId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO">
        select *
        from tyt_transport
        where src_msg_id = #{srcMsgId}
        order by id desc
        limit 1
    </select>

    <!-- 返回货源可见的transportId -->
    <select id="getValidTsIdBySrcMsgId" resultType="long">
        select id
        from tyt_transport
        where src_msg_id = #{srcMsgId}
          and status = 1
    </select>

    <select id="getFirstBySimilarityCode"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO">
        select *
        from tyt_transport
        where similarity_code = #{similarityCode}
        order by id asc
        limit 1
    </select>


    <select id="getHistoryIdListBySrcMsgId" resultType="java.lang.Long">
        select id
        from tyt_transport
        where user_id = #{userId}
          and src_msg_id = #{srcMsgId}
          and display_type = 1
          and ctime &lt; #{startOfDay}
    </select>

    <update id="updateDisplayTypeBysrcMsgId">
        update tyt_transport
        set display_type = #{display}
        where src_msg_id = #{srcMsgId}
    </update>

    <update id="updateStatusByIds">
        update tyt_transport set status = #{status}, display_type = #{display}, mtime = now(), is_display = 1
        <if test="infoStatus != null and infoStatus != ''">
            , info_status = #{infoStatus}
        </if>
        where id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateLabelJsonBySrcMsgId">
        UPDATE tyt_transport
        SET label_json = #{labelJson},
            mtime      = NOW()
        WHERE src_msg_id = #{srcMsgId}
          and status = 1
    </update>
    <update id="updateTransport">
        UPDATE tyt.tyt_transport
        SET sort                           = #{transport.sort},
            start_point                    = #{transport.startPoint},
            dest_point                     = #{transport.destPoint},
            task_content                   = #{transport.taskContent},
            tel                            = #{transport.tel},
            pub_time                       = #{transport.pubTime},
            pub_qq                         = #{transport.pubQq},
            nick_name                      = #{transport.nickName},
            user_show_name                 = #{transport.userShowName},
            status                         = #{transport.status},
            source                         = #{transport.source},
            ctime                          = #{transport.ctime},
            mtime                          = #{transport.mtime},
            upload_cellphone               = #{transport.uploadCellphone},
            resend                         = #{transport.resend},
            start_coord                    = #{transport.startCoord},
            dest_coord                     = #{transport.destCoord},
            plat_id                        = #{transport.platId},
            verify_flag                    = #{transport.verifyFlag},
            price                          = #{transport.price},
            user_id                        = #{transport.userId},
            price_code                     = #{transport.priceCode},
            start_coord_x                  = #{transport.startCoordXValue},
            start_coord_y                  = #{transport.startCoordYValue},
            dest_coord_x                   = #{transport.destCoordXValue},
            dest_coord_y                   = #{transport.destCoordYValue},
            start_detail_add               = #{transport.startDetailAdd},
            start_longitude                = #{transport.startLongitudeValue},
            start_latitude                 = #{transport.startLatitudeValue},
            dest_detail_add                = #{transport.destDetailAdd},
            dest_longitude                 = #{transport.destLongitudeValue},
            dest_latitude                  = #{transport.destLatitudeValue},
            pub_date                       = #{transport.pubDate},
            goods_code                     = #{transport.goodsCode},
            weight_code                    = #{transport.weightCode},
            weight                         = #{transport.weight},
            `length`                       = #{transport.length},
            wide                           = #{transport.wide},
            high                           = #{transport.high},
            is_superelevation              = #{transport.isSuperelevation},
            linkman                        = #{transport.linkman},
            remark                         = #{transport.remark},
            distance                       = #{transport.distanceValue},
            pub_goods_time                 = #{transport.pubGoodsTime},
            tel3                           = #{transport.tel3},
            tel4                           = #{transport.tel4},
            display_type                   = #{transport.displayType},
            hash_code                      = #{transport.hashCode},
            is_car                         = #{transport.isCar},
            user_type                      = #{transport.userType},
            pc_old_content                 = #{transport.pcOldContent},
            resend_counts                  = #{transport.resendCounts},
            verify_photo_sign              = #{transport.verifyPhotoSign},
            user_part                      = #{transport.userPart},
            start_city                     = #{transport.startCity},
            src_msg_id                     = #{transport.srcMsgId},
            start_provinc                  = #{transport.startProvinc},
            start_area                     = #{transport.startArea},
            dest_provinc                   = #{transport.destProvinc},
            dest_city                      = #{transport.destCity},
            dest_area                      = #{transport.destArea},
            client_version                 = #{transport.clientVersion},
            is_info_fee                    = #{transport.isInfoFee},
            info_status                    = #{transport.infoStatus},
            ts_order_no                    = #{transport.tsOrderNo},
            release_time                   = #{transport.releaseTime},
            reg_time                       = #{transport.regTime},
            type                           = #{transport.type},
            brand                          = #{transport.brand},
            good_type_name                 = #{transport.goodTypeName},
            good_number                    = #{transport.goodNumber},
            is_standard                    = #{transport.isStandard},
            match_item_id                  = #{transport.matchItemId},
            android_distance               = #{transport.androidDistanceValue},
            ios_distance                   = #{transport.iosDistanceValue},
            is_display                     = #{transport.isDisplay},
            refer_length                   = #{transport.referLength},
            refer_width                    = #{transport.referWidth},
            refer_height                   = #{transport.referHeight},
            refer_weight                   = #{transport.referWeight},
            change_time                    = #{transport.changeTime},
            car_length                     = #{transport.carLength},
            loading_time                   = #{transport.loadingTime},
            begin_unload_time              = #{transport.beginUnloadTime},
            unload_time                    = #{transport.unloadTime},
            car_min_length                 = #{transport.carMinLength},
            car_max_length                 = #{transport.carMaxLength},
            car_type                       = #{transport.carType},
            begin_loading_time             = #{transport.beginLoadingTime},
            car_style                      = #{transport.carStyle},
            work_plane_min_high            = #{transport.workPlaneMinHigh},
            work_plane_max_high            = #{transport.workPlaneMaxHigh},
            work_plane_min_length          = #{transport.workPlaneMinLength},
            work_plane_max_length          = #{transport.workPlaneMaxLength},
            climb                          = #{transport.climb},
            order_number                   = #{transport.orderNumber},
            evaluate                       = #{transport.evaluate},
            special_required               = #{transport.specialRequired},
            similarity_code                = #{transport.similarityCode},
            similarity_first_id            = #{transport.similarityFirstId},
            similarity_first_info          = #{transport.similarityFirstInfo},
            tyre_exposed_flag              = #{transport.tyreExposedFlag},
            car_length_labels              = #{transport.carLengthLabels},
            shunting_quantity              = #{transport.shuntingQuantity},
            first_publish_type             = #{transport.firstPublishType},
            publish_type                   = #{transport.publishType},
            info_fee                       = #{transport.infoFee},
            is_delete                      = #{transport.isDelete},
            exclusive_type                 = #{transport.exclusiveType},
            total_score                    = #{transport.totalScore},
            rank_level                     = #{transport.rankLevel},
            is_show                        = #{transport.isShow},
            refund_flag                    = #{transport.refundFlag},
            source_type                    = #{transport.sourceType},
            trade_num                      = #{transport.tradeNum},
            auth_name                      = #{transport.authName},
            label_json                     = #{transport.labelJson},
            guarantee_goods                = #{transport.guaranteeGoods},
            credit_retop                   = #{transport.creditRetop},
            sort_type                      = #{transport.sortType},
            priority_recommend_expire_time = #{transport.priorityRecommendExpireTime},
            excellent_goods                = #{transport.excellentGoods},
            excellent_goods_two            = #{transport.excellentGoodsTwo},
            driver_driving                 = #{transport.driverDriving},
            load_cell_phone                = #{transport.loadCellPhone},
            unload_cell_phone              = #{transport.unloadCellPhone},
            cargo_owner_id                 = #{transport.cargoOwnerId},
            tec_service_fee                = #{transport.tecServiceFee},
            machine_remark                 = #{transport.machineRemark},
            excellent_card_id              = #{transport.excellentCardId},
            invoice_transport              = #{transport.invoiceTransport},
            additional_price               = #{transport.additionalPrice},
            enterprise_tax_rate            = #{transport.enterpriseTaxRate}
        WHERE id = #{transport.id}

    </update>

	<update id="updateTransportBySrcMsgId">
        update tyt_transport
        set source_type = #{sourceType},
            info_fee = #{userInfoFee}
        where src_msg_id = #{srcMsgId} and status = 1
    </update>

	<select id="getTopNSrcMsgId" resultType="java.lang.Long">
        select id
        from tyt_transport
        where ctime between #{date} and concat(#{date}, ' 23:39:59')
          and user_id = #{userId}
        order by id
        limit #{topN}
    </select>

    <select id="getTodayIdListBySrcMsgId" resultType="java.lang.Long">
        select id
        from tyt_transport
        where user_id = #{userId}
          and src_msg_id = #{srcMsgId}
          and status = #{status}
          and ctime >= CURDATE()
    </select>

    <select id="getTsIdBySrcMsgId" resultType="java.lang.Long">
        select id
        from tyt_transport
        where src_msg_id = #{srcMsgId}
            and status = 1
            order by id desc
        limit 1
    </select>

    <select id="getTodayIdListBySrcMsgIdForCancel" resultType="java.lang.Long">
        select id
        from tyt_transport
        where user_id = #{userId}
        and src_msg_id = #{srcMsgId}
        and status in (1,6)
        and ctime >= CURDATE()
    </select>
</mapper>
