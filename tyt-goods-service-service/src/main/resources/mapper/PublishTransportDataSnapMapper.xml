<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.PublishTransportDataSnapMapper">

    <!-- 查询用户出发地历史记录 -->
    <select id="selectStartLocationHistory" resultType="com.teyuntong.goods.service.client.transport.vo.PublishLocationHistoryVO">
        SELECT
            id,
            start_provinc as provinc, 
            start_city as city, 
            start_area as area, 
            start_latitude as latitude, 
            start_longitude as longitude, 
            start_coord_x as coordX, 
            start_coord_y as coordY, 
            start_detail_add as detailAdd, 
            start_point as point
        FROM tyt_publish_transport_start_data_snap
        WHERE user_id = #{userId} 
          AND create_time > #{startTime}
        ORDER BY create_time DESC limit 20
    </select>

    <!-- 查询用户目的地历史记录 -->
    <select id="selectDestLocationHistory" resultType="com.teyuntong.goods.service.client.transport.vo.PublishLocationHistoryVO">
        SELECT
            id,
            dest_provinc as provinc, 
            dest_city as city, 
            dest_area as area, 
            dest_latitude as latitude, 
            dest_longitude as longitude, 
            dest_coord_x as coordX, 
            dest_coord_y as coordY, 
            dest_detail_add as detailAdd, 
            dest_point as point
        FROM tyt_publish_transport_dest_data_snap
        WHERE user_id = #{userId} 
          AND create_time > #{startTime}
        ORDER BY create_time DESC limit 20
    </select>

    <!-- 查询用户货物历史记录 -->
    <select id="selectGoodsHistory" resultType="com.teyuntong.goods.service.client.transport.vo.PublishTaskContentHistoryVO">
        SELECT
            id,
            task_content as taskContent, 
            match_item_id as matchItemId, 
            type,
            brand, 
            good_type_name as goodTypeName, 
            weight, 
            length, 
            wide, 
            high
        FROM tyt_publish_transport_task_data_snap
        WHERE user_id = #{userId} 
          AND create_time > #{startTime}
        ORDER BY create_time DESC limit 20
    </select>

    <insert id="insertStart">
        replace into tyt_publish_transport_start_data_snap (src_msg_id, user_id, start_provinc, start_city
                                                           , start_area, start_detail_add, start_longitude
                                                           , start_latitude, start_coord_x, start_coord_y
                                                           , start_point, create_time, update_time)
        VALUE (#{srcMsgId}, #{userId}, #{startProvinc}, #{startCity}, #{startArea}, #{startDetailAdd}
                   , #{startLongitude}, #{startLatitude}, #{startCoordX}
               , #{startCoordY}, #{startPoint}, #{createTime}, #{createTime})
    </insert>

    <insert id="insertDest">
        replace into tyt_publish_transport_dest_data_snap (src_msg_id, user_id, dest_provinc, dest_city, dest_area
                                                          , dest_detail_add, dest_longitude, dest_latitude, dest_coord_x
                                                          , dest_coord_y, dest_point, create_time, update_time)
        VALUE (#{srcMsgId}, #{userId}, #{destProvinc}, #{destCity}, #{destArea}, #{destDetailAdd}
            , #{destLongitude}, #{destLatitude}, #{destCoordX}
            , #{destCoordY}, #{destPoint}, #{createTime}, #{createTime});
    </insert>

    <insert id="insertTask">
        replace into tyt_publish_transport_task_data_snap (src_msg_id, user_id, task_content, match_item_id, type, brand
                                                          , good_type_name, weight, length, wide, high, create_time, update_time)
        VALUE (#{srcMsgId}, #{userId}, #{taskContent}, #{matchItemId}, #{type}, #{brand}, #{goodTypeName}, #{weight}, #{length}, #{wide}, #{high}, #{createTime}, #{createTime});
    </insert>

    <delete id="deleteStart">
        delete from tyt_publish_transport_start_data_snap where id = #{id}
    </delete>

    <delete id="deleteDest">
        delete from tyt_publish_transport_dest_data_snap where id = #{id}
    </delete>

    <delete id="deleteTask">
        delete from tyt_publish_transport_task_data_snap where id = #{id}
    </delete>

    <delete id="deleteNeedlessStart">
        DELETE t
        FROM tyt_publish_transport_start_data_snap t
                 JOIN (
            /* 取该用户按 id DESC 的前20条中的最小 id = 临界位 */
            SELECT MIN(id) AS cutoff
            FROM (
                     SELECT id
                     FROM tyt_publish_transport_start_data_snap
                     WHERE user_id = #{userId}
                     ORDER BY id DESC
                     LIMIT 20
                 ) AS top20
        ) c
                      ON t.user_id = #{userId}
        WHERE t.id &lt; c.cutoff
    </delete>

    <delete id="deleteNeedlessDest">
        DELETE t
        FROM tyt_publish_transport_dest_data_snap t
                 JOIN (
            /* 取该用户按 id DESC 的前20条中的最小 id = 临界位 */
            SELECT MIN(id) AS cutoff
            FROM (
                     SELECT id
                     FROM tyt_publish_transport_dest_data_snap
                     WHERE user_id = #{userId}
                     ORDER BY id DESC
                     LIMIT 20
                 ) AS top20
        ) c
                      ON t.user_id = #{userId}
        WHERE t.id &lt; c.cutoff
    </delete>

    <delete id="deleteNeedlessTask">
        DELETE t
        FROM tyt_publish_transport_task_data_snap t
                 JOIN (
            /* 取该用户按 id DESC 的前20条中的最小 id = 临界位 */
            SELECT MIN(id) AS cutoff
            FROM (
                     SELECT id
                     FROM tyt_publish_transport_task_data_snap
                     WHERE user_id = #{userId}
                     ORDER BY id DESC
                     LIMIT 20
                 ) AS top20
        ) c
                      ON t.user_id = #{userId}
        WHERE t.id &lt; c.cutoff
    </delete>

</mapper>
