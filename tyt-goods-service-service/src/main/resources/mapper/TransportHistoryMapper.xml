<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportHistoryMapper">

    <select id="getGoodsOnlineEditTimes" resultType="int">
        select count(1) from tyt_transport_history where ctime>CURDATE() and src_msg_id = #{srcMsgId} and edit_type=5
    </select>

    <select id="getGoodsOnlineEditTimesBySrcMsgIds" resultType="com.teyuntong.goods.service.service.biz.transport.vo.OnlineEditTimesVO">
        select src_msg_id as srcMsgId, count(*) as onlineEditTimes from tyt_transport_history where ctime>CURDATE() and src_msg_id in
        <foreach item="item" index="index" collection="srcMsgIdList" separator="," open="(" close=")">
            #{item}
        </foreach>
        and edit_type=5
        group by src_msg_id
    </select>

</mapper>
