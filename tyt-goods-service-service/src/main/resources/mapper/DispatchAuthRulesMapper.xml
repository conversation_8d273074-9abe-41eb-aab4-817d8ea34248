<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.publish.mybatis.mapper.DispatchAuthRulesMapper">


    <select id="selectRules"
            resultType="com.teyuntong.goods.service.service.biz.publish.mybatis.entity.DispatchAuthRulesDO">
        select *
        from tyt_dispatch_auth_rules
        where enable = 1
        and daily_start &lt; #{publishTime}
        and daily_end >= #{publishTime}
        and((start_provinc like concat("%",#{startProvinc},"%") or start_provinc = '全国') or start_city like
        concat("%",#{startCity},"%"))
        <choose>
            <when test="userIdentity != null">
                and (FIND_IN_SET(#{userIdentity}, user_identity) or user_identity = '-1')
            </when>
            <otherwise>
                and user_identity = '-1'
            </otherwise>
        </choose>
        <choose>
            <when test="goodTransportLabel != null">
                and (FIND_IN_SET(#{goodTransportLabel}, good_transport_label) or good_transport_label = '-1')
            </when>
            <otherwise>
                and good_transport_label = '-1'
            </otherwise>
        </choose>
        <choose>
            <when test="publishType != null">
                and (publish_type = #{publishType} or publish_type = -1)
            </when>
            <otherwise>
                and publish_type = -1
            </otherwise>
        </choose>
        <choose>
            <when test="publishGoodsType != null">
                and (FIND_IN_SET(#{publishGoodsType}, publish_goods_type) or publish_goods_type = '-1')
            </when>
            <otherwise>
                and publish_goods_type = '-1'
            </otherwise>
        </choose>
        <choose>
            <when test="havePrice != null">
                and (have_price = #{havePrice} or have_price = -1)
            </when>
            <otherwise>
                and have_price = -1
            </otherwise>
        </choose>
        <choose>
            <when test="refundFlag != null">
                and (refund_flag = #{refundFlag} or refund_flag = -1)
            </when>
            <otherwise>
                and refund_flag = -1
            </otherwise>
        </choose>
        <choose>
            <when test="invoiceTransport != null">
                and (invoice_transport = #{invoiceTransport} or invoice_transport = -1)
            </when>
            <otherwise>
                and invoice_transport = -1
            </otherwise>
        </choose>
        <choose>
            <when test="distance != null">
                and (min_distance &lt;= #{distance} and max_distance >= #{distance})
            </when>
            <otherwise>
                and min_distance = -1 and max_distance = -1
            </otherwise>
        </choose>
        <choose>
            <when test="weight != null">
                and (min_weight &lt;= #{weight} and max_weight >= #{weight})
            </when>
            <otherwise>
                and min_weight = -1 and max_weight = -1
            </otherwise>
        </choose>
        order by id desc limit 1
    </select>
</mapper>
